[data-theme=dark], .app-theme-picker__picker[data-theme=dark] {
  --dark-theme-bg-black: hsl(240, 6%, 10%);
  --dark-theme-bg-dark: #222;
  --dark-theme-bg-medium: #2d2b2b;
  --dark-theme-bg-dark-grey: #333;
  --dark-theme-bg-medium-grey: #444;
  --dark-theme-bg-light-grey: #555;
  --dark-theme-bg-very-light-grey: #444;
  --background: hsl(240, 6%, 13%);
  --very-light-grey: var(--dark-theme-bg-very-light-grey);
  --light-grey: var(--dark-theme-bg-medium);
  --medium-grey: var(--dark-theme-bg-medium-grey);
  --accent-color: color(display-p3 0.43 0.757 1);
  /* Text */
  --text-color: rgb(250, 250, 255);
  --text-color-secondary: rgb(160, 160, 165);
  --call-to-action-text-color: var(--dark-theme-bg-medium);
  --call-to-action-link-color: var(--call-to-action-text-color);
  /* Buttons */
  --btn-primary-bg: #53c1ff;
  --btn-secondary-bg: var(--dark-theme-bg-dark);
  --btn-secondary-color: var(--btn-primary-color);
  --btn-link-bg--hover: var(--dark-theme-bg-dark-grey);
  --btn-link-bg--active: var(--dark-theme-bg-dark-grey);
  /* Header */
  --brand-link-color: var(--accent-color);
  --header-link-color: var(--text-color);
  --header-link-color-active: var(--text-color);
  --header-bg-classic: #000;
  --search-bg: #4d545d;
  --search-input-color: rgb(200, 200, 204);
  /* Breadcrumbs */
  --breadcrumbs-bar-background: hsl(240, 6%, 9%, 0.8);
  /* Command Palette */
  --command-palette-results-backdrop-filter: contrast(0.95) brightness(1.3) saturate(2) blur(30px);
  --command-palette-inset-shadow: inset 0 0 0 1px hsla(250, 10%, 90%, 0.035);
  /* Tooltips */
  --tooltip-background: hsl(240, 6%, 23%);
  --tooltip-backdrop-filter: contrast(0.6) saturate(4) brightness(0.4) blur(15px);
  --tooltip-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                        0 0 8px 2px rgba(0, 0, 30, 0.05),
                        0 0 1px 1px rgba(0, 0, 20, 0.025),
                        0 10px 20px rgba(0, 0, 20, 0.15);
  /* Dropdowns */
  --dropdown-backdrop-filter: contrast(0.85) saturate(2) brightness(0.7) blur(20px);
  --dropdown-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                         0 0 8px 2px rgba(0, 0, 30, 0.05),
                         0 0 1px 1px rgba(0, 0, 20, 0.025),
                         0 10px 20px rgba(0, 0, 20, 0.3);
  /* Dialogs */
  --dialog-backdrop-filter: contrast(0.85) saturate(2) brightness(0.7) blur(20px);
  --dialog-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                      0 0 8px 2px rgba(0, 0, 30, 0.05),
                      0 0 1px 1px rgba(0, 0, 20, 0.025),
                      0 10px 20px rgba(0, 0, 20, 0.3);
  /* Modals - for backwards compatibility only */
  --modal-backdrop-filter: contrast(0.85) saturate(2) brightness(0.7) blur(20px);
  --modal-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                      0 0 8px 2px rgba(0, 0, 30, 0.05),
                      0 0 1px 1px rgba(0, 0, 20, 0.025),
                      0 10px 20px rgba(0, 0, 20, 0.3);
  /* Table */
  --table-background: var(--card-background);
  --table-border-color: var(--card-border-color);
  /* Deprecated */
  --bigtable-header-bg: var(--dark-theme-bg-black);
  --bigtable-header-border-color: var(--dark-theme-bg-dark-grey);
  --bigtable-header-text-color: var(--text-color);
  --bigtable-row-border-color: var(--dark-theme-bg-dark-grey);
  --even-row-color: #020202;
  --table-striped-bg--hover: var(--dark-theme-bg-dark-grey);
  /* Pane widget */
  --pane-border-color: var(--dark-theme-bg-dark-grey);
  --pane-header-border-color: var(--dark-theme-bg-dark-grey);
  --pane-header-color: var(--dark-theme-bg-black);
  /* Tab bar */
  --tabs-background: var(--card-background);
  --tabs-item-background: transparent;
  --tabs-item-background--hover: rgba(255, 255, 255, 0.05);
  --tabs-item-background--active: rgba(255, 255, 255, 0.1);
  --tabs-item-background--selected: rgba(255, 255, 255, 0.05);
  --tabs-border-color: var(--card-border-color);
  /* Side panel */
  --panel-header-bg-color: var(--dark-theme-bg-black);
  --panel-border-color: rgb(50, 50, 55);
  --panel-border-color--hover: var(--dark-theme-bg-black);
  --task-link-bg-color--hover: var(--dark-theme-bg-medium-grey);
  /* Form */
  --input-color: hsl(240, 6%, 20%);
  --input-border: hsl(240, 6%, 40%);
  --input-border-hover: hsl(240, 6%, 50%);
  --focus-input-border: var(--accent-color);
  --focus-input-glow: color-mix(in srgb, var(--focus-input-border) 20%, transparent);
  --pre-background: var(--dark-theme-bg-black);
  --selection-color: color-mix(in srgb, var(--focus-input-border) 40%, transparent);
  --header-search-border: var(--dark-theme-bg-dark-grey);
  --input-hidden-password-bg-color: var(--input-color);
  /* Pop out menus */
  --menu-bg-color: var(--dark-theme-bg-medium);
  --menu-text-color: var(--text-color);
  --menu-selected-color: var(--dark-theme-bg-dark);
  --menu-box-shadow: none;
  /* Add form widget */
  --light-bg-color: var(--dark-theme-bg-medium);
  --light-bg-color--hover: var(--dark-theme-bg-dark);
  --bright-bg-color: var(--dark-theme-bg-dark-grey);
  --brightest-bg-color: var(--input-color);
  --add-item-btn-decorator-border-color: var(--dark-theme-bg-dark-grey);
  --add-item-categories-bg-color--hover: var(--dark-theme-bg-dark-grey);
  --add-item-btn-decorator-bg-color: var(--dark-theme-bg-dark-grey);
  --configure-job-bottom-sticker-bg-color: var(--dark-theme-bg-dark-grey);
  --configure-job-bottom-sticker-border-color: var(--dark-theme-bg-dark-grey);
  /* Plugin manager */
  --plugin-manager-bg-color-already-upgraded: var(--dark-theme-bg-medium);
  --plugin-manager-category-link-bg-color: #5d5b5b;
  --plugin-manager-category-link-bg-color--hover: var(--dark-theme-bg-dark-grey);
  --plugin-manager-category-link-border-color--hover: var(--dark-theme-bg-black);
  --plugin-manager-category-text-color: var(--text-color);
  --plugin-manager-category-link-color--hover: #a5a3a3;
  --plugin-manager-unavailable-bg-color: var(--dark-theme-bg-medium);
  --plugin-manager-unavailable-label-color: var(--text-color);
  /* Auto complete */
  --auto-complete-bg-color--prehighlight: var(--accent-color);
  /* Card */
  --card-background: hsl(240, 6%, 15%);
  --card-background--hover: hsl(240, 6%, 17.5%);
  --card-background--active: hsl(240, 6%, 20%);
  --card-border-color: hsl(240, 6%, 16.5%);
  --card-border-color--hover: var(--card-border-color);
  --card-border-color--active: var(--card-border-color);
  color-scheme: dark;
}
[data-theme=dark] .main-search__icon-trailing, .app-theme-picker__picker[data-theme=dark] .main-search__icon-trailing {
  --header-link-bg-classic-hover: var(--text-color);
}
[data-theme=dark] ::backdrop, .app-theme-picker__picker[data-theme=dark] ::backdrop {
  --command-palette-backdrop-background: radial-gradient(
      farthest-corner at 50% 30vh,
      rgba(0, 0, 0, 0.6),
      rgba(0, 0, 0, 0.5)
  );
}
[data-theme=dark] ::backdrop, .app-theme-picker__picker[data-theme=dark] ::backdrop {
  --dialog-backdrop-background: hsla(240, 6%, 3%, 0.9);
}
[data-theme=dark] ::backdrop, .app-theme-picker__picker[data-theme=dark] ::backdrop {
  --modal-backdrop-background: hsla(240, 6%, 3%, 0.9);
}

[data-theme=dark] {
  --blue: hsl(210, 100%, 55%);
  --light-blue: hsl(210, 100%, 77.5%);
  --dark-blue: hsl(210, 100%, 33%);
  --brown: hsl(30, 35%, 45%);
  --light-brown: hsl(30, 35%, 72.5%);
  --dark-brown: hsl(30, 35%, 27%);
  --cyan: hsl(200, 100%, 70%);
  --light-cyan: hsl(200, 100%, 85%);
  --dark-cyan: hsl(200, 100%, 42%);
  --green: hsl(135, 75%, 55%);
  --light-green: hsl(135, 75%, 77.5%);
  --dark-green: hsl(135, 75%, 33%);
  --indigo: hsl(250, 75%, 60%);
  --light-indigo: hsl(250, 75%, 80%);
  --dark-indigo: hsl(250, 75%, 36%);
  --orange: hsl(35, 100%, 50%);
  --light-orange: hsl(35, 100%, 75%);
  --dark-orange: hsl(35, 100%, 30%);
  --pink: hsl(350, 100%, 60%);
  --light-pink: hsl(350, 100%, 80%);
  --dark-pink: hsl(350, 100%, 36%);
  --purple: hsl(280, 85%, 60%);
  --light-purple: hsl(280, 85%, 80%);
  --dark-purple: hsl(280, 85%, 36%);
  --red: hsl(5, 100%, 60%);
  --light-red: hsl(5, 100%, 80%);
  --dark-red: hsl(5, 100%, 36%);
  --yellow: hsl(50, 100%, 55%);
  --light-yellow: hsl(50, 100%, 77.5%);
  --dark-yellow: hsl(50, 100%, 33%);
  --white: hsl(0, 0%, 100%);
  --black: hsl(240, 22%, 10%);
}
[data-theme=dark] #jenkins .cm-s-default {
  font-family: Consolas, Menlo, Monaco, "Lucida Console", "Liberation Mono", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Courier New", monospace, serif;
}
[data-theme=dark] #jenkins .cm-s-default.CodeMirror {
  background: #2B2B2B;
  color: #A9B7C6;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-meta {
  color: #BBB529;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-number {
  color: #6897BB;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-keyword {
  color: #CC7832;
  line-height: 1em;
  font-weight: bold;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-def {
  color: #A9B7C6;
  font-style: italic;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-variable {
  color: #A9B7C6;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-variable-2 {
  color: #A9B7C6;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-variable-3 {
  color: #9876AA;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-type {
  color: #AABBCC;
  font-weight: bold;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-property {
  color: #FFC66D;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-operator {
  color: #A9B7C6;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-string {
  color: #6A8759;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-string-2 {
  color: #6A8759;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-comment {
  color: #61A151;
  font-style: italic;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-link {
  color: #CC7832;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-atom {
  color: #CC7832;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-error {
  color: #BC3F3C;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-tag {
  color: #629755;
  font-weight: bold;
  font-style: italic;
  text-decoration: underline;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-attribute {
  color: #6897bb;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-qualifier {
  color: #6A8759;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-bracket {
  color: #A9B7C6;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-builtin {
  color: #FF9E59;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-special {
  color: #FF9E59;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-matchhighlight {
  color: #FFFFFF;
  background-color: rgba(50, 89, 48, 0.7);
  font-weight: normal;
}
[data-theme=dark] #jenkins .cm-s-default span.cm-searching {
  color: #FFFFFF;
  background-color: rgba(61, 115, 59, 0.7);
  font-weight: normal;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirror-cursor {
  border-left: 1px solid #A9B7C6;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirror-activeline-background {
  background: #323232;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirror-gutter {
  background: #313335;
  border-right: 1px solid #313335;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirror-guttermarker {
  color: #FFEE80;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirror-guttermarker-subtle {
  color: #D0D0D0;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirrir-linenumber {
  color: #606366;
}
[data-theme=dark] #jenkins .cm-s-default .CodeMirror-matchingbracket {
  background-color: #3B514D;
  color: #FFEF28 !important;
  font-weight: bold;
}
[data-theme=dark] #jenkins .cm-s-default div.CodeMirror-selected {
  background: #214283;
}
[data-theme=dark] #jenkins .CodeMirror-hints.default {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  color: #9C9E9E;
  background-color: #3B3E3F !important;
}
[data-theme=dark] #jenkins .CodeMirror-hints.default .CodeMirror-hint-active {
  background-color: #494D4E !important;
  color: #9C9E9E !important;
}

@media (prefers-color-scheme: dark) {
  [data-theme=dark-system], .app-theme-picker__picker[data-theme=dark-system] {
    --dark-theme-bg-black: hsl(240, 6%, 10%);
    --dark-theme-bg-dark: #222;
    --dark-theme-bg-medium: #2d2b2b;
    --dark-theme-bg-dark-grey: #333;
    --dark-theme-bg-medium-grey: #444;
    --dark-theme-bg-light-grey: #555;
    --dark-theme-bg-very-light-grey: #444;
    --background: hsl(240, 6%, 13%);
    --very-light-grey: var(--dark-theme-bg-very-light-grey);
    --light-grey: var(--dark-theme-bg-medium);
    --medium-grey: var(--dark-theme-bg-medium-grey);
    --accent-color: color(display-p3 0.43 0.757 1);
    /* Text */
    --text-color: rgb(250, 250, 255);
    --text-color-secondary: rgb(160, 160, 165);
    --call-to-action-text-color: var(--dark-theme-bg-medium);
    --call-to-action-link-color: var(--call-to-action-text-color);
    /* Buttons */
    --btn-primary-bg: #53c1ff;
    --btn-secondary-bg: var(--dark-theme-bg-dark);
    --btn-secondary-color: var(--btn-primary-color);
    --btn-link-bg--hover: var(--dark-theme-bg-dark-grey);
    --btn-link-bg--active: var(--dark-theme-bg-dark-grey);
    /* Header */
    --brand-link-color: var(--accent-color);
    --header-link-color: var(--text-color);
    --header-link-color-active: var(--text-color);
    --header-bg-classic: #000;
    --search-bg: #4d545d;
    --search-input-color: rgb(200, 200, 204);
    /* Breadcrumbs */
    --breadcrumbs-bar-background: hsl(240, 6%, 9%, 0.8);
    /* Command Palette */
    --command-palette-results-backdrop-filter: contrast(0.95) brightness(1.3) saturate(2) blur(30px);
    --command-palette-inset-shadow: inset 0 0 0 1px hsla(250, 10%, 90%, 0.035);
    /* Tooltips */
    --tooltip-background: hsl(240, 6%, 23%);
    --tooltip-backdrop-filter: contrast(0.6) saturate(4) brightness(0.4) blur(15px);
    --tooltip-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                          0 0 8px 2px rgba(0, 0, 30, 0.05),
                          0 0 1px 1px rgba(0, 0, 20, 0.025),
                          0 10px 20px rgba(0, 0, 20, 0.15);
    /* Dropdowns */
    --dropdown-backdrop-filter: contrast(0.85) saturate(2) brightness(0.7) blur(20px);
    --dropdown-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                           0 0 8px 2px rgba(0, 0, 30, 0.05),
                           0 0 1px 1px rgba(0, 0, 20, 0.025),
                           0 10px 20px rgba(0, 0, 20, 0.3);
    /* Dialogs */
    --dialog-backdrop-filter: contrast(0.85) saturate(2) brightness(0.7) blur(20px);
    --dialog-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                        0 0 8px 2px rgba(0, 0, 30, 0.05),
                        0 0 1px 1px rgba(0, 0, 20, 0.025),
                        0 10px 20px rgba(0, 0, 20, 0.3);
    /* Modals - for backwards compatibility only */
    --modal-backdrop-filter: contrast(0.85) saturate(2) brightness(0.7) blur(20px);
    --modal-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                        0 0 8px 2px rgba(0, 0, 30, 0.05),
                        0 0 1px 1px rgba(0, 0, 20, 0.025),
                        0 10px 20px rgba(0, 0, 20, 0.3);
    /* Table */
    --table-background: var(--card-background);
    --table-border-color: var(--card-border-color);
    /* Deprecated */
    --bigtable-header-bg: var(--dark-theme-bg-black);
    --bigtable-header-border-color: var(--dark-theme-bg-dark-grey);
    --bigtable-header-text-color: var(--text-color);
    --bigtable-row-border-color: var(--dark-theme-bg-dark-grey);
    --even-row-color: #020202;
    --table-striped-bg--hover: var(--dark-theme-bg-dark-grey);
    /* Pane widget */
    --pane-border-color: var(--dark-theme-bg-dark-grey);
    --pane-header-border-color: var(--dark-theme-bg-dark-grey);
    --pane-header-color: var(--dark-theme-bg-black);
    /* Tab bar */
    --tabs-background: var(--card-background);
    --tabs-item-background: transparent;
    --tabs-item-background--hover: rgba(255, 255, 255, 0.05);
    --tabs-item-background--active: rgba(255, 255, 255, 0.1);
    --tabs-item-background--selected: rgba(255, 255, 255, 0.05);
    --tabs-border-color: var(--card-border-color);
    /* Side panel */
    --panel-header-bg-color: var(--dark-theme-bg-black);
    --panel-border-color: rgb(50, 50, 55);
    --panel-border-color--hover: var(--dark-theme-bg-black);
    --task-link-bg-color--hover: var(--dark-theme-bg-medium-grey);
    /* Form */
    --input-color: hsl(240, 6%, 20%);
    --input-border: hsl(240, 6%, 40%);
    --input-border-hover: hsl(240, 6%, 50%);
    --focus-input-border: var(--accent-color);
    --focus-input-glow: color-mix(in srgb, var(--focus-input-border) 20%, transparent);
    --pre-background: var(--dark-theme-bg-black);
    --selection-color: color-mix(in srgb, var(--focus-input-border) 40%, transparent);
    --header-search-border: var(--dark-theme-bg-dark-grey);
    --input-hidden-password-bg-color: var(--input-color);
    /* Pop out menus */
    --menu-bg-color: var(--dark-theme-bg-medium);
    --menu-text-color: var(--text-color);
    --menu-selected-color: var(--dark-theme-bg-dark);
    --menu-box-shadow: none;
    /* Add form widget */
    --light-bg-color: var(--dark-theme-bg-medium);
    --light-bg-color--hover: var(--dark-theme-bg-dark);
    --bright-bg-color: var(--dark-theme-bg-dark-grey);
    --brightest-bg-color: var(--input-color);
    --add-item-btn-decorator-border-color: var(--dark-theme-bg-dark-grey);
    --add-item-categories-bg-color--hover: var(--dark-theme-bg-dark-grey);
    --add-item-btn-decorator-bg-color: var(--dark-theme-bg-dark-grey);
    --configure-job-bottom-sticker-bg-color: var(--dark-theme-bg-dark-grey);
    --configure-job-bottom-sticker-border-color: var(--dark-theme-bg-dark-grey);
    /* Plugin manager */
    --plugin-manager-bg-color-already-upgraded: var(--dark-theme-bg-medium);
    --plugin-manager-category-link-bg-color: #5d5b5b;
    --plugin-manager-category-link-bg-color--hover: var(--dark-theme-bg-dark-grey);
    --plugin-manager-category-link-border-color--hover: var(--dark-theme-bg-black);
    --plugin-manager-category-text-color: var(--text-color);
    --plugin-manager-category-link-color--hover: #a5a3a3;
    --plugin-manager-unavailable-bg-color: var(--dark-theme-bg-medium);
    --plugin-manager-unavailable-label-color: var(--text-color);
    /* Auto complete */
    --auto-complete-bg-color--prehighlight: var(--accent-color);
    /* Card */
    --card-background: hsl(240, 6%, 15%);
    --card-background--hover: hsl(240, 6%, 17.5%);
    --card-background--active: hsl(240, 6%, 20%);
    --card-border-color: hsl(240, 6%, 16.5%);
    --card-border-color--hover: var(--card-border-color);
    --card-border-color--active: var(--card-border-color);
    color-scheme: dark;
  }
  [data-theme=dark-system] .main-search__icon-trailing, .app-theme-picker__picker[data-theme=dark-system] .main-search__icon-trailing {
    --header-link-bg-classic-hover: var(--text-color);
  }
  [data-theme=dark-system] ::backdrop, .app-theme-picker__picker[data-theme=dark-system] ::backdrop {
    --command-palette-backdrop-background: radial-gradient(
        farthest-corner at 50% 30vh,
        rgba(0, 0, 0, 0.6),
        rgba(0, 0, 0, 0.5)
    );
  }
  [data-theme=dark-system] ::backdrop, .app-theme-picker__picker[data-theme=dark-system] ::backdrop {
    --dialog-backdrop-background: hsla(240, 6%, 3%, 0.9);
  }
  [data-theme=dark-system] ::backdrop, .app-theme-picker__picker[data-theme=dark-system] ::backdrop {
    --modal-backdrop-background: hsla(240, 6%, 3%, 0.9);
  }
  [data-theme=dark-system] {
    --blue: hsl(210, 100%, 55%);
    --light-blue: hsl(210, 100%, 77.5%);
    --dark-blue: hsl(210, 100%, 33%);
    --brown: hsl(30, 35%, 45%);
    --light-brown: hsl(30, 35%, 72.5%);
    --dark-brown: hsl(30, 35%, 27%);
    --cyan: hsl(200, 100%, 70%);
    --light-cyan: hsl(200, 100%, 85%);
    --dark-cyan: hsl(200, 100%, 42%);
    --green: hsl(135, 75%, 55%);
    --light-green: hsl(135, 75%, 77.5%);
    --dark-green: hsl(135, 75%, 33%);
    --indigo: hsl(250, 75%, 60%);
    --light-indigo: hsl(250, 75%, 80%);
    --dark-indigo: hsl(250, 75%, 36%);
    --orange: hsl(35, 100%, 50%);
    --light-orange: hsl(35, 100%, 75%);
    --dark-orange: hsl(35, 100%, 30%);
    --pink: hsl(350, 100%, 60%);
    --light-pink: hsl(350, 100%, 80%);
    --dark-pink: hsl(350, 100%, 36%);
    --purple: hsl(280, 85%, 60%);
    --light-purple: hsl(280, 85%, 80%);
    --dark-purple: hsl(280, 85%, 36%);
    --red: hsl(5, 100%, 60%);
    --light-red: hsl(5, 100%, 80%);
    --dark-red: hsl(5, 100%, 36%);
    --yellow: hsl(50, 100%, 55%);
    --light-yellow: hsl(50, 100%, 77.5%);
    --dark-yellow: hsl(50, 100%, 33%);
    --white: hsl(0, 0%, 100%);
    --black: hsl(240, 22%, 10%);
  }
  [data-theme=dark-system] #jenkins .cm-s-default {
    font-family: Consolas, Menlo, Monaco, "Lucida Console", "Liberation Mono", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Courier New", monospace, serif;
  }
  [data-theme=dark-system] #jenkins .cm-s-default.CodeMirror {
    background: #2B2B2B;
    color: #A9B7C6;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-meta {
    color: #BBB529;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-number {
    color: #6897BB;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-keyword {
    color: #CC7832;
    line-height: 1em;
    font-weight: bold;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-def {
    color: #A9B7C6;
    font-style: italic;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-variable {
    color: #A9B7C6;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-variable-2 {
    color: #A9B7C6;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-variable-3 {
    color: #9876AA;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-type {
    color: #AABBCC;
    font-weight: bold;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-property {
    color: #FFC66D;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-operator {
    color: #A9B7C6;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-string {
    color: #6A8759;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-string-2 {
    color: #6A8759;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-comment {
    color: #61A151;
    font-style: italic;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-link {
    color: #CC7832;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-atom {
    color: #CC7832;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-error {
    color: #BC3F3C;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-tag {
    color: #629755;
    font-weight: bold;
    font-style: italic;
    text-decoration: underline;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-attribute {
    color: #6897bb;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-qualifier {
    color: #6A8759;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-bracket {
    color: #A9B7C6;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-builtin {
    color: #FF9E59;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-special {
    color: #FF9E59;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-matchhighlight {
    color: #FFFFFF;
    background-color: rgba(50, 89, 48, 0.7);
    font-weight: normal;
  }
  [data-theme=dark-system] #jenkins .cm-s-default span.cm-searching {
    color: #FFFFFF;
    background-color: rgba(61, 115, 59, 0.7);
    font-weight: normal;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirror-cursor {
    border-left: 1px solid #A9B7C6;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirror-activeline-background {
    background: #323232;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirror-gutter {
    background: #313335;
    border-right: 1px solid #313335;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirror-guttermarker {
    color: #FFEE80;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirror-guttermarker-subtle {
    color: #D0D0D0;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirrir-linenumber {
    color: #606366;
  }
  [data-theme=dark-system] #jenkins .cm-s-default .CodeMirror-matchingbracket {
    background-color: #3B514D;
    color: #FFEF28 !important;
    font-weight: bold;
  }
  [data-theme=dark-system] #jenkins .cm-s-default div.CodeMirror-selected {
    background: #214283;
  }
  [data-theme=dark-system] #jenkins .CodeMirror-hints.default {
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
    color: #9C9E9E;
    background-color: #3B3E3F !important;
  }
  [data-theme=dark-system] #jenkins .CodeMirror-hints.default .CodeMirror-hint-active {
    background-color: #494D4E !important;
    color: #9C9E9E !important;
  }
}

/*# sourceMappingURL=theme.css.map */
