<div>
  Isto controla o n&#250;mero de constru&#231;&#245;es concorrentes que o
  Jenkins pode executar neste agente. Assim o valor afeta toda a carga de
  sistema que o Jenkins pode sustentar. Um bom valor para iniciar seria o
  n&#250;mero de processadores da m&#225;quina.

  <p>
    Aumentando este valor al&#233;m disso pode causar lentid&#227;o na
    constru&#231;&#227;o, mas tamb&#233;m pode aumentar a capacidade total,
    porque permite a CPU construir um projeto enquanto uma outra
    constru&#231;&#227;o est&#225; aguardando por opera&#231;&#227;o de E/S.
  </p>

  <p>
    Atribuir 0 a este valor &#233; &#250;til para remover tempor&#225;riamente
    um agente desabilitado do Jenkins sem perder outras informa&#231;&#245;es de
    configura&#231;&#227;o.
  </p>
</div>
