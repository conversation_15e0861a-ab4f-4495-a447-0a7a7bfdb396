<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>jquery3-api</artifactId>
  <version>3.7.1-3</version>
  <packaging>hpi</packaging>
  <name>JQuery3 API Plugin</name>
  <description>Provides jQuery 3.x for Jenkins plugins.</description>
  <url>https://github.com/jenkinsci/jquery3-api-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT license</name>
      <comments>All source code is under the MIT license.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>uhafner</id>
      <name><PERSON><PERSON><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/jquery3-api-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/jquery3-api-plugin.git</developerConnection>
    <tag>v3.7.1-3</tag>
    <url>https://github.com/jenkinsci/jquery3-api-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
