<div>
  If your configuration matrix needs additional axes, you can specify them here.
  <p>
  For example, let's say you are trying to run a test for your database application,
  and you need to test it with three databases: MySQL, PostgreSQL, and Oracle. Your
  build script is designed so that you can test with a particular database by doing
  <tt>ant -Ddatabase=mysql</tt>.
  <p>
  This is the concept of an axis. You can have a variable called "database",
  which takes three values. When you configure it, <PERSON> will run 3 builds, each with
  different values assigned to the "database" variable to exhaustively cover
  the configuration matrix.
  <p>
  Variables specified here are made available to the build as environment variables.
  In addition, for <PERSON><PERSON> and <PERSON><PERSON>, variables are also exposed as properties, as if
  <tt>-D<i>variableName</i>=<i>value</i></tt> are specified on the command line.
  <p>
  When multiple axes are specified, all of the possible combinations of axes are built
  exhaustively. Multiple values in labels and JDKs are treated in the same way. So
  if you specify jdk=[JDK5,JDK6], database=[mysql,postgresql,oracle], container=[jetty,tomcat],
  then each build will consists of 2x3x2=12 different sub-builds.
</div>