{"version": 3, "file": "sortable-drag-drop.js", "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE+E;AAE/EA,yFAAQ,CAACE,KAAK,CAAC,IAAID,yFAAU,CAAC,CAAC,CAAC;AAEhC,SAASE,wBAAwBA,CAACC,CAAC,EAAE;EACnC,IAAI,CAACA,CAAC,IAAI,CAACA,CAAC,CAACC,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IACjD,OAAO,KAAK;EACd;EAEA,IAAIN,yFAAQ,CAACI,CAAC,EAAE;IACdG,SAAS,EAAE,iBAAiB;IAC5BC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,gCAAgC;IAC5CC,WAAW,EAAE,iCAAiC;IAC9CC,aAAa,EAAE,IAAI;IAAE;IACrBC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACzB,MAAMC,YAAY,GAAGD,KAAK,CAACE,IAAI;MAC/B,MAAMC,MAAM,GAAGF,YAAY,CAACG,YAAY;MACxCH,YAAY,CAACI,KAAK,CAACF,MAAM,GAAG,GAAGA,MAAM,IAAI;IAC3C,CAAC;IACDG,UAAU,EAAE,SAAAA,CAAUN,KAAK,EAAE;MAC3BA,KAAK,CAACE,IAAI,CAACG,KAAK,CAACE,cAAc,CAAC,QAAQ,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,6BAA6BA,CAACnB,CAAC,EAAEoB,gBAAgB,EAAE;EACjE,IAAI,CAACpB,CAAC,IAAI,CAACA,CAAC,CAACC,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IACjD,OAAO,KAAK;EACd;EAEAN,QAAQ,CAACyB,MAAM,CAACrB,CAAC,EAAE;IACjBI,MAAM,EAAE,YAAY;IACpBkB,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,SAAAA,CAAUZ,KAAK,EAAE;MACzB,IAAIS,gBAAgB,EAAE;QACpBA,gBAAgB,CAACT,KAAK,CAAC;MACzB;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACAa,MAAM,CAACzB,wBAAwB,GAAGA,wBAAwB;;;;;;UC1D1D;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA;WACA;WACA,kBAAkB,qBAAqB;WACvC,oHAAoH,iDAAiD;WACrK;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC7BA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD,8CAA8C;;;;;WCA9C;;;;;WCAA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,8CAA8C;;WAE9C;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,iCAAiC,mCAAmC;WACpE;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;WClDA;;;;;UEAA;UACA;UACA;UACA,8EAA8E,mCAAmC;UACjH", "sources": ["webpack://jenkins-ui/./src/main/js/sortable-drag-drop.js", "webpack://jenkins-ui/webpack/bootstrap", "webpack://jenkins-ui/webpack/runtime/chunk loaded", "webpack://jenkins-ui/webpack/runtime/define property getters", "webpack://jenkins-ui/webpack/runtime/global", "webpack://jenkins-ui/webpack/runtime/hasOwnProperty shorthand", "webpack://jenkins-ui/webpack/runtime/runtimeId", "webpack://jenkins-ui/webpack/runtime/jsonp chunk loading", "webpack://jenkins-ui/webpack/runtime/nonce", "webpack://jenkins-ui/webpack/before-startup", "webpack://jenkins-ui/webpack/startup", "webpack://jenkins-ui/webpack/after-startup"], "sourcesContent": ["/**\n * This module provides drag & drop functionality used by certain components,\n * such as f:repeatable or f:hetero-list.\n *\n * It does so using the SortableJS library.\n *\n * NOTE: there is another Sortable class exposed to the window namespace, this\n * corresponds to the sortable.js file that deals with table sorting.\n */\n\nimport Sortable, { AutoScroll } from \"sortablejs/modular/sortable.core.esm.js\";\n\nSortable.mount(new AutoScroll());\n\nfunction registerSortableDragDrop(e) {\n  if (!e || !e.classList.contains(\"with-drag-drop\")) {\n    return false;\n  }\n\n  new Sortable(e, {\n    draggable: \".repeated-chunk\",\n    handle: \".dd-handle\",\n    ghostClass: \"repeated-chunk--sortable-ghost\",\n    chosenClass: \"repeated-chunk--sortable-chosen\",\n    forceFallback: true, // Do not use html5 drag & drop behaviour because it does not work with autoscroll\n    scroll: true,\n    bubbleScroll: true,\n    onChoose: function (event) {\n      const draggableDiv = event.item;\n      const height = draggableDiv.clientHeight;\n      draggableDiv.style.height = `${height}px`;\n    },\n    onUnchoose: function (event) {\n      event.item.style.removeProperty(\"height\");\n    },\n  });\n}\n\nexport function registerSortableTableDragDrop(e, onChangeFunction) {\n  if (!e || !e.classList.contains(\"with-drag-drop\")) {\n    return false;\n  }\n\n  Sortable.create(e, {\n    handle: \".dd-handle\",\n    items: \"tr\",\n    onChange: function (event) {\n      if (onChangeFunction) {\n        onChangeFunction(event);\n      }\n    },\n  });\n}\n\n/*\n * Expose the function to register drag & drop components to the window objects\n * so that other widgets can use it (repeatable, hetero-list)\n */\nwindow.registerSortableDragDrop = registerSortableDragDrop;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "__webpack_require__.j = 80;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t80: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkjenkins_ui\"] = self[\"webpackChunkjenkins_ui\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [96], function() { return __webpack_require__(5454); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["Sortable", "AutoScroll", "mount", "registerSortableDragDrop", "e", "classList", "contains", "draggable", "handle", "ghostClass", "chosenClass", "force<PERSON><PERSON><PERSON>", "scroll", "bubbleScroll", "onChoose", "event", "draggableDiv", "item", "height", "clientHeight", "style", "onUnchoose", "removeProperty", "registerSortableTableDragDrop", "onChangeFunction", "create", "items", "onChange", "window"], "sourceRoot": ""}