<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='build-timeout' version='1.38'><l:dependency name='Build Timeout' groupId='org.jenkins-ci.plugins' artifactId='build-timeout' version='1.38' url='https://github.com/jenkinsci/build-timeout-plugin'><l:description>To terminate a build if it is taking too long</l:description><l:license name='The MIT License (MIT)' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Byte Buddy (without dependencies)' groupId='net.bytebuddy' artifactId='byte-buddy' version='1.15.11' url='https://bytebuddy.net/byte-buddy'><l:description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>