<div>
  When an inbound agent is launched, it attempts to connect to a specific TCP
  port of <PERSON> to establish a communication channel. But some security
  sensitive network can prevent you from making this connection. This can also
  happen when <PERSON> runs behind a load balancer,
  <a
    href="https://www.jenkins.io/doc/book/system-administration/reverse-proxy-configuration-apache/"
  >
    apache reverse proxy
  </a>
  into
  <a href="https://en.wikipedia.org/wiki/Demilitarized_zone_(computing)">DMZ</a>
  , and so on.

  <p>
    This tunneling option allows you to route this connection to another
    host/port, and useful for those situations. The field can either take "
    <code>HOST:PORT</code>
    ", "
    <code>:PORT</code>
    ", or "
    <code>HOST:</code>
    ". In the first format, the agent will connect to the given TCP port on the
    given host, and assume that you've configured your network so that this port
    forwards the connection to <PERSON>’ agent TCP port.
  </p>

  <p>
    In the latter two formats, the default host name and port number (that is,
    the host name that <PERSON> runs, and the TCP port that <PERSON> opened) are
    used to augment the missing values. In particular the
    <code>HOST:</code>
    format is useful if the HTTP reverse proxy is used and <PERSON> actually runs
    on another system.
  </p>
</div>
