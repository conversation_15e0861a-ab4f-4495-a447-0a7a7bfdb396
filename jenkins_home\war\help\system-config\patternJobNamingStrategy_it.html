<div>
  Definisce un pattern (espressione regolare) per controllare se il nome del
  processo è valido o meno.
  <p>
    Eseguendo forzatamente il controllo sui processi esistenti sarà possibile
    imporre una convenzione per i nomi dei processi esistenti - ad esempio,
    anche nel caso in cui l'utente non modifichi il nome, sarà controllata la
    corrispondenza con il pattern fornito a ogni invio e non potranno essere
    eseguiti aggiornamenti fino all'avvenuta corrispondenza del nome.
    <br />
    <i>
      Quest'opzione non influenza l'esecuzione dei processi con nomi non
      conformi. Essa controlla semplicemente il processo di validazione durante
      il salvataggio delle configurazioni dei processi.
    </i>
  </p>
</div>
