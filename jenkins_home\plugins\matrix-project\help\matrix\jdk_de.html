<div>
  Geben Sie die JDK(s) an, mit denen die Unter-Builds durchgeführt werden sollen.
  Wenn kein JDK ausgewählt ist, wird das Default-JDK verwendet (kein explizites
  Setzen der Umgebungsvariable <tt>JAVA_HOME</tt>; es wird erwartet, dass die 
  Kommandozeilenanwendung <tt>java</tt> im Suchpfad <tt>PATH</tt> enthalten ist).
  Wenn mehrere JDKs ausgewählt sind, deckt die Konfigurationsmatrix alle diese
  JDKs ab.
  <p>
  Eine Mehrfachauswahl ist typischerweise dann nützlich, wenn dieser Job Tests
  beinhaltet und Sie diese Tests mit unterschiedlichen JDKs ausführen möchten.
  <p>
  Während eines Builds ist das aktuelle JDK als Achse <tt>jdk</tt> sichtbar.
  Im Hilfetext zu "Achsen" finden Sie weitere Informationen, wie Sie auf die
  Werte der Achsenvariablen zugreifen können.
</div>