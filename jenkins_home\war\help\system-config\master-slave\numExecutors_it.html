<div>
  Quest'opzione controlla il numero di compilazioni concorrenti che Jenkins può
  eseguire su quest'agente. In questo modo il valore influenza il carico di
  sistema globale in cui Jenkins si può imbattere. Un buon valore di partenza
  potrebbe essere il numero di processori.

  <p>
    L'aumento di questo valore oltre tale numero farà sì che ogni compilazione
    richieda più tempo, ma potrebbe aumentare il throughput complessivo in
    quanto consente alla CPU di compilare un progetto finché un'altra
    compilazione è in attesa del disco.
  </p>

  <p>
    Impostare questo valore a zero è utile per rimuovere temporaneamente un
    agente disabilitato da Jenkins senza perdere altre informazioni di
    configurazione.
  </p>
</div>
