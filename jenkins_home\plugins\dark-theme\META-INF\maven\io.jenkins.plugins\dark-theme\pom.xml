<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>dark-theme</artifactId>
  <version>524.vd675b_22b_30cb_</version>
  <packaging>hpi</packaging>
  <name>Dark Theme</name>
  <description>The Jenkins Plugins Parent POM Project</description>
  <url>https://github.com/jenkinsci/dark-theme-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>timja</id>
      <name><PERSON></name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/dark-theme-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/dark-theme-plugin.git</developerConnection>
    <tag>d675b22b30cbafd0eea137831a93a7ed1bb4d3d0</tag>
    <url>https://github.com/jenkinsci/dark-theme-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>theme-manager</artifactId>
      <version>262.vc57ee4a_eda_5d</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.60</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
