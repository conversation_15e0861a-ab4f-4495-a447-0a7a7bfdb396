<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>ldap</artifactId>
  <version>780.vcb_33c9a_e4332</version>
  <packaging>hpi</packaging>
  <name>LDAP Plugin</name>
  <description>The Jenkins Plugins Parent POM Project</description>
  <url>https://github.com/jenkinsci/ldap-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>The MIT license</name>
      <url>https://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/ldap-plugin.git</connection>
    <developerConnection>scm:git:https://github.com/jenkinsci/ldap-plugin.git</developerConnection>
    <tag>cb33c9ae43325f2b167312699d3292b4f35f5158</tag>
    <url>https://github.com/jenkinsci/ldap-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-web</artifactId>
      <version>6.3.4</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-jcl</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-ldap</artifactId>
      <version>6.3.4</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.micrometer</groupId>
          <artifactId>micrometer-observation</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>net.time4j</groupId>
      <artifactId>time4j-base</artifactId>
      <version>5.9.4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.time4j</groupId>
      <artifactId>time4j-tzdata</artifactId>
      <version>5.0-2025a</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>mailer</artifactId>
      <version>489.vd4b_25144138f</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.main</groupId>
      <artifactId>jenkins-core</artifactId>
      <version>2.479.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.4</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
