Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Java JSON Web Token (JJWT) Plugin
Specification-Version: 0.11
Implementation-Title: Java JSON Web Token (JJWT) Plugin
Implementation-Version: 0.11.5-120.v0268cf544b_89
Group-Id: io.jenkins.plugins
Artifact-Id: jjwt-api
Short-Name: jjwt-api
Long-Name: Java JSON Web Token (JJWT) Plugin
Url: https://github.com/jenkinsci/jjwt-api-plugin
Plugin-Version: 0.11.5-120.v0268cf544b_89
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: jackson2-api:2.17.0-379.v02de8ec9f64c
Plugin-Developers: 
Support-Dynamic-Loading: true
Plugin-License-Name: MIT License
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/jjwt-api-plug
 in.git
Plugin-ScmTag: 0268cf544b89011ac091e4176b97c555813aa8f2
Plugin-ScmUrl: https://github.com/jenkinsci/jjwt-api-plugin
Implementation-Build: 0268cf544b89011ac091e4176b97c555813aa8f2

