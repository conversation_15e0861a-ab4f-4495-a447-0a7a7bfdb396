<div>
  Променливите на средата дефинирани тук ще са достъпни до всички изграждания
  изпълнени на този компютър и ще имат превес над променливите на средата със
  същото
  <i>Име</i>
  , които са зададени в страницата
  <i>Настройки на системата</i>
  .
  <p>
    Чрез изразите
    <code>$NAME</code>
    или
    <code>${NAME}</code>
    (
    <code>%NAME%</code>
    под Windows) стойностите на тези променливи може да се използват в
    настройките на задачите за изграждане или процесите, които се стартират от
    тях.
  </p>

  <p>
    Jenkins поддържа и специалния запис
    <code>BASE+EXTRA</code>
    , който позволява добавянето на множество двойки ключ-стойност, които се
    добавят пред стойността на съществуваща променлива на средата.
  </p>

  <p>
    Например, ако имате машина с път
    <code>PATH=/usr/bin</code>
    , можете да добавите още директории, към него като дефинирате тук променлива
    на име
    <code>PATH+LOCAL_BIN</code>
    , чието съдържание е
    <code>/usr/local/bin</code>
    .
    <br />
    Като резултат
    <code>PATH=/usr/local/bin:/usr/bin</code>
    ще е изнесена към изгражданията на машината.
    <code>PATH+LOCAL_BIN=/usr/local/bin</code>
    също ще бъде изнесена.
    <br />
    Ако има много променливи за добавяне към основната променлива, те се добавят
    според лексикографската подредба на имената им.
  </p>

  <p>
    Ако стойността е празен низ или низ само от празни знаци, тя няма да се
    добави към средата, както и няма да предефинира или изтрива променлива на
    средата на машината.
  </p>
</div>
