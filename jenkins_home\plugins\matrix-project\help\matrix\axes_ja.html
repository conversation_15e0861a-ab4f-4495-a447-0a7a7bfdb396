<div>
  構成軸を追加する必要があれば、ここで指定します。
  <p>
  例えば、データベースアプリケーションのテストを実行し、
  MySQL、PostgreSQLおよびOracleの3つのデータベースでテストする必要があるとしましょう。
  <tt>ant -Ddatabase=mysql</tt>を実行して特定のデータベースでテストできるように、
  ビルドスクリプトがデザインされています。
  <p>
  これが、構成軸のコンセプトです。"database"という変数を使用し、その変数は3つの値をとります。
  構成軸を設定すると、Jenkinsは3つのビルドを起動します。それぞれのビルドは、構成マトリックスを全てカバーするように、
  "database"変数に異なる値が割り当てられます。
  <p>
  ここで指定した変数は、環境変数としてビルドで利用できます。さらに、
  コマンドラインで<tt>-D<i>variableName</i>=<i>value</i></tt>と指定したのと同様に、
  AntやMavenでプロパティでも利用できます。
  <p>
  複数の軸が指定されている場合、軸の取り得る全ての組み合わせが組み立てられます。
  ラベルやJDKに複数の値を設定した場合も同様に扱われます。つまり、jdk=[JDK5,JDK6]、
  database=[mysql,postgresql,oracle]およびcontainer=[jetty,tomcat]と指定した場合、
  各ビルドは、2x3x2=12個の異なるサブビルドで構成されます。
</div>