#!groovy

import jenkins.model.*
import hudson.security.*
import hudson.security.csrf.DefaultCrumbIssuer
import hudson.markup.RawHtmlMarkupFormatter
import hudson.model.*
import jenkins.security.s2m.AdminWhitelistRule
import hudson.slaves.*
import hudson.slaves.EnvironmentVariablesNodeProperty.Entry

def instance = Jenkins.getInstance()

// Create admin user
def hudsonRealm = new HudsonPrivateSecurityRealm(false)
hudsonRealm.createAccount("admin", "admin123")
instance.setSecurityRealm(hudsonRealm)

// Set authorization strategy
def strategy = new FullControlOnceLoggedInAuthorizationStrategy()
strategy.setAllowAnonymousRead(false)
instance.setAuthorizationStrategy(strategy)

// Enable CSRF protection
instance.setCrumbIssuer(new DefaultCrumbIssuer(true))

// Set markup formatter
instance.setMarkupFormatter(new RawHtmlMarkupFormatter(false))

// Configure agent protocols
instance.getDescriptor("jenkins.CLI").get().setEnabled(false)
instance.getDescriptor("jenkins.security.s2m.MasterKillSwitchConfiguration").setMasterKillSwitch(false)

// Configure built-in node to ensure it's properly set as Linux
def node = instance.getNode("")
if (node != null) {
    // Set node properties to explicitly indicate this is a Linux node
    def envVars = new EnvironmentVariablesNodeProperty([
        new Entry("SHELL", "/bin/bash"),
        new Entry("OS", "linux")
    ])
    
    def nodeProperties = node.getNodeProperties()
    nodeProperties.clear()
    nodeProperties.add(envVars)
    
    println "Built-in node configured as Linux"
}

// Create a pipeline job with correct configuration
def jobName = "CaseCashBack-Pipeline"
def jobXml = '''<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job">
  <description>CaseCashBack Pipeline - Linux Compatible</description>
  <keepDependencies>false</keepDependencies>
  <properties/>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition" plugin="workflow-cps">
    <scm class="hudson.plugins.git.GitSCM" plugin="git">
      <configVersion>2</configVersion>
      <userRemoteConfigs>
        <hudson.plugins.git.UserRemoteConfig>
          <url>https://github.com/CHTSaif/CaseCashBack.git</url>
        </hudson.plugins.git.UserRemoteConfig>
      </userRemoteConfigs>
      <branches>
        <hudson.plugins.git.BranchSpec>
          <name>*/main</name>
        </hudson.plugins.git.BranchSpec>
      </branches>
      <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
      <submoduleCfg class="empty-list"/>
      <extensions/>
    </scm>
    <scriptPath>Jenkinsfile</scriptPath>
    <lightweight>true</lightweight>
  </definition>
  <triggers/>
  <disabled>false</disabled>
</flow-definition>'''

try {
    def job = instance.createProjectFromXML(jobName, new ByteArrayInputStream(jobXml.getBytes()))
    println "Created pipeline job: ${jobName}"
} catch (Exception e) {
    println "Job creation failed or job already exists: ${e.message}"
}

// Save configuration
instance.save()

println "Jenkins initial configuration completed!"
println "Admin user created with username: admin, password: admin123"
println "Built-in node configured for Linux/Unix shell commands"
println "Pipeline job 'CaseCashBack-Pipeline' created"
println "Please change the default password after first login!"
