<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='git-client' version='6.1.3'><l:dependency name='Jenkins Git client plugin' groupId='org.jenkins-ci.plugins' artifactId='git-client' version='6.1.3' url='https://github.com/jenkinsci/git-client-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JGit - HTTP Server' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.http.server' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.http.server'><l:description>Git aware HTTP server implementation.</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='JGit - Apache sshd-based SSH support' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.ssh.apache' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.ssh.apache'><l:description>SSH support for JGit based on Apache MINA sshd</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='JGit - Apache httpclient based HTTP support' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.http.apache' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.http.apache'><l:description>Apache httpclient based HTTP support</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='SLF4J API Module' groupId='org.slf4j' artifactId='slf4j-api' version='2.0.16' url='http://www.slf4j.org'><l:description>The slf4j API</l:description><l:license name='MIT License' url='http://www.opensource.org/licenses/mit-license.php'/></l:dependency><l:dependency name='Apache Commons Lang' groupId='org.apache.commons' artifactId='commons-lang3' version='3.14.0' url='https://commons.apache.org/proper/commons-lang/'><l:description>Apache Commons Lang, a package of Java utility classes for the
  classes that are in java.lang's hierarchy, or are considered to be so
  standard as to justify existence in java.lang.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JGit - Core' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit'><l:description>Repository access and algorithms</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='JavaEWAH' groupId='com.googlecode.javaewah' artifactId='JavaEWAH' version='1.2.3' url='https://github.com/lemire/javaewah'><l:description>The bit array data structure is implemented in Java as the BitSet class. Unfortunately, this fails to scale without compression.
  JavaEWAH is a word-aligned compressed variant of the Java bitset class. It uses a 64-bit run-length encoding (RLE) compression scheme.
  The goal of word-aligned compression is not to achieve the best compression, but rather to improve query processing time. Hence, we try to save CPU cycles, maybe at the expense of storage. However, the EWAH scheme we implemented is always more efficient storage-wise than an uncompressed bitmap (implemented in Java as the BitSet class). Unlike some alternatives, javaewah does not rely on a patented scheme.</l:description><l:license name='Apache 2' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JGit - Large File Storage' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.lfs' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.lfs'><l:description>JGit Large File Storage (LFS) implementation.</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency></l:dependencies>