<div>
  您可以在這裡新增設定矩陣的軸線。

  <p>
  假如想要在 MySQL, PostgreSQL 及 Oracle 上測試開發的資料庫應用程式，
  您的建置 Script 應該就要能指定要測試的資料庫，例如 <code>ant -Ddatabase=mysql</code>。

  <p>
  這就是軸線的概念，您可以設計出帶三種值的 "database" 變數。
  設定好後，Jenkins 會執行三次建置，每一次都套不同的 "database" 變數值，完整涵蓋整個設定矩陣。

  <p>
  這裡定義的變數會變成建置過程中可以看到的環境變數。
  對 Ant 及 Maven 而言，變數同時會以屬性方式帶入，就等同在命令列加上
  <code>-D<i>variableName</i>=<i>value</i></code>。

  <p>
  如果設定多個軸線，軸線所有的排列組合都會被建置一次，標籤及 JDK 軸也一樣。
  所以如果您設定 jdk=[JDK5,JDK6], database=[mysql,postgresql,oracle], container=[jetty,tomcat]，
  那麼每次建置都會有 2 × 3 × 2 = 12 種不同的子建置。
</div>