{"version": 3, "file": "add-item.css", "mappings": "AAEA,gBAEE,YADA,eACA,CAEA,qBACE,aACA,sBACA,2BAMA,qEACE,kBAEA,4DACE,aAGF,gDACE,iBACA,mBAGF,gDACE,kBAOJ,kHACE,yBAEA,oCADA,eACA,CAGF,8GACE,aAIJ,sBAEE,mBADA,aAGA,cADA,uBAEA,aAEA,0BAGE,YAFA,kBACA,UACA,CAIJ,8BAGE,kBAKA,WADA,eADA,gBALA,YAIA,iBAKA,YANA,kBAKA,uCAPA,UAQA,CAEA,iCACE,yBAIJ,0BACE,sBACA,kJACE,CAOJ,0BACE,sBACA,+IACE,CAOJ,0BACE,sBACA,kJACE,CAOJ,0BACE,sBACA,kJACE,CAOJ,0BACE,sBACA,+IACE,CAOJ,sBAEE,gBADA,SACA,CAKE,8CACE,eAEA,iDACE,SACA,iBAGF,gDACE,eAIJ,4CACE,kCAMJ,2CACE,mBAEA,mDACE,aAMR,mCAEE,cAGA,gBAFA,SACA,UAEA,kBAGF,gBACE,aACA,sBACA,QAGE,iCACE,yCAIJ,mBC5HA,6DAMA,uBAEA,YAJA,8CACA,eAFA,gBAIA,aARA,kBAGA,+BADA,UD8HE,2BAIA,aAEA,gBADA,+BAFA,gBADA,oBADA,+DAKA,CC3HF,mDAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,0BACE,wCACA,qCAGF,yBACE,mCAGF,iCACE,aAQE,8NACE,+CAIJ,yCACE,uBACA,UAEA,gDACE,gDAGF,+CACE,sDAKF,sDACE,mDACA,oBAOF,yFACE,yBDmEJ,yBACE,gBAGF,yBAIE,wBACA,eAJA,cACA,8BACA,mCAEA,CAEA,+BAIE,aADA,UAFA,kBACA,QAEA,CAIJ,yBACE", "sources": ["webpack://jenkins-ui/./src/main/js/widgets/add/addform.scss", "webpack://jenkins-ui/./src/main/scss/abstracts/_mixins.scss"], "sourcesContent": ["@use \"../../../scss/abstracts/mixins\";\n\n#add-item-panel {\n  max-width: 850px;\n  margin: auto;\n\n  form {\n    display: flex;\n    flex-direction: column;\n    gap: var(--section-padding);\n  }\n\n  .item-copy {\n    position: relative;\n\n    .add-item-copy {\n      position: relative;\n\n      input[type=\"radio\"] {\n        display: none;\n      }\n\n      label {\n        line-height: 48px;\n        padding: 0 10px 0 0;\n      }\n\n      input {\n        font-size: inherit;\n      }\n    }\n  }\n\n  .add-item-name,\n  .add-item-copy {\n    .input-validation-message {\n      color: var(--error-color);\n      padding-top: 2px;\n      font-weight: var(--font-bold-weight);\n    }\n\n    .input-message-disabled {\n      display: none;\n    }\n  }\n\n  .icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 2.5rem;\n    width: 2.5rem;\n\n    img {\n      position: relative;\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  .default-icon {\n    height: 40px;\n    width: 40px;\n    border-radius: 50%;\n    text-align: center;\n    line-height: 36px;\n    font-weight: bold;\n    font-size: 125%;\n    color: #ffffff;\n    text-shadow: rgba(0, 0, 0, 0.25) 0 -1px 1px;\n    opacity: 0.75;\n\n    .b {\n      text-transform: lowercase;\n    }\n  }\n\n  .c-D33833 {\n    border: 1px solid #900;\n    box-shadow:\n      inset #900 0 0 1px,\n      inset #fff 0 0 0 1px,\n      0 5px 3px -2px rgba(0, 0, 0, 0.25),\n      inset -5px -5px 10px 2px #903,\n      inset -5px -10px 15px 14px #d33833;\n  }\n\n  .c-6D6B6D {\n    border: 1px solid #000;\n    box-shadow:\n      inset #333 0 0 1px,\n      inset #fff 0 0 0 1px,\n      0 5px 3px -2px rgba(0, 0, 0, 0.25),\n      inset -5px -5px 10px 2px #222,\n      inset -5px -10px 15px 14px #555;\n  }\n\n  .c-335061 {\n    border: 1px solid #356;\n    box-shadow:\n      inset #024 0 0 1px,\n      inset #fff 0 0 0 1px,\n      0 5px 3px -2px rgba(0, 0, 0, 0.25),\n      inset -5px -5px 10px 2px #012,\n      inset -5px -10px 15px 14px #335061;\n  }\n\n  .c-49728B {\n    border: 1px solid #036;\n    box-shadow:\n      inset #036 0 0 1px,\n      inset #fff 0 0 0 1px,\n      0 5px 3px -2px rgba(0, 0, 0, 0.25),\n      inset -5px -5px 10px 2px #036,\n      inset -5px -10px 15px 14px #49728b;\n  }\n\n  .c-6699CC {\n    border: 1px solid #6699cc;\n    box-shadow:\n      inset #003399 0 0 1px,\n      inset #ffffff 0 0 0 1px,\n      0 5px 3px -2px rgba(0, 0, 0, 0.25),\n      inset -5px -5px 10px 2px #336699,\n      inset -5px -10px 15px 14px #6699cc;\n  }\n\n  input {\n    width: 50%;\n    min-width: 300px;\n  }\n\n  .categories {\n    .category {\n      .header {\n        padding: 0 20px;\n\n        h2 {\n          margin: 0;\n          padding: 15px 0 0;\n        }\n\n        p {\n          margin-top: 5px;\n        }\n      }\n\n      .desc {\n        color: var(--text-color-secondary);\n      }\n    }\n  }\n\n  .categories.flat {\n    .category {\n      border-bottom: none;\n\n      .header {\n        display: none;\n      }\n    }\n  }\n}\n\n.j-item-options li,\n.j-item-options {\n  display: block;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  position: relative;\n}\n\n.j-item-options {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n\n  li.active {\n    &::before {\n      background: var(--item-background--hover);\n    }\n  }\n\n  li {\n    @include mixins.item($border: false);\n\n    -webkit-touch-callout: none;\n    user-select: none;\n    padding: 0.75rem 1rem;\n    min-height: 68px;\n    display: grid;\n    grid-template-columns: auto 1fr;\n    gap: 0.25rem 1rem;\n\n    .icon {\n      grid-row: span 2;\n    }\n\n    label {\n      display: block;\n      font-size: var(--font-size-sm);\n      font-weight: var(--font-bold-weight);\n      color: var(--text-color);\n      cursor: pointer;\n\n      input {\n        position: absolute;\n        top: 24px;\n        left: 25px;\n        display: none;\n      }\n    }\n\n    .desc {\n      grid-column: 2;\n    }\n  }\n}\n", "@mixin link {\n  text-decoration: var(--link-text-decoration);\n  font-weight: var(--link-font-weight);\n  text-underline-offset: 2px;\n  text-decoration-thickness: 2px;\n\n  &:link {\n    color: var(--link-color);\n  }\n\n  &:visited {\n    color: var(--link-visited-color);\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--link-color--hover);\n    text-decoration: var(--link-text-decoration--hover);\n  }\n\n  &:active {\n    color: var(--link-color--active);\n    text-decoration: var(--link-text-decoration--active);\n  }\n\n  @media (prefers-contrast: more) {\n    text-decoration: underline;\n\n    &:hover {\n      text-decoration-thickness: 3px;\n    }\n  }\n}\n\n@mixin link-dark {\n  text-decoration: var(--link-dark-text-decoration);\n  font-weight: var(--link-dark-font-weight);\n\n  &:link {\n    color: var(--link-dark-color);\n  }\n\n  &:visited {\n    color: var(--link-dark-visited-color);\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--link-dark-color--hover);\n    text-decoration: var(--link-dark-text-decoration--hover);\n  }\n\n  &:active {\n    color: var(--link-dark-color--active);\n    text-decoration: var(--link-dark-text-decoration--active);\n  }\n}\n\n@mixin item($border: true) {\n  position: relative;\n  appearance: none;\n  z-index: 0;\n  text-decoration: none !important;\n  font-weight: normal;\n  border-radius: var(--form-input-border-radius);\n  cursor: pointer;\n  background: transparent;\n  outline: none;\n  border: none;\n\n  &::before,\n  &::after {\n    position: absolute;\n    content: \"\";\n    inset: 0;\n    z-index: -1;\n    border-radius: inherit;\n    transition: var(--standard-transition);\n    pointer-events: none;\n  }\n\n  &::before {\n    background-color: var(--item-background);\n    border: var(--jenkins-border--subtle);\n  }\n\n  &::after {\n    box-shadow: 0 0 0 0.5rem transparent;\n  }\n\n  &:focus-visible {\n    outline: none;\n  }\n\n  &:not(:disabled) {\n    &:hover,\n    &:focus-visible,\n    &[aria-describedby],\n    &[aria-expanded=\"true\"] {\n      &::before {\n        background-color: var(--item-background--hover);\n      }\n    }\n\n    &:active {\n      outline: none !important;\n      z-index: 1;\n\n      &::before {\n        background-color: var(--item-background--active);\n      }\n\n      &::after {\n        box-shadow: 0 0 0 0.25rem var(--item-box-shadow--focus);\n      }\n    }\n\n    &:focus-visible {\n      &::after {\n        box-shadow: 0 0 0 0.2rem var(--text-color) !important;\n        opacity: 1 !important;\n      }\n    }\n  }\n\n  @if $border == false {\n    &:not(:hover, &:active, &:focus) {\n      &::before {\n        border-color: transparent;\n      }\n    }\n  }\n}\n"], "names": [], "sourceRoot": ""}