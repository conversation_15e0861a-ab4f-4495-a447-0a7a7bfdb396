<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='echarts-api' version='5.6.0-4'><l:dependency name='ECharts API Plugin' groupId='io.jenkins.plugins' artifactId='echarts-api' version='5.6.0-4' url='https://github.com/jenkinsci/echarts-api-plugin'><l:description>Provides ECharts for Jenkins plugins.</l:description><l:license name='MIT license' url=''/></l:dependency><l:dependency name='Byte Buddy (without dependencies)' groupId='net.bytebuddy' artifactId='byte-buddy' version='1.17.2' url='https://bytebuddy.net/byte-buddy'><l:description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>