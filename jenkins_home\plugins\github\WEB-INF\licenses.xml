<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='com.coravy.hudson.plugins.github' artifactId='github' version='1.43.0'><l:dependency name='GitHub plugin' groupId='com.coravy.hudson.plugins.github' artifactId='github' version='1.43.0' url='https://github.com/jenkinsci/github-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://www.opensource.org/licenses/mit-license.php'/></l:dependency><l:dependency name='SnakeYAML' groupId='org.yaml' artifactId='snakeyaml' version='2.3' url='https://bitbucket.org/snakeyaml/snakeyaml'><l:description>YAML 1.1 parser and emitter for Java</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='SnakeYAML API Plugin' groupId='io.jenkins.plugins' artifactId='snakeyaml-api' version='2.3-123.v13484c65210a_' url='https://github.com/jenkinsci/snakeyaml-api-plugin'><l:description>This plugin packages stock SnakeYAML library</l:description><l:license name='The MIT License (MIT)' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JSON Api Plugin' groupId='io.jenkins.plugins' artifactId='json-api' version='20250107-125.v28b_a_ffa_eb_f01' url='https://github.com/jenkinsci/json-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT No Attribution License' url='https://opensource.org/license/mit-0/'/></l:dependency><l:dependency name='Byte Buddy (without dependencies)' groupId='net.bytebuddy' artifactId='byte-buddy' version='1.15.11' url='https://bytebuddy.net/byte-buddy'><l:description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JSON in Java' groupId='org.json' artifactId='json' version='20250107' url='https://github.com/douglascrockford/JSON-java'><l:description>JSON is a light-weight, language independent, data interchange format.
        See http://www.JSON.org/

        The files in this package implement JSON encoders/decoders in Java.
        It also includes the capability to convert between JSON and XML, HTTP
        headers, Cookies, and CDL.

        This is a reference implementation. There are a large number of JSON packages
        in Java. Perhaps someday the Java community will standardize on one. Until
        then, choose carefully.</l:description><l:license name='Public Domain' url='https://github.com/stleary/JSON-java/blob/master/LICENSE'/></l:dependency></l:dependencies>