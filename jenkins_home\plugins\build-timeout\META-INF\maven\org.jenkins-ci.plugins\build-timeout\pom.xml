<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>build-timeout</artifactId>
  <version>1.38</version>
  <packaging>hpi</packaging>
  <name>Build Timeout</name>
  <description>To terminate a build if it is taking too long</description>
  <url>https://github.com/jenkinsci/build-timeout-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>https://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/build-timeout-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/build-timeout-plugin.git</developerConnection>
    <tag>build-timeout-1.38</tag>
    <url>https://github.com/jenkinsci/build-timeout-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>json-path-api</artifactId>
      <version>2.9.0-148.v22a_7ffe323ce</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>token-macro</artifactId>
      <version>444.v52de7e9c573d</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>naginator</artifactId>
      <version>1.496.v94260e77b_3f5</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.main</groupId>
      <artifactId>jenkins-core</artifactId>
      <version>2.479.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.4</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
