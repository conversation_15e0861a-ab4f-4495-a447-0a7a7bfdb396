<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='jaxb' version='2.3.9-133.vb_ec76a_73f706'><l:dependency name='JAXB plugin' groupId='io.jenkins.plugins' artifactId='jaxb' version='2.3.9-133.vb_ec76a_73f706' url='https://github.com/jenkinsci/jaxb-plugin'><l:description>Detached packaging for JAXB for more transparent Java 9+ compatibility</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Old JAXB Runtime' groupId='com.sun.xml.bind' artifactId='jaxb-impl' version='2.3.9' url='https://eclipse-ee4j.github.io/jaxb-ri/'><l:description>Old JAXB Runtime module. Contains sources required for runtime processing.</l:description><l:license name='Eclipse Distribution License - v 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='jaxb-api' groupId='javax.xml.bind' artifactId='jaxb-api' version='2.3.1' url='https://github.com/javaee/jaxb-spec/jaxb-api'><l:description>JAXB (JSR 222) API</l:description><l:license name='CDDL 1.1' url='https://oss.oracle.com/licenses/CDDL+GPL-1.1'/><l:license name='GPL2 w/ CPE' url='https://oss.oracle.com/licenses/CDDL+GPL-1.1'/></l:dependency></l:dependencies>