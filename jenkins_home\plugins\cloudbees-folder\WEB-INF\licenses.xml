<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='cloudbees-folder' version='6.1023.v4fcb_72152519'><l:dependency name='Folders Plugin' groupId='org.jenkins-ci.plugins' artifactId='cloudbees-folder' version='6.1023.v4fcb_72152519' url='https://github.com/jenkinsci/cloudbees-folder-plugin/'><l:description>This plugin allows users to create "folders" to organize jobs. Users can define custom taxonomies (like
    by project type, organization type etc). Folders are nestable and you can define views within folders. Maintained by CloudBees, Inc.</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>