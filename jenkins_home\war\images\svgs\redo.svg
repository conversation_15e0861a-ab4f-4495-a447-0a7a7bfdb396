<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:export-ydpi="90.000000"
   inkscape:export-xdpi="90.000000"
   inkscape:export-filename="/home/<USER>/Desktop/wi-fi.png"
   width="48px"
   height="48px"
   id="svg11300"
   sodipodi:version="0.32"
   inkscape:version="0.43+devel"
   sodipodi:docbase="/home/<USER>/cvs/freedesktop.org/tango-icon-theme/scalable/actions"
   sodipodi:docname="edit-redo.svg"
   inkscape:output_extension="org.inkscape.output.svg.inkscape">
  <defs
     id="defs3">
    <linearGradient
       inkscape:collect="always"
       id="linearGradient2240">
      <stop
         style="stop-color:#99b00b;stop-opacity:1;"
         offset="0"
         id="stop2242" />
      <stop
         style="stop-color:#99b00b;stop-opacity:0;"
         offset="1"
         id="stop2244" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient2232">
      <stop
         style="stop-color:#788600;stop-opacity:1;"
         offset="0"
         id="stop2234" />
      <stop
         style="stop-color:#788600;stop-opacity:0;"
         offset="1"
         id="stop2236" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4991">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop4993" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop4995" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient8662">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop8664" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop8666" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient8662"
       id="radialGradient8668"
       cx="24.837126"
       cy="36.421127"
       fx="24.837126"
       fy="36.421127"
       r="15.644737"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.536723,-5.825329e-14,16.87306)"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       id="linearGradient2187"
       inkscape:collect="always">
      <stop
         id="stop2189"
         offset="0"
         style="stop-color:#ffffff;stop-opacity:1;" />
      <stop
         id="stop2191"
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2187"
       id="linearGradient1764"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.813471e-16,-1.171926,1.171926,1.813471e-16,1.782801,54.10111)"
       x1="17.060806"
       y1="11.39502"
       x2="12.624337"
       y2="12.583769" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4991"
       id="radialGradient4997"
       cx="16.563837"
       cy="11.132236"
       fx="16.563837"
       fy="11.132236"
       r="19.0625"
       gradientTransform="matrix(-1.290127e-2,1.685197,1.713082,1.311475e-2,-1.041499,-10.11571)"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2232"
       id="linearGradient2238"
       x1="33"
       y1="35.75"
       x2="31.5"
       y2="42.5"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2240"
       id="linearGradient2246"
       x1="33"
       y1="35.75"
       x2="31.5"
       y2="42.5"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     stroke="#788600"
     fill="#99b00b"
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="0.25490196"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="4"
     inkscape:cx="53.000477"
     inkscape:cy="33.008096"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:showpageshadow="false"
     inkscape:window-width="892"
     inkscape:window-height="831"
     inkscape:window-x="368"
     inkscape:window-y="57" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://jimmac.musichall.cz</dc:source>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:title>Edit Redo</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>edit</rdf:li>
            <rdf:li>redo</rdf:li>
            <rdf:li>again</rdf:li>
            <rdf:li>reapply</rdf:li>
          </rdf:Bag>
        </dc:subject>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <path
       transform="matrix(1.489736,0.000000,0.000000,-1.001252,-12.64716,75.31260)"
       d="M 40.481863 36.421127 A 15.644737 8.3968935 0 1 1  9.1923885,36.421127 A 15.644737 8.3968935 0 1 1  40.481863 36.421127 z"
       sodipodi:ry="8.3968935"
       sodipodi:rx="15.644737"
       sodipodi:cy="36.421127"
       sodipodi:cx="24.837126"
       id="path8660"
       style="opacity:0.14117647;color:#000000;fill:url(#radialGradient8668);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       sodipodi:type="arc" />
    <path
       style="opacity:1;color:#000000;fill:url(#linearGradient2246);fill-opacity:1.0;fill-rule:nonzero;stroke:url(#linearGradient2238);stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:block;overflow:visible"
       d="M 38.37476,45.034369 C -1.6510486,46.355509 4.6747954,12.29355 25.49479,12.49765 L 25.49479,3.1222396 L 42.143271,17.708819 L 25.49479,33.006349 C 25.49479,33.006349 25.49479,23.337969 25.49479,23.337969 C 11.43168,22.751999 7.3172614,44.770549 38.37476,45.034369 z "
       id="path1432"
       sodipodi:nodetypes="ccccccc" />
    <path
       sodipodi:nodetypes="ccccccc"
       id="path2177"
       d="M 16.92492,39.315519 C 5.2018204,33.235892 8.7371274,13.087489 26.5085,13.549959 L 26.5085,5.4508678 C 26.5085,5.4508678 40.556238,17.714589 40.556238,17.714589 L 26.5085,30.658617 C 26.5085,30.658617 26.5085,22.380979 26.5085,22.380979 C 11.66865,22.032709 12.34859,35.138579 16.92492,39.315519 z "
       style="opacity:0.69886361;color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:url(#linearGradient1764);stroke-width:0.9999997;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:block;overflow:visible" />
    <path
       style="opacity:0.49431817;color:#000000;fill:url(#radialGradient4997);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.9999997;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       d="M 26.036989,4.5686095 L 36.723727,14.798241 C 29.786227,14.79824 32.036989,23.735424 25.911989,26.610424 L 25.974489,22.943609 C 10.786989,22.881109 11.661989,38.443609 22.724489,42.693609 C 3.6363414,37.811681 6.2869904,13.381109 25.911989,12.88111 L 26.036989,4.5686095 z "
       id="path4989"
       sodipodi:nodetypes="ccccccc" />
  </g>
</svg>
