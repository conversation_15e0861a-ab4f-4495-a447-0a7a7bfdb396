<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='matrix-auth' version='3.2.6'><l:dependency name='Matrix Authorization Strategy Plugin' groupId='org.jenkins-ci.plugins' artifactId='matrix-auth' version='3.2.6' url='https://github.com/jenkinsci/matrix-auth-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='asm-tree' groupId='org.ow2.asm' artifactId='asm-tree' version='9.7.1' url='http://asm.ow2.io/'><l:description>Tree API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='SCM API Plugin' groupId='org.jenkins-ci.plugins' artifactId='scm-api' version='698.v8e3b_c788f0a_6' url='https://github.com/jenkinsci/scm-api-plugin/blob/master/docs/consumer.adoc'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JBoss Marshalling River' groupId='org.jboss.marshalling' artifactId='jboss-marshalling-river' version='2.2.1.Final' url='http://www.jboss.org'><l:description>JBoss Marshalling River Implementation</l:description><l:license name='Apache License 2.0' url='http://repository.jboss.org/licenses/apache-2.0.txt'/></l:dependency><l:dependency name='Structs Plugin' groupId='org.jenkins-ci.plugins' artifactId='structs' version='338.v848422169819' url='https://github.com/jenkinsci/structs-plugin'><l:description>Library plugin for DSL plugins that need names for Jenkins objects.</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='asm-util' groupId='org.ow2.asm' artifactId='asm-util' version='9.7.1' url='http://asm.ow2.io/'><l:description>Utilities for ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='ASM API Plugin' groupId='io.jenkins.plugins' artifactId='asm-api' version='9.7.1-97.v4cc844130d97' url='https://github.com/jenkinsci/asm-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/license/mit/'/></l:dependency><l:dependency name='JBoss Marshalling API' groupId='org.jboss.marshalling' artifactId='jboss-marshalling' version='2.2.1.Final' url='http://www.jboss.org'><l:description>JBoss Marshalling API</l:description><l:license name='Apache License 2.0' url='http://repository.jboss.org/licenses/apache-2.0.txt'/></l:dependency><l:dependency name='asm' groupId='org.ow2.asm' artifactId='asm' version='9.7.1' url='http://asm.ow2.io/'><l:description>ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Groovy Sandbox' groupId='org.kohsuke' artifactId='groovy-sandbox' version='1.34' url='https://github.com/jenkinsci/groovy-sandbox'><l:description>Executes untrusted Groovy script safely</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='asm-commons' groupId='org.ow2.asm' artifactId='asm-commons' version='9.7.1' url='http://asm.ow2.io/'><l:description>Usefull class adapters based on ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Pipeline: Supporting APIs' groupId='org.jenkins-ci.plugins.workflow' artifactId='workflow-support' version='936.v9fa_77211ca_e1' url='https://github.com/jenkinsci/workflow-support-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Pipeline: Step API' groupId='org.jenkins-ci.plugins.workflow' artifactId='workflow-step-api' version='678.v3ee58b_469476' url='https://github.com/jenkinsci/workflow-step-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='asm-analysis' groupId='org.ow2.asm' artifactId='asm-analysis' version='9.7.1' url='http://asm.ow2.io/'><l:description>Static code analysis API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Script Security Plugin' groupId='org.jenkins-ci.plugins' artifactId='script-security' version='1369.v9b_98a_4e95b_2d' url='https://github.com/jenkinsci/script-security-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Pipeline: API' groupId='org.jenkins-ci.plugins.workflow' artifactId='workflow-api' version='1336.vee415d95c521' url='https://github.com/jenkinsci/workflow-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>