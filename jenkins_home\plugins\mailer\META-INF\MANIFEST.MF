Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: <PERSON> Mailer Plugin
Specification-Version: 0.0
Implementation-Title: Jenkins Mailer Plugin
Implementation-Version: 489.vd4b_25144138f
Plugin-Class: hudson.tasks.PluginImpl
Group-Id: org.jenkins-ci.plugins
Artifact-Id: mailer
Short-Name: mailer
Long-Name: Jenkins Mailer Plugin
Url: https://github.com/jenkinsci/mailer-plugin
Plugin-Version: 489.vd4b_25144138f
Hudson-Version: 2.479
Jenkins-Version: 2.479
Plugin-Dependencies: jakarta-mail-api:2.1.3-1,instance-identity:201.vd2a
 _b_5a_468a_a_6,display-url-api:2.204.vf6fddd8a_8b_e9
Plugin-Developers: 
Plugin-License-Name: The MIT license
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/mailer-plugin
 .git
Plugin-ScmTag: d4b25144138f8ed3ea3763d7bbe4688d061dcadf
Plugin-ScmUrl: https://github.com/jenkinsci/mailer-plugin
Implementation-Build: d4b25144138f8ed3ea3763d7bbe4688d061dcadf

