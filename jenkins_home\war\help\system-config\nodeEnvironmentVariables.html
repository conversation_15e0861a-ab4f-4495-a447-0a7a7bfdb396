<div>
  Environment variables defined here will be made available to every build
  executed by this agent, and will override any environment variables that have
  the same
  <i>Name</i>
  as those defined on the
  <i>Configure System</i>
  page.
  <p>
    Using the syntax
    <code>$NAME</code>
    or
    <code>${NAME}</code>
    (
    <code>%NAME%</code>
    on Windows), these variables can be used in job configurations, or from
    process launched by a build.
  </p>

  <p>
    <PERSON> also supports a special syntax,
    <code>BASE+EXTRA</code>
    , which allows you to add multiple key-value pairs here, which will be
    prepended to an existing environment variable.
  </p>

  <p>
    For example, if you have a machine which has
    <code>PATH=/usr/bin</code>
    , you could add to the standard path by defining an environment variable
    here, with the name
    <code>PATH+LOCAL_BIN</code>
    and value
    <code>/usr/local/bin</code>
    .
    <br />
    This would result in
    <code>PATH=/usr/local/bin:/usr/bin</code>
    being exported during builds executed on this machine.
    <code>PATH+LOCAL_BIN=/usr/local/bin</code>
    will also be exported.
    <br />
    Multiple entries are prepended to the "base" variable according to the
    alphabetical order of the "extra" part of the name.
  </p>

  <p>
    If the
    <i>Value</i>
    is empty or whitespace-only, it will not be added to the environment, nor
    will it override or unset any environment variable with the same name that
    may already exist (e.g. a variable defined by the system).
  </p>
</div>
