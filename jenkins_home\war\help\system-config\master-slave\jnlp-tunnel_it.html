<div>
  Quando si lancia un agente tramite JNLP, l'agente tenta di collegarsi a una
  specifica porta TCP di Jenkins per stabilire un canale di comunicazione.
  Alcune reti configurate in modo sicuro, tuttavia, possono impedire
  l'instaurazione di questa connessione. Ciò può succedere anche quando <PERSON> in esecuzione mascherato da un bilanciatore di carico,
  <a
    href="https://www.jenkins.io/doc/book/system-administration/reverse-proxy-configuration-apache/"
  >
    da un proxy inverso Apache
  </a>
  è in una
  <a href="https://en.wikipedia.org/wiki/Demilitarized_zone_(computing)">DMZ</a>
  , e così via.

  <p>
    Quest'opzione di tunneling consente di instradare questa connessione a un
    altro host/porta ed è utile in queste situazioni. Il campo può accettare i
    formati "
    <code>HOST:PORTA</code>
    ", "
    <code>:PORTA</code>
    " o "
    <code>HOST:</code>
    ". Con il primo formato, l'agente JNLP si collegherà alla porta TCP data
    sull'host specificato e assumerà che si sia configurata la rete in modo che
    questa porta inoltri la connessione alla porta TCP dell'agente JNLP Jenkins.
  </p>

  <p>
    Con gli ultimi due formati, il nome host e la porta predefiniti (vale a
    dire, il nome dell'host che esegue Jenkins e la porta TCP che Jenkins ha
    aperto) sono utilizzati per integrare i valori mancanti. In particolare, il
    formato
    <code>HOST:</code>
    è utile se viene utilizzato un proxy inverso HTTP e Jenkins in realtà viene
    eseguito su un altro sistema.
  </p>
</div>
