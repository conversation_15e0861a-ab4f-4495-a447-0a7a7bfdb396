#!/bin/bash

echo "Cleaning up <PERSON> configuration and cache..."

# Stop Jenkins container
echo "Stopping <PERSON> container..."
docker-compose down jenkins

# Remove <PERSON> volume to clear all cached configurations
echo "Removing Jenkins volume to clear cache..."
docker volume rm casecashback_jenkins_home 2>/dev/null || true

# Remove any existing jenkins_home directory
echo "Removing local jenkins_home directory..."
sudo rm -rf ./jenkins_home

# Clean up Docker images
echo "Cleaning up Jenkins Docker images..."
docker image rm casecashback-jenkins 2>/dev/null || true
docker image prune -f

echo "Jenkins cleanup completed!"
echo "Now run: docker-compose up -d jenkins"
