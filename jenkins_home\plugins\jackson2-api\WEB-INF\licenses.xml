<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='jackson2-api' version='2.18.3-402.v74c4eb_f122b_2'><l:dependency name='Jackson 2 API Plugin' groupId='org.jenkins-ci.plugins' artifactId='jackson2-api' version='2.18.3-402.v74c4eb_f122b_2' url='https://github.com/jenkinsci/jackson2-api-plugin'><l:description>This plugin exposes the Jackson 2 JSON APIs to other Jenkins plugins.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/><l:license name='BSD 3-Clause ASM' url='https://gitlab.ow2.org/asm/asm/raw/ASM_9_7_1/LICENSE.txt'/></l:dependency><l:dependency name='Jackson-core' groupId='com.fasterxml.jackson.core' artifactId='jackson-core' version='2.18.3' url='https://github.com/FasterXML/jackson-core'><l:description>Core Jackson processing abstractions (aka Streaming API), implementation for JSON</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson-dataformat-XML' groupId='com.fasterxml.jackson.dataformat' artifactId='jackson-dataformat-xml' version='2.18.3' url='https://github.com/FasterXML/jackson-dataformat-xml'><l:description>Data format extension for Jackson to offer
alternative support for serializing POJOs as XML and deserializing XML as pojos.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson-dataformat-CSV' groupId='com.fasterxml.jackson.dataformat' artifactId='jackson-dataformat-csv' version='2.18.3' url='https://github.com/FasterXML/jackson-dataformats-text'><l:description>Support for reading and writing CSV-encoded data via Jackson
abstractions.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson-dataformat-YAML' groupId='com.fasterxml.jackson.dataformat' artifactId='jackson-dataformat-yaml' version='2.18.3' url='https://github.com/FasterXML/jackson-dataformats-text'><l:description>Support for reading and writing YAML-encoded data via Jackson abstractions.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson datatype: JSR310' groupId='com.fasterxml.jackson.datatype' artifactId='jackson-datatype-jsr310' version='2.18.3' url='https://github.com/FasterXML/jackson-modules-java8/jackson-datatype-jsr310'><l:description>Add-on module to support JSR-310 (Java 8 Date &amp; Time API) data types.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Woodstox' groupId='com.fasterxml.woodstox' artifactId='woodstox-core' version='7.0.0' url='https://github.com/FasterXML/woodstox'><l:description>Woodstox is a high-performance XML processor that implements Stax (JSR-173),
SAX2 and Stax2 APIs</l:description><l:license name='The Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson-annotations' groupId='com.fasterxml.jackson.core' artifactId='jackson-annotations' version='2.18.3' url='https://github.com/FasterXML/jackson'><l:description>Core annotations used for value types, used by Jackson data binding package.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Stax2 API' groupId='org.codehaus.woodstox' artifactId='stax2-api' version='4.2.2' url='http://github.com/FasterXML/stax2-api'><l:description>Stax2 API is an extension to basic Stax 1.0 API that adds significant new functionality, such as full-featured bi-direction validation interface and high-performance Typed Access API.</l:description><l:license name='The BSD 2-Clause License' url='http://www.opensource.org/licenses/bsd-license.php'/></l:dependency><l:dependency name='Jackson datatype: org.json' groupId='com.fasterxml.jackson.datatype' artifactId='jackson-datatype-json-org' version='2.18.3' url='https://github.com/FasterXML/jackson-datatypes-misc'><l:description>Support for datatypes of "org.json" JSON library (see https://json.org/java),
mainly to make it easier to upgrade code to Jackson, using automated conversions.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson-module-parameter-names' groupId='com.fasterxml.jackson.module' artifactId='jackson-module-parameter-names' version='2.18.3' url='https://github.com/FasterXML/jackson-modules-java8/jackson-module-parameter-names'><l:description>Add-on module for Jackson (https://github.com/FasterXML/jackson) to support
introspection of method/constructor parameter names, without having to add explicit property name annotation.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson datatype: jdk8' groupId='com.fasterxml.jackson.datatype' artifactId='jackson-datatype-jdk8' version='2.18.3' url='https://github.com/FasterXML/jackson-modules-java8/jackson-datatype-jdk8'><l:description>Add-on module for Jackson (https://github.com/FasterXML/jackson) to support
JDK 8 data types.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson module: Old JAXB Annotations (javax.xml.bind)' groupId='com.fasterxml.jackson.module' artifactId='jackson-module-jaxb-annotations' version='2.18.3' url='https://github.com/FasterXML/jackson-modules-base'><l:description>Support for using JAXB annotations as an alternative to "native" Jackson annotations,
for configuring data-binding.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='jackson-databind' groupId='com.fasterxml.jackson.core' artifactId='jackson-databind' version='2.18.3' url='https://github.com/FasterXML/jackson'><l:description>General data-binding functionality for Jackson: works on core streaming API</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson-dataformat-TOML' groupId='com.fasterxml.jackson.dataformat' artifactId='jackson-dataformat-toml' version='2.18.3' url='https://github.com/FasterXML/jackson-dataformats-text'><l:description>Support for reading and writing TOML-encoded data via Jackson abstractions.</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jackson dataformat: CBOR' groupId='com.fasterxml.jackson.dataformat' artifactId='jackson-dataformat-cbor' version='2.18.3' url='https://github.com/FasterXML/jackson-dataformats-binary'><l:description>Support for reading and writing Concise Binary Object Representation
([CBOR](https://www.rfc-editor.org/info/rfc7049)
encoded data using Jackson abstractions (streaming API, data binding, tree model)</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>