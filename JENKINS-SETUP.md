# Jenkins Setup and Troubleshooting Guide

## Issues Fixed

### 1. Volume Permissions Issue
**Problem**: `touch: cannot touch '/var/jen<PERSON>_home/copy_reference_file.log': Permission denied`

**Solution**: 
- Updated `Dockerfile.jenkins` to properly set up permissions
- Changed `docker-compose.yml` to use named volume instead of bind mount
- Added user mapping to ensure correct permissions

### 2. Platform Mismatch Issue
**Problem**: `Batch scripts can only be run on Windows nodes` - <PERSON> trying to run `bat` commands on Linux

**Solution**: 
- Your `Jenkinsfile` correctly uses `sh` commands for Linux
- The issue was in Jenkins configuration trying to use Windows batch commands
- Fixed by proper Jenkins setup and plugin installation

## Setup Instructions

### 1. Fix Permissions (if needed)
If you encounter permission issues, run:
```bash
chmod +x fix-jenkins-permissions.sh
./fix-jenkins-permissions.sh
```

### 2. Build and Start Jenkins
```bash
# Build the Jenkins image
docker-compose build jenkins

# Start Jenkins
docker-compose up -d jenkins
```

### 3. Access Jenkins
- URL: http://localhost:8082
- Username: `admin`
- Password: `admin123`

**Important**: Change the default password after first login!

### 4. Verify Setup
1. <PERSON> <PERSON> logs: `docker-compose logs jenkins`
2. Ensure no permission errors
3. Verify Docker is accessible from Jenkins container

## Pipeline Configuration

Your `Jenkinsfile` is correctly configured for Linux with `sh` commands:
- ✅ `sh 'chmod +x mvnw'`
- ✅ `sh './mvnw clean package -DskipTests'`
- ✅ `sh './mvnw test'`
- ✅ `sh "docker build -t ${DOCKER_IMAGE}:${DOCKER_TAG} ."`

## Troubleshooting

### If you still see permission errors:
1. Stop Jenkins: `docker-compose down jenkins`
2. Remove the volume: `docker volume rm casecashback_jenkins_home`
3. Run the permission fix script: `./fix-jenkins-permissions.sh`
4. Restart Jenkins: `docker-compose up -d jenkins`

### If you see "Batch scripts can only be run on Windows nodes":
This is the most common issue. Jenkins is trying to run `bat` commands instead of `sh` commands.

**Quick Fix:**
```bash
# 1. Verify your Jenkinsfile is correct
chmod +x verify-jenkinsfile.sh
./verify-jenkinsfile.sh

# 2. If Jenkinsfile is correct, clear Jenkins cache
chmod +x cleanup-jenkins.sh
./cleanup-jenkins.sh

# 3. Rebuild and restart Jenkins
docker-compose build jenkins
docker-compose up -d jenkins
```

**Manual Fix in Jenkins UI:**
1. Go to Jenkins UI: http://localhost:8082
2. Navigate to your pipeline job
3. Click "Configure"
4. Under "Pipeline" section, ensure:
   - Definition: "Pipeline script from SCM"
   - SCM: Git
   - Repository URL: Your repo URL
   - Script Path: `Jenkinsfile`
5. Save the configuration

**Check Node Configuration:**
1. Go to "Manage Jenkins" → "Manage Nodes and Clouds"
2. Click on "Built-in Node" → "Configure"
3. Ensure "Usage" is set to "Use this node as much as possible"
4. Check that no Windows-specific labels are set

## Docker Integration

The Jenkins container now includes:
- Docker CLI installed
- Jenkins user added to docker group
- Docker socket mounted for container access
- All necessary plugins for Docker workflows
