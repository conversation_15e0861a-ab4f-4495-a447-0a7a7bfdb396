{"version": 3, "file": "keyboard-shortcuts.js", "mappings": ";;;;;;;;AAAiC;AAEjCC,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,MAAM;EACpC,MAAMC,wBAAwB,GAAGC,QAAQ,CAACC,aAAa,CACrD,8BACF,CAAC;EACD,IAAIF,wBAAwB,EAAE;IAC5BH,+DAAO,CAACM,qCAAqC,CAAC,OAAO,CAAC,EAAE,MAAM;MAC5DH,wBAAwB,CAACI,KAAK,CAAC,CAAC;;MAEhC;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEA,MAAMC,aAAa,GAAGJ,QAAQ,CAACK,gBAAgB,CAC7C,mCACF,CAAC;EACD,IAAID,aAAa,CAACE,MAAM,KAAK,CAAC,EAAE;IAC9BV,+DAAO,CAAC,GAAG,EAAE,MAAM;MACjBQ,aAAa,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC;;MAExB;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,SAASL,qCAAqCA,CAACM,gBAAgB,EAAE;EAC/D,MAAMC,SAAS,GACbC,SAAS,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IACpDH,SAAS,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,IAC7CF,SAAS,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,MAAM;EAC7C,OAAOJ,gBAAgB,CAACM,OAAO,CAAC,YAAY,EAAEL,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC;AAC3E;;;;;;UCvCA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA;WACA;WACA,kBAAkB,qBAAqB;WACvC,oHAAoH,iDAAiD;WACrK;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC7BA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD,8CAA8C;;;;;WCA9C;;;;;WCAA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,8CAA8C;;WAE9C;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,iCAAiC,mCAAmC;WACpE;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;WClDA;;;;;UEAA;UACA;UACA;UACA,8EAA8E,kCAAkC;UAChH", "sources": ["webpack://jenkins-ui/./src/main/js/keyboard-shortcuts.js", "webpack://jenkins-ui/webpack/bootstrap", "webpack://jenkins-ui/webpack/runtime/chunk loaded", "webpack://jenkins-ui/webpack/runtime/define property getters", "webpack://jenkins-ui/webpack/runtime/global", "webpack://jenkins-ui/webpack/runtime/hasOwnProperty shorthand", "webpack://jenkins-ui/webpack/runtime/runtimeId", "webpack://jenkins-ui/webpack/runtime/jsonp chunk loading", "webpack://jenkins-ui/webpack/runtime/nonce", "webpack://jenkins-ui/webpack/before-startup", "webpack://jenkins-ui/webpack/startup", "webpack://jenkins-ui/webpack/after-startup"], "sourcesContent": ["import hotkeys from \"hotkeys-js\";\n\nwindow.addEventListener(\"load\", () => {\n  const openCommandPaletteButton = document.querySelector(\n    \"#button-open-command-palette\",\n  );\n  if (openCommandPaletteButton) {\n    hotkeys(translateModifierKeysForUsersPlatform(\"CMD+K\"), () => {\n      openCommandPaletteButton.click();\n\n      // Returning false stops the event and prevents default browser events\n      return false;\n    });\n  }\n\n  const pageSearchBar = document.querySelectorAll(\n    \"#page-body .jenkins-search__input\",\n  );\n  if (pageSearchBar.length === 1) {\n    hotkeys(\"/\", () => {\n      pageSearchBar[0].focus();\n\n      // Returning false stops the event and prevents default browser events\n      return false;\n    });\n  }\n});\n\n/**\n * Given a keyboard shortcut, e.g. CMD+K, replace any included modifier keys for the user's\n * platform e.g. output will be CMD+K for macOS/iOS, CTRL+K for Windows/Linux\n * @param {string} keyboardShortcut The shortcut to translate\n */\nfunction translateModifierKeysForUsersPlatform(keyboardShortcut) {\n  const useCmdKey =\n    navigator.platform.toUpperCase().indexOf(\"MAC\") >= 0 ||\n    navigator.platform.toUpperCase() === \"IPHONE\" ||\n    navigator.platform.toUpperCase() === \"IPAD\";\n  return keyboardShortcut.replace(/CMD|CTRL/gi, useCmdKey ? \"CMD\" : \"CTRL\");\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "__webpack_require__.j = 122;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t122: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkjenkins_ui\"] = self[\"webpackChunkjenkins_ui\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [96], function() { return __webpack_require__(212); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["hotkeys", "window", "addEventListener", "openCommandPaletteButton", "document", "querySelector", "translateModifierKeysForUsersPlatform", "click", "pageSearchBar", "querySelectorAll", "length", "focus", "keyboardShortcut", "useCmdKey", "navigator", "platform", "toUpperCase", "indexOf", "replace"], "sourceRoot": ""}