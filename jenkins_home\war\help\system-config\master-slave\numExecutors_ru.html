﻿<div>
  Эта настройка управляет количеством одновременных сборок, которые Jenkins
  может производить на этом подчиненном узле. Указанное значение в конечном
  итоге определяет загрузку узла которую может вызвать Jenkins. Подходящее
  значение для начала - установите его равным количеству процессоров на узле.

  <p>
    Увеличивая это значение выше вы заметите что сборки стали более
    продолжительными, однако результирующая пропускная способность может быть
    выше, т.к. в этом случае какие-то проекты будут использовать процессор в те
    моменты, когда другие проекты ожидают окончания операций ввода-вывода.
  </p>

  <p>
    Установка значения в 0 может использоваться для временного запрета сборок на
    указанном узле без потери настроек.
  </p>
</div>
