<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='joda-time-api' version='2.14.0-127.v7d9da_295a_d51'><l:dependency name='Joda Time API Plugin' groupId='io.jenkins.plugins' artifactId='joda-time-api' version='2.14.0-127.v7d9da_295a_d51' url='https://github.com/jenkinsci/joda-time-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Joda-Time' groupId='joda-time' artifactId='joda-time' version='2.14.0' url='https://www.joda.org/joda-time/'><l:description>Date and time library to replace JDK date handling</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>