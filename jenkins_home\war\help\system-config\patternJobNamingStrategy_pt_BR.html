<div>
  Defina um padr&#227;o (express&#227;o regular) para checar se o nome de um job
  &#233; v&#225;lido ou n&#227;o.
  <p>
    For&#231;ando a checagem em jobs que j&#225; existem, vai permitir que
    voc&#234; reforce uma conven&#231;&#227;o - e.g. mesmo que os usu&#225;rios
    n&#227;o mudem o nome, O job ser&#225; validado com o padr&#227;o definido
    em cada submiss&#227;o e atualiza&#231;&#227;o at&#233; que o nome confirme.
    <br />
    <i>
      Essa op&#231;&#227;o n&#227;o afetar&#225; a execu&#231;&#227;o de jobs
      com nomes n&#227;o complascentes. Isto apenas controla o processo de
      valida&#231;&#227;o no momento de salvar as configura&#231;&#245;es de um
      job.
    </i>
  </p>
</div>
