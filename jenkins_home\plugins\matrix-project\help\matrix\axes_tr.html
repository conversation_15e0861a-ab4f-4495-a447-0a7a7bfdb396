<div>
  Konfig&#252;rasyon matrisinizin daha fazla eks<PERSON> i<PERSON>&#305; varsa, burada belirleyeb<PERSON>rs<PERSON>z.
  <p>
  <PERSON><PERSON><PERSON> ki veritaban&#305; uy<PERSON><PERSON>an&#305;z ile ilgili bir test &#231;al&#305;&#351;t&#305;rman&#305;z gerekiyor ve
  bunu da &#252;&#231; ayr&#305; veritaban&#305; ile yapmak istiyorsunuz: MySQL, PostgreSQL, ve Oracle.
  Yap&#305;land&#305;rma dosyalar&#305;n&#305;z&#305;, <tt>ant -Ddatabase=mysql</tt> &#351;eklinde &#231;al&#305;&#351;abilecek bir bi&#231;imde tasarlayabilirsiniz.  
  <p>
  <PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON> girdi&#287;i k&#305;s&#305;m buras&#305;d&#305;r. "database" isimli bir de&#287;i&#351;k<PERSON><PERSON><PERSON> vard&#305;r ve &#252;&#231; de&#287;er al&#305;r.
  Konfig&#252;ras<PERSON><PERSON> yapt&#305;&#287;&#305;n&#305;zda, <PERSON> konfig&#252;rasyon matrisini tamamlamak ad&#305;na "database" 
  de&#287;i&#351;kenine atanan &#252;&#231; ayr&#305; de&#287;i&#351;kenle yap&#305;land&#305;rmay&#305; 3 kere &#231;al&#305;&#351;t&#305;racakt&#305;r.
  <p>
  Burada belirlenen de&#287;i&#351;kenler, yap&#305;land&#305;rmaya ortam de&#287;i&#351;keni olarak aktar&#305;labilirler.
  Ant ve Maven i&#231;in buna ek olarak, de&#287;i&#351;kenler, komut sat&#305;r&#305;nda <tt>-D<i>variableName</i>=<i>value</i></tt>
  &#351;eklindeki gibi &#246;zellik olarak bulunabilirler.
  <p>
  Birden fazla eksen tan&#305;m&#305; yap&#305;ld&#305;&#287;&#305;nda, eksenlerin m&#252;mk&#252;n t&#252;m kombinasyonlar&#305; ayr&#305;nt&#305;l&#305; bir &#351;ekilde
  yap&#305;land&#305;r&#305;l&#305;r. Etiketlerde veya JDK'larda tan&#305;mlanan &#231;oklu-de&#287;erler de ayn&#305; &#351;ekilde i&#351;lenir. 
  Yani, e&#287;er jdk=[JDK5,JDK6],database=[mysql,postgresql,oracle] ve container=[jetty,tomcat] &#351;eklinde
  bir tan&#305;m yaparsan&#305;z, her yap&#305;land&#305;rma i&#231;in 2x3x2=12 adet alt-yap&#305;land&#305;rma &#231;al&#305;&#351;t&#305;r&#305;lacakt&#305;r.
</div>