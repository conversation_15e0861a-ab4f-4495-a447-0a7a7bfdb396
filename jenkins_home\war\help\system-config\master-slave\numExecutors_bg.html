<div>
  Това определя броя на едновременните изграждания, които Jenkins изпълнява на
  тази машина. Стойността отговаря на максималното натоварване на машината,
  което Jenkins може да си позволи. Добра начална стойност е броя на ядрата на
  процесорите.

  <p>
    Увеличаването на броя над тази стойност ще забави завършването на отделното
    изграждане, но ще увеличи като цяло производителността, защото позволява на
    една задача да се възползва от процесор, докато друга завършва
    входно/изходна операция.
  </p>

  <p>
    Задаването на стойността да е
    <code>0</code>
    позволява да изключите машината от Jenkins, без да загубите други настройки.
  </p>
</div>
