{"version": 3, "file": "app.js", "mappings": ";;;;;;;;;AAAA,SAASA,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACrC,IAAIC,WAAW;EACf,IAAIC,CAAC,GAAGH,OAAO,CAACI,OAAO,CAAC,GAAG,CAAC;EAC5B,IAAID,CAAC,IAAI,CAAC,EAAE;IACVD,WAAW,GAAGF,OAAO,CAACK,SAAS,CAACF,CAAC,CAAC;EACpC,CAAC,MAAM;IACLD,WAAW,GAAG,EAAE;EAClB;EAEAC,CAAC,GAAGH,OAAO,CAACI,OAAO,CAAC,GAAG,CAAC;EACxB,IAAID,CAAC,IAAI,CAAC,EAAE;IACVH,OAAO,GAAGA,OAAO,CAACK,SAAS,CAAC,CAAC,EAAEF,CAAC,CAAC;EACnC;EAEA,IAAIH,OAAO,CAACM,QAAQ,CAAC,GAAG,CAAC,EAAE;IACzB,OAAON,OAAO,GAAGC,OAAO,GAAGC,WAAW;EACxC;EACA,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAGC,WAAW;AAC9C;AAEA,yCAAe;EAAEH;AAAY,CAAC;;ACpB9B,SAASQ,OAAOA,CAACC,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACjDC,SAAS,CAACL,OAAO,CAACC,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;AACrD;AAEA,SAASE,YAAYA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC5CH,SAAS,CAACC,YAAY,CAACC,SAAS,EAAEC,WAAW,CAAC;AAChD;AAEA,kDAAe;EAAER,OAAO;EAAEM;AAAa,CAAC;;ACRjC,SAASG,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EACnDF,QAAQ,CAACG,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAAC;EAChC,OAAOJ,QAAQ,CAACK,OAAO,CAACC,iBAAiB;AAC3C;AAEO,SAASC,IAAIA,CAACC,MAAM,EAAE;EAC3B,OAAOA,MAAM,CACVJ,IAAI,CAAC,CAAC,CACNK,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBC,WAAW,CAAC,CAAC;AAClB;;ACXA,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,CAACH,OAAO,CAAC,UAAU,EAAGI,KAAK,IAAK;IACxC,QAAQA,KAAK;MACX,KAAK,GAAG;QACN,OAAO,MAAM;MACf,KAAK,GAAG;QACN,OAAO,MAAM;MACf,KAAK,GAAG;QACN,OAAO,OAAO;MAChB,KAAK,GAAG;QACN,OAAO,QAAQ;MACjB,KAAK,GAAG;QACN,OAAO,QAAQ;IACnB;EACF,CAAC,CAAC;AACJ;;;ACfmD;AACP;AAE5C,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAO;IACLT,OAAO,EAAE,iCAAiC;IAC1CU,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAEpB,QAAQ,CAACqB,IAAI;IACvBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAGC,QAAQ,IAAK;MACpB,MAAMC,eAAe,GAAGD,QAAQ,CAACE,SAAS,CAACC,UAAU;MAErD,IAAIF,eAAe,CAACG,SAAS,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpDJ,eAAe,CAACG,SAAS,CAACE,GAAG,CAAC,kBAAkB,CAAC;MACnD;IACF,CAAC;IACDC,MAAM,EAAGP,QAAQ,IAAK;MACpB,MAAMC,eAAe,GAAGD,QAAQ,CAACE,SAAS,CAACC,UAAU;MACrDF,eAAe,CAACG,SAAS,CAACI,MAAM,CAAC,kBAAkB,CAAC;IACtD;EACF,CAAC;AACH;AAEA,SAASC,QAAQA,CAACC,OAAO,EAAE;EACzB,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAC/B;IACEC,IAAI,EAAE;EACR,CAAC,EACDJ,OACF,CAAC;EAED,MAAMK,KAAK,GAAG/B,SAAS,CAAC2B,WAAW,CAACI,KAAK,CAAC;EAC1C,IAAIC,SAAS;EACb,IAAIC,YAAY;EAChB,IAAIC,aAAa;EACjB,IAAIP,WAAW,CAACQ,KAAK,EAAE;IACrBH,SAAS,GAAGhC,SAAS,CAAC2B,WAAW,CAACQ,KAAK,CAACC,IAAI,CAAC;IAC7CH,YAAY,GAAGjC,SAAS,CAAC2B,WAAW,CAACQ,KAAK,CAACE,OAAO,CAAC;IACnDH,aAAa,GAAGlC,SAAS,CAAC2B,WAAW,CAACQ,KAAK,CAACG,QAAQ,CAAC;EACvD;EACA,MAAMC,GAAG,GAAGZ,WAAW,CAACG,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,QAAQ;EAExD,MAAMU,IAAI,GAAGrD,qBAAqB,CAAC;AACrC,SAASoD,GAAG,kCAAkCZ,WAAW,CAACc,KAAK,GAAGzC,SAAS,CAAC2B,WAAW,CAACc,KAAK,CAAC,GAAG,EAAE,KAAKd,WAAW,CAACe,GAAG,GAAG,SAAS1C,SAAS,CAAC2B,WAAW,CAACe,GAAG,CAAC,GAAG,GAAG,EAAE,IAAIf,WAAW,CAAC/C,EAAE,GAAG,OAAOoB,SAAS,CAAC2B,WAAW,CAAC/C,EAAE,CAAC,GAAG,GAAG,EAAE;AAClO,YACY+C,WAAW,CAACgB,IAAI,GACZ,6CACEhB,WAAW,CAACiB,OAAO,GACfjB,WAAW,CAACiB,OAAO,GACnB,aAAab,KAAK,UAAUJ,WAAW,CAACgB,IAAI,MAAM,QAChD,GACR,EAAE;AAClB,YACYZ,KAAK;AACjB,sBACsBJ,WAAW,CAACQ,KAAK,IAAI,IAAI,GACrB,sEAAsED,aAAa,oBAAoBD,YAAY,KAAKD,SAAS,SAAS,GAC1I,EAAE;AAC5B,YAEYL,WAAW,CAACkB,OAAO,IAAI,IAAI,GACvB,uDAAuD,GACvD,EAAE;AAClB,UACUN,GAAG;AACb,KAAK,CAAC;EAEJ,IAAIb,OAAO,CAACoB,OAAO,EAAE;IACnBN,IAAI,CAACO,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAKtB,OAAO,CAACoB,OAAO,CAACE,KAAK,CAAC,CAAC;EACnE;EACA,IAAItB,OAAO,CAACuB,UAAU,EAAE;IACtBT,IAAI,CAACU,UAAU,GAAGxB,OAAO,CAACuB,UAAU;EACtC;EACA,OAAOT,IAAI;AACb;AAEA,SAASW,OAAOA,CAACpB,KAAK,EAAE;EACtB,OAAO5C,qBAAqB,CAC1B,wCAAwC4C,KAAK,MAC/C,CAAC;AACH;AAEA,SAASqB,SAASA,CAAA,EAAG;EACnB,OAAOjE,qBAAqB,CAC1B,iDACF,CAAC;AACH;AAEA,SAASkE,WAAWA,CAACtB,KAAK,EAAE;EAC1B,OAAO5C,qBAAqB,CAC1B,4CAA4C4C,KAAK,MACnD,CAAC;AACH;AAEA,SAASuB,QAAQA,CAACvB,KAAK,EAAE;EACvB,OAAO5C,qBAAqB,CAC1B,yCAAyC4C,KAAK,MAChD,CAAC;AACH;AAEA,8CAAe;EACb5B,QAAQ;EACRsB,QAAQ;EACR0B,OAAO;EACPC,SAAS;EACTC,WAAW;EACXC;AACF,CAAC;;AClHD;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASC,qBAAqBA,CAC3CC,SAAS,EACTC,SAAS,EACTC,aAAa,EAIb;EAAA,IAHAC,oBAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM,CAAC,CAAC;EAAA,IAC/BG,mBAAmB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MACpBI,MAAM,CAACC,gBAAgB,CAACT,SAAS,CAAC,CAACU,UAAU,KAAK,SAAS;EAE7DF,MAAM,CAACjB,gBAAgB,CAAC,OAAO,EAAGoB,CAAC,IAAK;IACtC,IAAIC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACb,SAAS,CAAC,CAAC,CAAC;IACnC,IAAIc,YAAY,GAAGH,KAAK,CAACI,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrD,SAAS,CAACC,QAAQ,CAACqC,aAAa,CAAC,CAAC;IACzE,IAAIF,SAAS,IAAIO,mBAAmB,CAACP,SAAS,CAAC,EAAE;MAC/C,IAAIW,CAAC,CAACO,GAAG,KAAK,KAAK,EAAE;QACnB,IAAIN,KAAK,CAACO,QAAQ,CAACrF,QAAQ,CAACsF,aAAa,CAAC,EAAE;UAC1C,IAAIL,YAAY,EAAE;YAChBA,YAAY,CAACnD,SAAS,CAACI,MAAM,CAACkC,aAAa,CAAC;UAC9C;UACAa,YAAY,GAAGjF,QAAQ,CAACsF,aAAa;UACrCL,YAAY,CAACnD,SAAS,CAACE,GAAG,CAACoC,aAAa,CAAC;QAC3C;MACF;IACF;EACF,CAAC,CAAC;EACFM,MAAM,CAACjB,gBAAgB,CAAC,SAAS,EAAGoB,CAAC,IAAK;IACxC,IAAIC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACb,SAAS,CAAC,CAAC,CAAC;IACnC,IAAIc,YAAY,GAAGH,KAAK,CAACI,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrD,SAAS,CAACC,QAAQ,CAACqC,aAAa,CAAC,CAAC;;IAEzE;IACA,IAAIF,SAAS,IAAIO,mBAAmB,CAACP,SAAS,CAAC,EAAE;MAC/C,IAAIW,CAAC,CAACO,GAAG,KAAK,WAAW,EAAE;QACzBP,CAAC,CAACU,cAAc,CAAC,CAAC;QAElB,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAACnD,SAAS,CAACI,MAAM,CAACkC,aAAa,CAAC;UAC5C,MAAMoB,IAAI,GAAGV,KAAK,CAACA,KAAK,CAAC7F,OAAO,CAACgG,YAAY,CAAC,GAAG,CAAC,CAAC;UAEnD,IAAIO,IAAI,EAAE;YACRP,YAAY,GAAGO,IAAI;UACrB,CAAC,MAAM;YACLP,YAAY,GAAGH,KAAK,CAAC,CAAC,CAAC;UACzB;QACF,CAAC,MAAM;UACLG,YAAY,GAAGH,KAAK,CAAC,CAAC,CAAC;QACzB;QAEAW,eAAe,CAACR,YAAY,EAAEb,aAAa,EAAEU,KAAK,CAAC;MACrD,CAAC,MAAM,IAAID,CAAC,CAACO,GAAG,KAAK,SAAS,EAAE;QAC9BP,CAAC,CAACU,cAAc,CAAC,CAAC;QAElB,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAACnD,SAAS,CAACI,MAAM,CAACkC,aAAa,CAAC;UAC5C,MAAMsB,QAAQ,GAAGZ,KAAK,CAACA,KAAK,CAAC7F,OAAO,CAACgG,YAAY,CAAC,GAAG,CAAC,CAAC;UAEvD,IAAIS,QAAQ,EAAE;YACZT,YAAY,GAAGS,QAAQ;UACzB,CAAC,MAAM;YACLT,YAAY,GAAGH,KAAK,CAACA,KAAK,CAACP,MAAM,GAAG,CAAC,CAAC;UACxC;QACF,CAAC,MAAM;UACLU,YAAY,GAAGH,KAAK,CAACA,KAAK,CAACP,MAAM,GAAG,CAAC,CAAC;QACxC;QAEAkB,eAAe,CAACR,YAAY,EAAEb,aAAa,EAAEU,KAAK,CAAC;MACrD,CAAC,MAAM,IAAID,CAAC,CAACO,GAAG,KAAK,OAAO,EAAE;QAC5B,IAAIH,YAAY,EAAE;UAChBJ,CAAC,CAACU,cAAc,CAAC,CAAC;UAClBN,YAAY,CAACU,KAAK,CAAC,CAAC;QACtB;MACF,CAAC,MAAM;QACLtB,oBAAoB,CAACY,YAAY,EAAEJ,CAAC,CAACO,GAAG,EAAEP,CAAC,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASY,eAAeA,CAACR,YAAY,EAAEb,aAAa,EAAEU,KAAK,EAAE;EAC3D,IAAIG,YAAY,EAAE;IAChB,IAAI,CAACW,YAAY,CAACX,YAAY,CAAC,EAAE;MAC/BA,YAAY,CAACY,cAAc,CAAC,KAAK,CAAC;IACpC;IACAZ,YAAY,CAACnD,SAAS,CAACE,GAAG,CAACoC,aAAa,CAAC;IACzC,IAAIU,KAAK,CAACO,QAAQ,CAACrF,QAAQ,CAACsF,aAAa,CAAC,EAAE;MAC1CL,YAAY,CAACa,KAAK,CAAC,CAAC;IACtB;EACF;AACF;AAEA,SAASF,YAAYA,CAACG,OAAO,EAAE;EAC7B,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;EAC5C,OACED,IAAI,CAACE,GAAG,IAAI,CAAC,IACbF,IAAI,CAACG,IAAI,IAAI,CAAC,IACdH,IAAI,CAACI,MAAM,IAAI1B,MAAM,CAAC2B,WAAW,IACjCL,IAAI,CAACM,KAAK,IAAI5B,MAAM,CAAC6B,UAAU;AAEnC;;;;ACtGyD;AACL;AACvB;AACmB;AAEhD,MAAMI,mBAAmB,GAAG,kCAAkC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACb,OAAO,EAAEc,QAAQ,EAAEC,SAAS,EAAE;EACtD,IAAIf,OAAO,CAACgB,MAAM,IAAIhB,OAAO,CAACgB,MAAM,CAACC,KAAK,CAAC7F,KAAK,KAAK,UAAU,EAAE;IAC/D4E,OAAO,CAACgB,MAAM,CAACE,OAAO,CAAC,CAAC;EAC1B;EAEAR,6BAAK,CACHV,OAAO,EACPzD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiE,SAAS,CAAC3F,QAAQ,CAAC,CAAC,EAAE;IACtCqG,WAAW,EACTnB,OAAO,CAACoB,OAAO,CAAC,aAAa,CAAC,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK;IAC/DC,QAAQA,CAAC1F,QAAQ,EAAE;MACjB,MAAM2F,MAAM,GAAGA,CAAA,KAAM;QACnB,IAAI3F,QAAQ,CAAC4F,MAAM,EAAE;UACnB;QACF;QAEAtH,QAAQ,CAACyD,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UAC5C,MAAM6D,oBAAoB,GACxB,CAAC,CAAC7D,KAAK,CAAC8D,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC;UAC7C,MAAMC,kBAAkB,GAAGhG,QAAQ,CAACE,SAAS,CAACG,QAAQ,CACpD2B,KAAK,CAAC8D,MACR,CAAC;UAED,IAAI,CAACD,oBAAoB,IAAI,CAACG,kBAAkB,EAAE;YAChDhG,QAAQ,CAACiG,IAAI,CAAC,CAAC;UACjB;QACF,CAAC,CAAC;QAEFd,QAAQ,CAACnF,QAAQ,CAAC;MACpB,CAAC;MACD,IAAIoF,SAAS,EAAE;QACbO,MAAM,CAAC,CAAC;MACV,CAAC,MAAM;QACL3F,QAAQ,CAACE,SAAS,CAAC6B,gBAAgB,CAAC,YAAY,EAAE4D,MAAM,CAAC;MAC3D;IACF;EACF,CAAC,CACH,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASO,qBAAqBA,CAAC9C,KAAK,EAAE+C,OAAO,EAAE;EAC7C,MAAMC,SAAS,GAAG9H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC/C6H,SAAS,CAAChG,SAAS,CAACE,GAAG,CAAC,kBAAkB,CAAC;EAC3C,IAAI6F,OAAO,KAAK,IAAI,EAAE;IACpBC,SAAS,CAAChG,SAAS,CAACE,GAAG,CAAC,2BAA2B,CAAC;EACtD;EAEA8C,KAAK,CACFiD,GAAG,CAAE7E,IAAI,IAAK;IACb,IAAIA,IAAI,CAACV,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAOU,IAAI,CAAC8E,QAAQ;IACtB;IAEA,IAAI9E,IAAI,CAACV,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAOgE,SAAS,CAAC3C,OAAO,CAACX,IAAI,CAACT,KAAK,CAAC;IACtC;IAEA,IAAIS,IAAI,CAACV,IAAI,KAAK,WAAW,EAAE;MAC7B,OAAOgE,SAAS,CAAC1C,SAAS,CAAC,CAAC;IAC9B;IAEA,IAAIZ,IAAI,CAACV,IAAI,KAAK,UAAU,EAAE;MAC5B,OAAOgE,SAAS,CAACxC,QAAQ,CAACd,IAAI,CAACT,KAAK,CAAC;IACvC;IAEA,MAAMN,QAAQ,GAAGqE,SAAS,CAACrE,QAAQ,CAACe,IAAI,CAAC;IAEzC,IAAIA,IAAI,CAACK,OAAO,IAAI,IAAI,EAAE;MACxBkD,6BAAK,CACHtE,QAAQ,EACRG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiE,SAAS,CAAC3F,QAAQ,CAAC,CAAC,EAAE;QACtCT,OAAO,EAAEwH,qBAAqB,CAAC1E,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC;QAC9CxC,OAAO,EAAE,YAAY;QACrBE,SAAS,EAAE,aAAa;QACxBK,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CACH,CAAC;IACH;IAEA,OAAOa,QAAQ;EACjB,CAAC,CAAC,CACD8F,OAAO,CAAE/E,IAAI,IAAK4E,SAAS,CAACI,WAAW,CAAChF,IAAI,CAAC,CAAC;EAEjD,IAAI4B,KAAK,CAACP,MAAM,KAAK,CAAC,EAAE;IACtBuD,SAAS,CAACI,WAAW,CAAC1B,SAAS,CAACzC,WAAW,CAAC,UAAU,CAAC,CAAC;EAC1D;EAEAE,qBAAqB,CACnB6D,SAAS,EACT,MAAMA,SAAS,CAACK,gBAAgB,CAAC,yBAAyB,CAAC,EAC3DxB,mBAAmB,EACnB,CAAC1B,YAAY,EAAEG,GAAG,EAAEgD,GAAG,KAAK;IAC1B,IAAI,CAACnD,YAAY,EAAE;MACjB;IACF;IACA,QAAQG,GAAG;MACT,KAAK,WAAW;QAAE;UAChB,MAAMiD,IAAI,GAAGpD,YAAY,CAACwC,OAAO,CAAC,mBAAmB,CAAC;UACtD,IAAIY,IAAI,EAAE;YACR,MAAMC,cAAc,GAAGD,IAAI,CAACtB,MAAM;YAClC,IAAIuB,cAAc,EAAE;cAClBA,cAAc,CAACX,IAAI,CAAC,CAAC;YACvB;UACF;UACA;QACF;MACA,KAAK,YAAY;QAAE;UACjB,MAAMY,QAAQ,GAAGtD,YAAY,CAAC8B,MAAM;UACpC,IAAI,CAACwB,QAAQ,EAAE;YACb;UACF;UAEAA,QAAQ,CAACC,IAAI,CAAC,CAAC;UACfD,QAAQ,CAACvB,KAAK,CAAC5G,OAAO,CACnBqI,aAAa,CAAC,yBAAyB,CAAC,CACxC3G,SAAS,CAACE,GAAG,CAAC2E,mBAAmB,CAAC;UACrC;QACF;MACA;QACE,IAAI1B,YAAY,CAACrB,UAAU,EAAE;UAC3BqB,YAAY,CAACrB,UAAU,CAACwE,GAAG,CAAC;QAC9B;IACJ;EACF,CAAC,EACAlE,SAAS,IAAK;IACb,MAAMwE,SAAS,GACbhE,MAAM,CAACC,gBAAgB,CAACT,SAAS,CAAC,CAACU,UAAU,KAAK,SAAS;IAC7D,MAAM+D,cAAc,GAAG5D,KAAK,CAACC,IAAI,CAC/BhF,QAAQ,CAACmI,gBAAgB,CAAC,mBAAmB,CAC/C,CAAC,CACES,MAAM,CAAE/H,QAAQ,IAAKqD,SAAS,KAAKrD,QAAQ,CAAC,CAC5C+H,MAAM,CACJ/H,QAAQ,IACP6D,MAAM,CAACC,gBAAgB,CAAC9D,QAAQ,CAAC,CAAC+D,UAAU,KAAK,SACrD,CAAC,CACAiE,KAAK,CACHhI,QAAQ,IACP,EACEqD,SAAS,CAAC4E,uBAAuB,CAACjI,QAAQ,CAAC,GAC3CkI,IAAI,CAACC,2BAA2B,CAEtC,CAAC;IAEH,OAAON,SAAS,IAAIC,cAAc;EACpC,CACF,CAAC;EAEDjC,aAAY,CAAChH,YAAY,CAACoI,SAAS,CAAC;EAEpC,OAAOA,SAAS;AAClB;AAEA,SAASmB,kBAAkBA,CAACC,QAAQ,EAAE;EACpC,MAAMpE,KAAK,GAAG,EAAE;EAChBC,KAAK,CAACC,IAAI,CAACkE,QAAQ,CAAC,CAACjB,OAAO,CAAEkB,KAAK,IAAK;IACtC,MAAMC,UAAU,GAAGD,KAAK,CAAChC,OAAO;IAChC,MAAM3E,IAAI,GAAG2G,KAAK,CAAChC,OAAO,CAACkC,YAAY;IAEvC,QAAQ7G,IAAI;MACV,KAAK,MAAM;QAAE;UACX,MAAMU,IAAI,GAAG;YACXT,KAAK,EAAE2G,UAAU,CAACE,YAAY;YAC9BhK,EAAE,EAAE8J,UAAU,CAACG,UAAU;YACzBlG,IAAI,EAAE+F,UAAU,CAACI,YAAY;YAC7BlG,OAAO,EAAE8F,UAAU,CAACI,YAAY;YAChCrG,KAAK,EAAEiG,UAAU,CAACK;UACpB,CAAC;UAED,IAAIL,UAAU,CAACM,YAAY,EAAE;YAC3BxG,IAAI,CAACE,GAAG,GAAGgG,UAAU,CAACM,YAAY;YAClCxG,IAAI,CAACV,IAAI,GAAG,MAAM;UACpB,CAAC,MAAM;YACLU,IAAI,CAACV,IAAI,GAAG,QAAQ;UACtB;UAEAsC,KAAK,CAAC6E,IAAI,CAACzG,IAAI,CAAC;UAChB;QACF;MACA,KAAK,SAAS;QACZ4B,KAAK,CAAC6E,IAAI,CAAC;UACTnH,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE2G,UAAU,CAACE,YAAY;UAC9BjG,IAAI,EAAE+F,UAAU,CAACI,YAAY;UAC7BlG,OAAO,EAAE8F,UAAU,CAACI,YAAY;UAChCjG,OAAO,EAAEA,CAAA,KAAM0F,kBAAkB,CAACE,KAAK,CAAC/I,OAAO,CAAC8I,QAAQ;QAC1D,CAAC,CAAC;QACF;MACF,KAAK,WAAW;QACdpE,KAAK,CAAC6E,IAAI,CAAC;UAAEnH,IAAI,EAAEA;QAAK,CAAC,CAAC;QAC1B;MACF,KAAK,QAAQ;QACXsC,KAAK,CAAC6E,IAAI,CAAC;UAAEnH,IAAI,EAAEA,IAAI;UAAEC,KAAK,EAAE2G,UAAU,CAACE;QAAa,CAAC,CAAC;QAC1D;MACF,KAAK,QAAQ;QACXxE,KAAK,CAAC6E,IAAI,CAAC;UAAEnH,IAAI,EAAEA,IAAI;UAAEwF,QAAQ,EAAEmB,KAAK,CAAC/I,OAAO,CAACwJ,SAAS,CAAC,IAAI;QAAE,CAAC,CAAC;QACnE;IACJ;EACF,CAAC,CAAC;EACF,OAAO9E,KAAK;AACd;AAEA,SAAS+E,gBAAgBA,CAAChF,CAAC,EAAE;EAC3B,IAAIA,CAAC,CAACiF,SAAS,EAAE;IACf,MAAMC,MAAM,GAAGlF,CAAC,CAACmF,YAAY,CAAC,aAAa,CAAC,IAAI,MAAM;IACtD,IAAI;MACFC,WAAW,CAACC,YAAY,CAACrF,CAAC,CAACiF,SAAS,CAAC,CAAC,EAAEC,MAAM,EAAElF,CAAC,CAACsF,aAAa,CAAC;IAClE,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;IACjB;EACF;AACF;AAEA,SAASG,qBAAqBA,CAAC1F,CAAC,EAAE2F,YAAY,EAAE;EAC9C,OAAOC,QAAQ,CAAC5F,CAAC,CAACsC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAIqD,YAAY;AAC9D;AAEA,SAASE,QAAQA,CAAC7D,QAAQ,EAAE;EAC1BA,QAAQ,CAAC8D,OAAO,GAAG,KAAK;EACxB,OAAO,MAAM;IACX,IAAI,CAAC9D,QAAQ,CAAC8D,OAAO,EAAE;MACrB9D,QAAQ,CAAC8D,OAAO,GAAG,IAAI;MACvBC,UAAU,CAAC,MAAM;QACf/D,QAAQ,CAAC,CAAC;QACVA,QAAQ,CAAC8D,OAAO,GAAG,KAAK;MAC1B,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;AACH;AAEA,0CAAe;EACb1B,kBAAkB;EAClBrC,gBAAgB;EAChBgB,qBAAqB;EACrBiC,gBAAgB;EAChBU,qBAAqB;EACrBG;AACF,CAAC;;AC5P8B;AACiB;AACC;AAEjD,SAASK,IAAIA,CAAA,EAAG;EACdC,yBAAyB,CAAC,CAAC;EAC3BC,iBAAiB,CAAC,CAAC;AACrB;;AAEA;AACA;AACA;AACA,SAASD,yBAAyBA,CAAA,EAAG;EACnCtE,aAAY,CAACtH,OAAO,CAAC,cAAc,EAAE,YAAY,EAAE,GAAG,EAAG8L,IAAI,IAAK;IAChE,MAAMC,SAAS,GAAGC,SAAS,CAACC,SAAS,CAACpM,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/D;IACA,MAAMqM,eAAe,GAAGtL,QAAQ,CAACC,aAAa,CAC5CkL,SAAS,GAAG,MAAM,GAAG,QACvB,CAAC;IACDG,eAAe,CAACC,SAAS,GAAG,+BAA+B;IAC3DD,eAAe,CAACnE,OAAO,CAACqE,IAAI,GAAGN,IAAI,CAACM,IAAI;IACxCF,eAAe,CAAC7H,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MACnDA,KAAK,CAAC6B,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC;IACF2F,IAAI,CAAChD,WAAW,CAACoD,eAAe,CAAC;EACnC,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAASL,iBAAiBA,CAAA,EAAG;EAC3BvE,aAAY,CAACtH,OAAO,CAClB,oFAAoF,EACpF,YAAY,EACZ,IAAI,EACH2G,OAAO,IACN+E,KAAK,CAAClE,gBAAgB,CAACb,OAAO,EAAGrE,QAAQ,IAAK;IAC5C,MAAM8J,IAAI,GAAGzF,OAAO,CAACoB,OAAO,CAACqE,IAAI;IACjC,MAAMC,YAAY,GAAG,CAAC1F,OAAO,CAACjE,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,GACxD,aAAa,GACb,qBAAqB;IAEzB,IAAIgE,OAAO,CAACjB,KAAK,EAAE;MACjBpD,QAAQ,CAACgK,UAAU,CAACZ,KAAK,CAAClD,qBAAqB,CAAC7B,OAAO,CAACjB,KAAK,CAAC,CAAC;MAC/D;IACF;IAEA6G,KAAK,CAACd,IAAI,CAACjM,WAAW,CAAC4M,IAAI,EAAEC,YAAY,CAAC,CAAC,CACxCG,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEE,IAAI,IACTpK,QAAQ,CAACgK,UAAU,CACjBZ,KAAK,CAAClD,qBAAqB,CACzBmE,+BAA+B,CAACD,IAAI,CAAChH,KAAK,CAC5C,CACF,CACF,CAAC,CACAkH,KAAK,CAAEC,KAAK,IAAK5B,OAAO,CAAC6B,GAAG,CAAC,4BAA4BD,KAAK,EAAE,CAAC,CAAC,CAClEE,OAAO,CAAC,MAAOzK,QAAQ,CAAC4F,MAAM,GAAG,IAAK,CAAC;EAC5C,CAAC,CACL,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASyE,+BAA+BA,CAACjH,KAAK,EAAE;EAC9C,OAAOA,KAAK,CAACiD,GAAG,CAAE7E,IAAI,IAAK;IACzB,IAAIA,IAAI,CAACV,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAO;QACLA,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAES,IAAI,CAACkJ;MACd,CAAC;IACH;IAEA,IAAIlJ,IAAI,CAACV,IAAI,KAAK,WAAW,EAAE;MAC7B,OAAO;QACLA,IAAI,EAAE;MACR,CAAC;IACH;IAEA,OAAO;MACLa,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfC,OAAO,EAAEJ,IAAI,CAACI,OAAO;MACrBb,KAAK,EAAES,IAAI,CAACkJ,WAAW;MACvBhJ,GAAG,EAAEF,IAAI,CAACE,GAAG;MACbZ,IAAI,EAAEU,IAAI,CAACmJ,IAAI,IAAInJ,IAAI,CAACoJ,oBAAoB,GAAG,QAAQ,GAAG,MAAM;MAChEzJ,KAAK,EAAEK,IAAI,CAACL,KAAK;MACjBW,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIN,IAAI,CAACmJ,IAAI,IAAInJ,IAAI,CAACoJ,oBAAoB,EAAE;UAC1C,IAAIpJ,IAAI,CAACoJ,oBAAoB,EAAE;YAC7BC,MAAM,CACHC,OAAO,CAACtJ,IAAI,CAACkJ,WAAW,EAAE;cAAEK,OAAO,EAAEvJ,IAAI,CAACuJ;YAAQ,CAAC,CAAC,CACpDb,IAAI,CAAC,MAAM;cACV,MAAMc,IAAI,GAAG1M,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;cAC3CyM,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAEzJ,IAAI,CAACmJ,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;cACvDK,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAEzJ,IAAI,CAACE,GAAG,CAAC;cACrC,IAAIF,IAAI,CAACmJ,IAAI,EAAE;gBACbO,KAAK,CAACC,YAAY,CAACH,IAAI,CAAC;cAC1B;cACA1M,QAAQ,CAACqB,IAAI,CAAC6G,WAAW,CAACwE,IAAI,CAAC;cAC/BA,IAAI,CAACI,MAAM,CAAC,CAAC;YACf,CAAC,CAAC;UACN,CAAC,MAAM;YACLnB,KAAK,CAACzI,IAAI,CAACE,GAAG,EAAE;cACd2G,MAAM,EAAE,MAAM;cACdgD,OAAO,EAAEH,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAACpB,IAAI,CAAEqB,GAAG,IAAK;cACf,IAAIA,GAAG,CAACC,EAAE,EAAE;gBACVC,eAAe,CAAC3E,IAAI,CAClBtF,IAAI,CAACkJ,WAAW,GAAG,SAAS,EAC5Be,eAAe,CAACC,OAClB,CAAC;cACH,CAAC,MAAM;gBACLD,eAAe,CAAC3E,IAAI,CAClBtF,IAAI,CAACkJ,WAAW,GAAG,WAAW,EAC9Be,eAAe,CAACE,KAClB,CAAC;cACH;YACF,CAAC,CAAC;UACJ;QACF;MACF,CAAC;MACD9J,OAAO,EAAEL,IAAI,CAACK,OAAO,GACjB,MAAM;QACJ,OAAOwI,+BAA+B,CAAC7I,IAAI,CAACK,OAAO,CAACuB,KAAK,CAAC;MAC5D,CAAC,GACD;IACN,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,8CAAe;EAAEiG;AAAK,CAAC;;ACpIW;;AAElC;AACA;AACA;AACA;AACA,SAASA,oBAAIA,CAAA,EAAG;EACd,MAAMuC,0BAA0B,GAAGtN,QAAQ,CAACyI,aAAa,CAAC,aAAa,CAAC;EAExE,IAAI6E,0BAA0B,EAAE;IAC9B,MAAMC,OAAO,GAAGvN,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC5CsN,OAAO,CAACzL,SAAS,CAACE,GAAG,CAAC,UAAU,CAAC;IACjCuL,OAAO,CAACzI,KAAK,GAAGC,KAAK,CAACC,IAAI,CACxBhF,QAAQ,CAACmI,gBAAgB,CACvB,yDACF,CACF,CAAC,CAACJ,GAAG,CAAEyF,OAAO,IAAK;MACjBA,OAAO,CAAClO,EAAE,GAAGgB,IAAI,CAACkN,OAAO,CAACC,WAAW,CAAC;MACtC,OAAO;QAAEhL,KAAK,EAAE+K,OAAO,CAACC,WAAW;QAAErK,GAAG,EAAE,GAAG,GAAGoK,OAAO,CAAClO;MAAG,CAAC;IAC9D,CAAC,CAAC;IAEFgO,0BAA0B,CAACI,KAAK,CAACH,OAAO,CAAC;EAC3C;AACF;AAEA,oDAAe;EAAExC,IAAIA,sBAAAA;AAAC,CAAC;;ACzB0B;AACD;;AAEhD;AACA;AACA;AACA,SAASA,oBAAIA,CAAA,EAAG;EACdrE,aAAY,CAACtH,OAAO,CAClB,wBAAwB,EACxB,YAAY,EACZ,IAAI,EACH2G,OAAO,IAAK;IACX+E,KAAK,CAAClE,gBAAgB,CAACb,OAAO,EAAGrE,QAAQ,IAAK;MAC5C,MAAMiM,QAAQ,GACZ5H,OAAO,CAAC6H,kBAAkB,CAACxN,OAAO,CAAC8I,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;MACzD,MAAM2E,WAAW,GAAG/C,KAAK,CAAC7B,kBAAkB,CAAC0E,QAAQ,CAAC;MAEtDjM,QAAQ,CAACgK,UAAU,CAACZ,KAAK,CAAClD,qBAAqB,CAACiG,WAAW,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ,CACF,CAAC;AACH;AAEA,oDAAe;EAAE9C,IAAIA,sBAAAA;AAAC,CAAC;;ACvBhB,MAAM+C,IAAI,GAAG,oUAAoU;AACjV,MAAMV,OAAO,GAAG,kVAAkV;AAClW,MAAMW,OAAO,GAAG,8XAA8X;AAC9Y,MAAMV,KAAK,GAAG,2TAA2T;AACzU,MAAMW,KAAK,GAAG,qOAAqO;AACnP,MAAMC,YAAY,GAAG,wPAAwP;AAC7Q,MAAMC,MAAM,GAAG,4YAA4Y;;ACNlX;AACS;AACR;AACP;AACS;AACtB;AAE7B,SAASnD,gBAAIA,CAAA,EAAG;EACdqD,eAAe,CAAC,CAAC;EACjBC,eAAe,CAAC,CAAC;AACnB;AAEA,SAASA,eAAeA,CAAA,EAAG;EACzB3H,aAAY,CAACtH,OAAO,CAAC,eAAe,EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,UAAUyF,CAAC,EAAE;IACtEA,CAAC,CAACpB,gBAAgB,CAAC,WAAW,EAAE,YAAY;MAC1C,IAAI,CAACgE,OAAO,CAAC,iBAAiB,CAAC,CAAC3F,SAAS,CAACE,GAAG,CAAC,OAAO,CAAC;IACxD,CAAC,CAAC;IACF6C,CAAC,CAACpB,gBAAgB,CAAC,UAAU,EAAE,YAAY;MACzC,IAAI,CAACgE,OAAO,CAAC,iBAAiB,CAAC,CAAC3F,SAAS,CAACI,MAAM,CAAC,OAAO,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASoM,sBAAsBA,CAACzJ,CAAC,EAAE;EACjC,IAAI0J,SAAS,GAAG1J,CAAC,CAACsD,gBAAgB,CAAC,uBAAuB,CAAC;EAC3DoG,SAAS,CAACtG,OAAO,CAAEuG,MAAM,IAAK;IAC5B,IAAIC,GAAG,GAAGzO,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC1CwO,GAAG,CAAC9B,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IAClC8B,GAAG,CAAC3M,SAAS,CAACE,GAAG,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACtDyM,GAAG,CAACC,SAAS,GAAGF,MAAM,CAACxE,YAAY,CAAC,OAAO,CAAC;IAC5C,IAAIwE,MAAM,CAACG,YAAY,CAAC,QAAQ,CAAC,EAAE;MACjCF,GAAG,CAAC9B,YAAY,CAAC,QAAQ,EAAE6B,MAAM,CAACxE,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC3D;IACA,IAAIuD,OAAO,GAAG1N,qBAAqB,CAACsO,YAAoB,CAAC;IACzDM,GAAG,CAACvG,WAAW,CAACqF,OAAO,CAAC;IACxBiB,MAAM,CAAC3M,UAAU,CAACqG,WAAW,CAACuG,GAAG,CAAC;IAClCD,MAAM,CAACtM,MAAM,CAAC,CAAC;EACjB,CAAC,CAAC;AACJ;AAEA,SAASkM,eAAeA,CAAA,EAAG;EACzB1H,aAAY,CAACtH,OAAO,CAClB,2BAA2B,EAC3B,iBAAiB,EACjB,CAAC,GAAG,EACJ,UAAUyF,CAAC,EAAE;IACX,IAAI+J,iBAAiB,CAAC/J,CAAC,CAAC,EAAE;MACxB;IACF;IAEAyJ,sBAAsB,CAACzJ,CAAC,CAAC;IACzB,IAAI4J,GAAG,GAAG1J,KAAK,CAACC,IAAI,CAACH,CAAC,CAACsD,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,CAAC0G,GAAG,CAAC,CAAC;IAExE,IAAIC,UAAU,GAAGjK,CAAC,CAACkK,gBAAgB;IACnC,OAAO,CAACD,UAAU,CAAChN,SAAS,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MACnD+M,UAAU,GAAGA,UAAU,CAACE,sBAAsB;IAChD;IACA,IAAIC,cAAc,GAAGH,UAAU,CAACE,sBAAsB,CAAC,CAAC;;IAExD,IAAIE,SAAS,GAAG,EAAE;IAClB,IAAIhG,QAAQ,GAAG4F,UAAU,CAAC5F,QAAQ;IAClC,KAAK,IAAIlK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkK,QAAQ,CAAC3E,MAAM,EAAEvF,CAAC,EAAE,EAAE;MACxC,IAAImQ,CAAC,GAAGjG,QAAQ,CAAClK,CAAC,CAAC;MACnB,IAAIoQ,IAAI,GAAGD,CAAC,CAACnF,YAAY,CAAC,MAAM,CAAC;MACjC,IAAIqF,YAAY,GAAGF,CAAC,CAACnF,YAAY,CAAC,cAAc,CAAC;MACjD,IAAIsF,KAAK,GAAGH,CAAC,CAACnF,YAAY,CAAC,OAAO,CAAC;MAEnCkF,SAAS,CAACvF,IAAI,CAAC;QACb7J,IAAI,EAAEqP,CAAC,CAACjP,SAAS;QACjBkP,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY;QAC1BC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;IACAR,UAAU,CAAC5M,MAAM,CAAC,CAAC;IACnB,IAAIqN,YAAY,GAAGC,wBAAwB,CAAC3K,CAAC,CAAC;IAE9C,SAAS4K,MAAMA,CAAC/N,QAAQ,EAAE3B,QAAQ,EAAE;MAClC,IAAI2P,EAAE,GAAG1P,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACtCyP,EAAE,CAACnE,SAAS,GAAG,wBAAwB;MACvCmE,EAAE,CAAC/C,YAAY,CAAC,MAAM,EAAE5M,QAAQ,CAACqP,IAAI,CAAC;MACtCM,EAAE,CAAC/C,YAAY,CAAC,cAAc,EAAE5M,QAAQ,CAACsP,YAAY,CAAC;MACtDK,EAAE,CAACxP,SAAS,GAAGH,QAAQ,CAACD,IAAI;MAE5B4B,QAAQ,CAACiG,IAAI,CAAC,CAAC;MAEfgI,cAAc,CACZD,EAAE,CAACjH,aAAa,CAAC,iBAAiB,CAAC,EACnC,YAAY;QACV,SAASmH,kBAAkBA,CAAA,EAAG;UAC5B;UACA;UACA;UACA;UACA;UACA,SAASC,gBAAgBA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAE;YAClD,SAASC,YAAYA,CAACC,GAAG,EAAE;cACzB,IAAIC,KAAK,GAAG,CAAC;cACb,KAAK,IAAInR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Q,OAAO,CAACxL,MAAM,EAAEvF,CAAC,EAAE,EAAE;gBACvC,IAAIA,CAAC,GAAGkR,GAAG,IAAIF,KAAK,CAACD,OAAO,CAAC/Q,CAAC,CAAC,CAAC,IAAIgR,KAAK,CAACF,QAAQ,CAAC,EAAE;kBACnDK,KAAK,EAAE;gBACT;cACF;cACA,OAAOA,KAAK;YACd;YAEA,IAAIC,SAAS,GAAG,CAAC,CAAC;YAClB,IAAIC,OAAO,GAAG,CAAC;YACf,KAAK,IAAIrR,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+Q,OAAO,CAACxL,MAAM,EAAEvF,CAAC,EAAE,EAAE;cACxC,IAAIsR,CAAC,GAAGL,YAAY,CAACjR,CAAC,CAAC;cACvB,IAAIoR,SAAS,IAAIE,CAAC,EAAE;gBAClB;gBACAF,SAAS,GAAGE,CAAC;gBACbD,OAAO,GAAGrR,CAAC;cACb;YACF;YACA,OAAOqR,OAAO;UAChB;UAEA,IAAIN,OAAO,GAAGhL,KAAK,CAACC,IAAI,CAACH,CAAC,CAACqE,QAAQ,CAAC,CAACN,MAAM,CAAC,UAAU/D,CAAC,EAAE;YACvD,OAAOA,CAAC,CAAC0L,OAAO,CAAC,oBAAoB,CAAC;UACxC,CAAC,CAAC;UAEF,SAASC,CAACA,CAACC,GAAG,EAAE;YACd,IAAIA,GAAG,YAAYC,OAAO,EAAE;cAC1BD,GAAG,GAAGA,GAAG,CAACzG,YAAY,CAAC,cAAc,CAAC;YACxC;YACA,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkQ,SAAS,CAAC3K,MAAM,EAAEvF,CAAC,EAAE,EAAE;cACzC,IAAIkQ,SAAS,CAAClQ,CAAC,CAAC,CAACqQ,YAAY,IAAIoB,GAAG,EAAE;gBACpC,OAAOzR,CAAC;cACV;YACF;YACA,OAAO,CAAC,CAAC,CAAC;UACZ;UAEA,IAAIqR,OAAO,GAAGR,gBAAgB,CAAC9P,QAAQ,CAACsP,YAAY,EAAEU,OAAO,EAAES,CAAC,CAAC;UACjE,IAAIH,OAAO,GAAGN,OAAO,CAACxL,MAAM,EAAE;YAC5B,OAAOwL,OAAO,CAACM,OAAO,CAAC;UACzB,CAAC,MAAM;YACL,OAAOpB,cAAc;UACvB;QACF;QACA,IAAI0B,aAAa,GAAG9L,CAAC,CAAC/C,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,GACnD6N,kBAAkB,CAAC,CAAC,GACpBX,cAAc;QAClB0B,aAAa,CAAC9O,UAAU,CAAC+O,YAAY,CAAClB,EAAE,EAAEiB,aAAa,CAAC;;QAExD;QACA,IAAIpB,YAAY,EAAE;UAChBC,wBAAwB,CAACE,EAAE,CAAC;QAC9B;QACAjQ,SAAS,CAACC,YAAY,CAACgQ,EAAE,EAAE,IAAI,CAAC;QAChCmB,aAAa,CAACnB,EAAE,CAAC;QACjBA,EAAE,CAAC5N,SAAS,CAACI,MAAM,CAAC,SAAS,CAAC;QAC9B4O,oBAAoB,CAACC,IAAI,CAAC,CAAC;MAC7B,CAAC,EACD,IACF,CAAC;IACH;IAEA,SAASC,GAAGA,CAAC1R,EAAE,EAAE;MACf,OACEuF,CAAC,CAAC4D,aAAa,CAAC,mCAAmC,GAAGnJ,EAAE,GAAG,IAAI,CAAC,IAChE,IAAI;IAER;IAEA,IAAI2R,OAAO,GAAGpM,CAAC,CAAC/C,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;IAE9CmP,gBAAgB,CAACzC,GAAG,EAAG/M,QAAQ,IAAK;MAClC,IAAIoG,SAAS,GAAG,EAAE;MAClB,KAAK,IAAI9I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkQ,SAAS,CAAC3K,MAAM,EAAEvF,CAAC,EAAE,EAAE;QACzC,IAAImQ,CAAC,GAAGD,SAAS,CAAClQ,CAAC,CAAC;QACpB,IAAIgF,QAAQ,GAAGiN,OAAO,IAAID,GAAG,CAAC7B,CAAC,CAACE,YAAY,CAAC;QAC7C,IAAI7M,IAAI,GAAGwB,QAAQ,GAAG,UAAU,GAAG,QAAQ;QAC3C,IAAId,IAAI,GAAG;UACTT,KAAK,EAAE0M,CAAC,CAACG,KAAK;UACd9L,OAAO,EAAGE,KAAK,IAAK;YAClBA,KAAK,CAAC6B,cAAc,CAAC,CAAC;YACtB7B,KAAK,CAACyN,eAAe,CAAC,CAAC;YACvB1B,MAAM,CAAC/N,QAAQ,EAAEyN,CAAC,CAAC;UACrB,CAAC;UACD3M,IAAI,EAAEA;QACR,CAAC;QACDsF,SAAS,CAAC6B,IAAI,CAACzG,IAAI,CAAC;MACtB;MACA,MAAMkO,aAAa,GAAGpR,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACnD,MAAMoR,IAAI,GAAGvG,KAAK,CAAClD,qBAAqB,CAACE,SAAS,EAAE,IAAI,CAAC;MACzDsJ,aAAa,CAAClJ,WAAW,CAACoJ,YAAY,CAACD,IAAI,CAAC,CAAC;MAC7CD,aAAa,CAAClJ,WAAW,CAACmJ,IAAI,CAAC;MAC/B3P,QAAQ,CAACgK,UAAU,CAAC0F,aAAa,CAAC;IACpC,CAAC,CAAC;EACJ,CACF,CAAC;AACH;AAEA,SAASE,YAAYA,CAACD,IAAI,EAAE;EAC1B,MAAME,WAAW,GAAG1R,qBAAqB,CAAC;AAC5C;AACA,GAAG,CAAC;EAEF0R,WAAW,CAAC9N,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAC1C8N,kBAAkB,CAACH,IAAI,EAAE3N,KAAK,CAAC+N,aAAa,CAC9C,CAAC;EACDF,WAAW,CAAC9N,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAKA,KAAK,CAACyN,eAAe,CAAC,CAAC,CAAC;EACzEI,WAAW,CAAC9N,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAK;IACjD,IAAIA,KAAK,CAAC0B,GAAG,KAAK,OAAO,EAAE;MACzB1B,KAAK,CAAC6B,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EAEF,MAAMmM,eAAe,GAAG7R,qBAAqB,CAAC;AAChD;AACA;AACA,UAAUsO,MAAc;AACxB;AACA;AACA,GAAG,CAAC;EACFuD,eAAe,CAACxJ,WAAW,CAACqJ,WAAW,CAAC;EACxC,OAAOG,eAAe;AACxB;AAEA,SAASF,kBAAkBA,CAACH,IAAI,EAAEE,WAAW,EAAE;EAC7C,MAAMI,aAAa,GAAG,CAACJ,WAAW,CAACK,KAAK,IAAI,EAAE,EAAEnR,WAAW,CAAC,CAAC;EAC7D,IAAIqE,KAAK,GAAGuM,IAAI,CAAClJ,gBAAgB,CAC/B,sDACF,CAAC;EACD,KAAK,IAAIjF,IAAI,IAAI4B,KAAK,EAAE;IACtB,IAAIlE,KAAK,GAAGsC,IAAI,CAACwL,SAAS,CAACjO,WAAW,CAAC,CAAC,CAAC4E,QAAQ,CAACsM,aAAa,CAAC;IAChEzO,IAAI,CAAC2O,KAAK,CAACC,OAAO,GAAGlR,KAAK,GAAG,aAAa,GAAG,MAAM;EACrD;AACF;AAEA,SAASsQ,gBAAgBA,CAACa,MAAM,EAAElL,QAAQ,EAAE;EAC1CJ,6BAAK,CACHsL,MAAM,EACNzP,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiE,SAAS,CAAC3F,QAAQ,CAAC,CAAC,EAAE;IACtCO,QAAQ,EAAEoD,SAAS;IACnB4C,QAAQA,CAAC1F,QAAQ,EAAE;MACjB,IAAIA,QAAQ,CAAC4F,MAAM,EAAE;QACnB;MACF;MACA5F,QAAQ,CAACsQ,MAAM,CAACvO,gBAAgB,CAAC,OAAO,EAAE,MAAM;QAC9C/B,QAAQ,CAACiG,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MACFjG,QAAQ,CAACsQ,MAAM,CAACvO,gBAAgB,CAAC,SAAS,EAAE,MAAM;QAChD,IAAIC,KAAK,CAAC0B,GAAG,KAAK,QAAQ,EAAE;UAC1B1D,QAAQ,CAACiG,IAAI,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC;IACDlG,MAAMA,CAACC,QAAQ,EAAE;MACfmF,QAAQ,CAACnF,QAAQ,CAAC;MAClBqQ,MAAM,CAAC5K,OAAO,CAAC8K,QAAQ,GAAG,MAAM;IAClC,CAAC;IACDhQ,MAAMA,CAAA,EAAG;MACP8P,MAAM,CAAC5K,OAAO,CAAC8K,QAAQ,GAAG,OAAO;IACnC;EACF,CAAC,CACH,CAAC;AACH;AAEA,gDAAe;EAAElH,IAAIA,kBAAAA;AAAC,CAAC;;ACtQyB;AACC;AAEjD,SAASA,cAAIA,CAAA,EAAG;EACd,SAASmH,uBAAuBA,CAACC,UAAU,EAAEtN,CAAC,EAAE;IAC9C,MAAM2H,OAAO,GAAGA,CAAA,KAAM;MACpB3H,CAAC,CAAC+M,KAAK,GAAGO,UAAU,CAAC/C,IAAI;MACzBtE,KAAK,CAACjB,gBAAgB,CAAChF,CAAC,CAAC;MACzBA,CAAC,CAACiB,KAAK,CAAC,CAAC;IACX,CAAC;IACD,OAAO;MACLrD,KAAK,EAAE0P,UAAU,CAAC/C,IAAI;MACtB5L,OAAO,EAAEgJ,OAAO;MAChB7I,UAAU,EAAGyE,GAAG,IAAK;QACnB,IAAIA,GAAG,CAAChD,GAAG,KAAK,KAAK,EAAE;UACrBoH,OAAO,CAAC,CAAC;UACT3H,CAAC,CAAChE,QAAQ,CAAC8G,IAAI,CAAC,CAAC;UACjBS,GAAG,CAAC7C,cAAc,CAAC,CAAC;QACtB;MACF;IACF,CAAC;EACH;EAEA,SAAS6M,qBAAqBA,CAACvN,CAAC,EAAEwN,GAAG,EAAEC,WAAW,EAAE;IAClD,MAAMxN,KAAK,GAAGwN,WAAW,CACtBC,MAAM,CAAC,CAAC,EAAEzH,KAAK,CAACP,qBAAqB,CAAC1F,CAAC,EAAE,EAAE,CAAC,CAAC,CAC7CkD,GAAG,CAAEyK,CAAC,IAAKN,uBAAuB,CAACM,CAAC,EAAE3N,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACA,CAAC,CAAChE,QAAQ,EAAE;MACfiK,KAAK,CAAClE,gBAAgB,CACpByL,GAAG,EACF3Q,QAAQ,IAAK;QACZmD,CAAC,CAAChE,QAAQ,GAAGa,QAAQ;MACvB,CAAC,EACD,IACF,CAAC;IACH;IACAmD,CAAC,CAAChE,QAAQ,CAAC6K,UAAU,CAACZ,KAAK,CAAClD,qBAAqB,CAAC9C,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/DD,CAAC,CAAChE,QAAQ,CAAC2H,IAAI,CAAC,CAAC;EACnB;EAEA,SAASiK,iBAAiBA,CAAC5N,CAAC,EAAEwN,GAAG,EAAEvN,KAAK,EAAE;IACxC,MAAMhC,IAAI,GAAG+B,CAAC,CAAC+M,KAAK,CAACzR,IAAI,CAAC,CAAC;IAE3B,IAAIuS,aAAa,GAAG5P,IAAI,GACpBgC,KAAK,CAAC8D,MAAM,CAAE1F,IAAI,IAAKA,IAAI,CAACjE,OAAO,CAAC6D,IAAI,CAAC,KAAK,CAAC,CAAC,GAChDgC,KAAK;IAET,MAAMwN,WAAW,GAAGI,aAAa,CAC9B9J,MAAM,CAAE1F,IAAI,IAAKA,IAAI,CAACjE,OAAO,CAAC6D,IAAI,CAAC,KAAK,CAAC,CAAC,CAC1CiF,GAAG,CAAE7E,IAAI,IAAK;MACb,OAAO;QAAEkM,IAAI,EAAElM;MAAK,CAAC;IACvB,CAAC,CAAC;IACJkP,qBAAqB,CAACvN,CAAC,EAAEwN,GAAG,EAAEC,WAAW,IAAI,EAAE,CAAC;EAClD;EAEA5L,aAAY,CAACtH,OAAO,CAAC,iBAAiB,EAAE,UAAU,EAAE,GAAG,EAAE,UAAUyF,CAAC,EAAE;IACpE;IACA;IACA8N,cAAc,CAAC9N,CAAC,EAAE,UAAU+N,MAAM,EAAE;MAClC,MAAMP,GAAG,GAAGrS,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACzC4E,CAAC,CAAChD,UAAU,CAAC+O,YAAY,CAACyB,GAAG,EAAExN,CAAC,CAAC+I,kBAAkB,CAAC;MACpD/I,CAAC,CAACgN,KAAK,CAACgB,QAAQ,GAAG,UAAU;MAE7B,MAAMzP,GAAG,GAAGyB,CAAC,CAACmF,YAAY,CAAC,SAAS,CAAC;MACrC2B,KAAK,CAACvI,GAAG,EAAE;QACT2J,OAAO,EAAEH,KAAK,CAACI,IAAI,CAAC;UAClB,cAAc,EAAE;QAClB,CAAC,CAAC;QACFjD,MAAM,EAAE,MAAM;QACd1I,IAAI,EAAE,IAAIyR,eAAe,CAACF,MAAM;MAClC,CAAC,CAAC,CACChH,IAAI,CAAEqB,GAAG,IAAMA,GAAG,CAACC,EAAE,GAAGD,GAAG,CAACnB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAE,CAAC,CACzCF,IAAI,CAAE9G,KAAK,IAAK;QACfD,CAAC,CAACpB,gBAAgB,CAAC,OAAO,EAAE,MAAMgP,iBAAiB,CAAC5N,CAAC,EAAEwN,GAAG,EAAEvN,KAAK,CAAC,CAAC;;QAEnE;QACA;QACAD,CAAC,CAACpB,gBAAgB,CAAC,UAAU,EAAE,MAC7BmH,UAAU,CAAC,MAAM/F,CAAC,CAAChE,QAAQ,CAAC8G,IAAI,CAAC,CAAC,EAAE,GAAG,CACzC,CAAC;QAED9C,CAAC,CAACpB,gBAAgB,CAChB,OAAO,EACPqH,KAAK,CAACJ,QAAQ,CAAC,MAAM;UACnB+H,iBAAiB,CAAC5N,CAAC,EAAEwN,GAAG,EAAEvN,KAAK,CAAC;QAClC,CAAC,CACH,CAAC;MACH,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,8CAAe;EAAEiG,IAAIA,gBAAAA;AAAC,CAAC;;AC5FyB;AACC;AAEjD,SAASA,iBAAIA,CAAA,EAAG;EACd,SAASgI,QAAQA,CAACnB,KAAK,EAAE1O,IAAI,EAAE8P,SAAS,EAAE;IACxC,MAAMC,IAAI,GAAGrB,KAAK,CAACvM,QAAQ,CAAC2N,SAAS,CAAC,GAClCpB,KAAK,CAAC1S,SAAS,CAAC,CAAC,EAAE0S,KAAK,CAACsB,WAAW,CAACF,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAC1D,EAAE;IACN,OAAOC,IAAI,GAAG/P,IAAI,GAAG8P,SAAS,GAAG,GAAG;EACtC;EAEA,SAASd,uBAAuBA,CAACC,UAAU,EAAEtN,CAAC,EAAE;IAC9C,MAAMmO,SAAS,GAAGnO,CAAC,CAACmF,YAAY,CAAC,uBAAuB,CAAC;IACzD,MAAMwC,OAAO,GAAGA,CAAA,KAAM;MACpB3H,CAAC,CAAC+M,KAAK,GAAGoB,SAAS,GACfD,QAAQ,CAAClO,CAAC,CAAC+M,KAAK,EAAEO,UAAU,CAAC/C,IAAI,EAAE4D,SAAS,CAAC,GAC7Cb,UAAU,CAAC/C,IAAI;MACnBtE,KAAK,CAACjB,gBAAgB,CAAChF,CAAC,CAAC;MACzBA,CAAC,CAACiB,KAAK,CAAC,CAAC;IACX,CAAC;IACD,OAAO;MACLrD,KAAK,EAAE0P,UAAU,CAAC/C,IAAI;MACtB5L,OAAO,EAAEgJ,OAAO;MAChB7I,UAAU,EAAGyE,GAAG,IAAK;QACnB,IAAIA,GAAG,CAAChD,GAAG,KAAK,KAAK,EAAE;UACrBoH,OAAO,CAAC,CAAC;UACT3H,CAAC,CAAChE,QAAQ,CAAC8G,IAAI,CAAC,CAAC;UACjBS,GAAG,CAAC7C,cAAc,CAAC,CAAC;QACtB;MACF;IACF,CAAC;EACH;EAEA,SAAS6M,qBAAqBA,CAACvN,CAAC,EAAEyN,WAAW,EAAE;IAC7C,MAAMxN,KAAK,GAAGwN,WAAW,CACtBC,MAAM,CAAC,CAAC,EAAEzH,KAAK,CAACP,qBAAqB,CAAC1F,CAAC,EAAE,EAAE,CAAC,CAAC,CAC7CkD,GAAG,CAAEyK,CAAC,IAAKN,uBAAuB,CAACM,CAAC,EAAE3N,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACA,CAAC,CAAChE,QAAQ,EAAE;MACfiK,KAAK,CAAClE,gBAAgB,CACpB/B,CAAC,EACAnD,QAAQ,IAAK;QACZmD,CAAC,CAAChE,QAAQ,GAAGa,QAAQ;QACrBA,QAAQ,CAACsQ,MAAM,CAACH,KAAK,CAACsB,QAAQ,GAAGtO,CAAC,CAACuO,WAAW,GAAG,IAAI;MACvD,CAAC,EACD,IACF,CAAC;IACH;IACAvO,CAAC,CAAChE,QAAQ,CAAC6K,UAAU,CAACZ,KAAK,CAAClD,qBAAqB,CAAC9C,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/DD,CAAC,CAAChE,QAAQ,CAAC2H,IAAI,CAAC,CAAC;EACnB;EAEA,SAASiK,iBAAiBA,CAAC5N,CAAC,EAAE;IAC5B,MAAM/B,IAAI,GAAG+B,CAAC,CAAC+M,KAAK,CAACzR,IAAI,CAAC,CAAC;IAC3B,MAAM6S,SAAS,GAAGnO,CAAC,CAACmF,YAAY,CAAC,uBAAuB,CAAC;IACzD,MAAMqJ,IAAI,GAAGL,SAAS,GAAGlQ,IAAI,CAACwQ,KAAK,CAACN,SAAS,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpT,IAAI,CAAC,CAAC,GAAG2C,IAAI;IACzE,IAAI,CAACuQ,IAAI,EAAE;MACT,IAAIxO,CAAC,CAAChE,QAAQ,EAAE;QACdgE,CAAC,CAAChE,QAAQ,CAAC8G,IAAI,CAAC,CAAC;MACnB;MACA;IACF;IAEA,MAAMvE,GAAG,GAAGyB,CAAC,CAACmF,YAAY,CAAC,iBAAiB,CAAC;IAE7C,MAAMwJ,OAAO,GAAG3O,CAAC,CAACmF,YAAY,CAAC,eAAe,CAAC;IAC/C,MAAMyJ,CAAC,GAAGC,EAAE,CAAC7O,CAAC,CAAC,CAAC8O,OAAO,CAAC,CAAC;IACzB,IAAIH,OAAO,IAAIA,OAAO,CAACjP,MAAM,GAAG,CAAC,EAAE;MACjCiP,OAAO,CAACF,KAAK,CAAC,GAAG,CAAC,CAACrL,OAAO,CACxB2L,OAAO,CAAC,UAAUzE,CAAC,EAAE;QACnBsE,CAAC,CAACI,MAAM,CAAC1E,CAAC,CAAC;MACb,CAAC,CACH,CAAC;IACH;IAEA,MAAM2E,WAAW,GAAGL,CAAC,CAACM,QAAQ,CAAC,CAAC;IAChC,MAAMC,GAAG,GAAGF,WAAW,CAAC7U,OAAO,CAAC,GAAG,CAAC;IACpC,MAAMgV,UAAU,GAAGH,WAAW,CAAC5U,SAAS,CAAC8U,GAAG,GAAG,CAAC,CAAC;IAEjDrI,KAAK,CAACvI,GAAG,EAAE;MACT2G,MAAM,EAAE,MAAM;MACdgD,OAAO,EAAEH,KAAK,CAACI,IAAI,CAAC;QAClB,cAAc,EAAE;MAClB,CAAC,CAAC;MACF3L,IAAI,EAAE4S;IACR,CAAC,CAAC,CACCrI,IAAI,CAAEqB,GAAG,IAAMA,GAAG,CAACC,EAAE,GAAGD,GAAG,CAACnB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAE,CAAC,CACzCF,IAAI,CAAEC,QAAQ,IAAKuG,qBAAqB,CAACvN,CAAC,EAAEgH,QAAQ,CAACyG,WAAW,IAAI,EAAE,CAAC,CAAC;EAC7E;EAEA5L,aAAY,CAACtH,OAAO,CAClB,qBAAqB,EACrB,qBAAqB,EACrB,CAAC,EACD,UAAUyF,CAAC,EAAE;IACXA,CAAC,CAAC8H,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC;IACrC9H,CAAC,CAACsC,OAAO,CAAC,aAAa,CAAC,GAAG,OAAO;IAClC;IACAtC,CAAC,CAACgN,KAAK,CAACgB,QAAQ,GAAG,UAAU;IAC7B;IACA;IACAhO,CAAC,CAACpB,gBAAgB,CAAC,UAAU,EAAE,MAC7BmH,UAAU,CAAC,MAAM/F,CAAC,CAAChE,QAAQ,IAAIgE,CAAC,CAAChE,QAAQ,CAAC8G,IAAI,CAAC,CAAC,EAAE,GAAG,CACvD,CAAC;IACD9C,CAAC,CAACpB,gBAAgB,CAChB,OAAO,EACPqH,KAAK,CAACJ,QAAQ,CAAC,MAAM;MACnB+H,iBAAiB,CAAC5N,CAAC,CAAC;IACtB,CAAC,CACH,CAAC;EACH,CACF,CAAC;AACH;AAEA,iDAAe;EAAEkG,IAAIA,mBAAAA;AAAC,CAAC;;ACjHkC;AACW;AACA;AACP;AACL;AACO;AAE/D,SAASA,cAAIA,CAAA,EAAG;EACdmJ,SAAS,CAACnJ,IAAI,CAAC,CAAC;EAChBoJ,eAAc,CAACpJ,IAAI,CAAC,CAAC;EACrBqJ,eAAc,CAACrJ,IAAI,CAAC,CAAC;EACrBsJ,WAAW,CAACtJ,IAAI,CAAC,CAAC;EAClBuJ,SAAQ,CAACvJ,IAAI,CAAC,CAAC;EACfwJ,YAAY,CAACxJ,IAAI,CAAC,CAAC;AACrB;AAEA,8CAAe;EAAEA,IAAIA,gBAAAA;AAAC,CAAC;;AChBhB,MAAMyJ,aAAa,GAAG,wVAAwV;AAC9W,MAAMC,IAAI,GAAG,6hBAA6hB;;ACD5gB;AACO;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAAC9B,MAAM,EAAE;EACjC,OAAO;IACLnQ,KAAK,EAAEmQ,MAAM,CAACnQ,KAAK;IACnBW,GAAG,EAAEwP,MAAM,CAACxP,GAAG;IACfuR,KAAK,EAAE/B,MAAM,CAAC+B,KAAK;IACnBC,MAAM,EAAEA,CAAA,KAAM;MACZ,OAAO,2DAA2DlU,SAAS,CACzEkS,MAAM,CAACxP,GACT,CAAC;AACP,UAAUwP,MAAM,CAACpQ,IAAI,KAAK,OAAO,GAAG,aAAa9B,SAAS,CAACkS,MAAM,CAACnQ,KAAK,CAAC,8EAA8EmQ,MAAM,CAACvP,IAAI,MAAM,GAAG,EAAE;AAC5K,UAAUuP,MAAM,CAACpQ,IAAI,KAAK,OAAO,GAAG,6DAA6DoQ,MAAM,CAACvP,IAAI,QAAQ,GAAG,EAAE;AACzH,UAAU3C,SAAS,CAACkS,MAAM,CAACnQ,KAAK,CAAC;AACjC,UAAUmQ,MAAM,CAACiC,UAAU,GAAG1G,aAAqB,GAAG,EAAE;AACxD,SAAS;IACL;EACF,CAAC;AACH;;AC5BA;AACA;AACA;AACA,SAAS2G,MAAMA,CAACC,UAAU,EAAE;EAC1B,MAAMC,OAAO,GAAGhV,QAAQ,CAACiV,cAAc,CAAC,6BAA6B,CAAC,CAAC9N,OAAO,CAC3E+N,SAAS;EACZ,OAAOvJ,KAAK,CAAC,GAAGqJ,OAAO,UAAUG,kBAAkB,CAACJ,UAAU,CAAC,EAAE,CAAC;AACpE;AAEA,+CAAe;EAAED,MAAM,EAAEA;AAAO,CAAC;;ACTK;AACJ;AAE3B,MAAMO,mBAAmB,GAAG;EACjCC,OAAOA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGxV,QAAQ,CAACyV,IAAI,CAACtO,OAAO,CAACuO,OAAO;IAE7C,SAASC,cAAcA,CAACvS,GAAG,EAAE;MAC3B,IAAIA,GAAG,CAACwS,UAAU,CAAC,GAAG,CAAC,EAAE;QACvBxS,GAAG,GAAGA,GAAG,CAAClE,SAAS,CAAC,CAAC,CAAC;MACxB;MAEA,OAAOsW,OAAO,GAAG,GAAG,GAAGpS,GAAG;IAC5B;IAEA,OAAOgS,UAAM,CAACN,MAAM,CAACS,KAAK,CAAC,CAAC3J,IAAI,CAAEqB,GAAG,IACnCA,GAAG,CAACnB,IAAI,CAAC,CAAC,CAACF,IAAI,CAAEiK,IAAI,IAAK;MACxB,OAAOA,IAAI,CAAC,aAAa,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC/N,GAAG,CAAElD,CAAC,IACvC6P,UAAU,CAAC;QACTrR,IAAI,EAAEwB,CAAC,CAACxB,IAAI;QACZb,IAAI,EAAEqC,CAAC,CAACrC,IAAI;QACZC,KAAK,EAAEoC,CAAC,CAACuK,IAAI;QACbhM,GAAG,EAAEuS,cAAc,CAAC9Q,CAAC,CAACzB,GAAG,CAAC;QAC1BuR,KAAK,EAAE9P,CAAC,CAAC8P;MACX,CAAC,CACH,CAAC;IACH,CAAC,CACH,CAAC;EACH;AACF,CAAC;;;;;AC7BD;AACA;AACA;AACO,SAASoB,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,OAAOA,KAAK,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;IACjC,IAAIA,GAAG,CAACxB,KAAK,KAAKnQ,SAAS,EAAE;MAC3B,OAAO0R,IAAI;IACb;IACA,OAAO5T,MAAM,CAACC,MAAM,CAAC2T,IAAI,EAAE;MACzB,CAACC,GAAG,CAACxB,KAAK,GAAG,CAACuB,IAAI,CAACC,GAAG,CAACxB,KAAK,CAAC,IAAI,EAAE,EAAEyB,MAAM,CAACD,GAAG;IACjD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;ACZiE;AACb;AACb;AACF;AACe;AACR;AACO;AACyB;AAE5E,MAAME,WAAW,GAAG,CAAChB,mBAAmB,CAAC;AAEzC,SAAStK,oBAAIA,CAAA,EAAG;EACd,MAAMuL,IAAI,GAAGtW,QAAQ,CAACiV,cAAc,CAAC,sBAAsB,CAAC;EAC5D,MAAMsB,0BAA0B,GAAGvW,QAAQ,CAACiV,cAAc,CACxD,6BACF,CAAC;EACD,IAAIsB,0BAA0B,KAAK,IAAI,EAAE;IACvC,OAAO,CAAC;EACV;EACA,MAAMC,cAAc,GAAGxW,QAAQ,CAACiV,cAAc,CAAC,iBAAiB,CAAC;EACjE,MAAMwB,qBAAqB,GAAGD,cAAc,CAAC/N,aAAa,CACxD,mCACF,CAAC;EACD,MAAMiO,mBAAmB,GAAG1W,QAAQ,CAACiV,cAAc,CAAC,aAAa,CAAC;EAClE,MAAM0B,gCAAgC,GAAGH,cAAc,CAAC/N,aAAa,CACnE,kCACF,CAAC;EACD,MAAMmO,aAAa,GAAG5W,QAAQ,CAACiV,cAAc,CAAC,gBAAgB,CAAC;EAC/D,MAAM4B,sBAAsB,GAAG7W,QAAQ,CAACiV,cAAc,CACpD,0BACF,CAAC;EAED,MAAM6B,UAAU,GAAG,+CAA+C;EAElE7S,qBAAqB,CACnB4S,sBAAsB,EACtB,MAAMD,aAAa,CAACzO,gBAAgB,CAAC,GAAG,CAAC,EACzC2O,UAAU,EACV,MAAM,CAAC,CAAC,EACR,MAAMN,cAAc,CAACO,IACvB,CAAC;;EAED;EACAR,0BAA0B,CAAC9S,gBAAgB,CAAC,OAAO,EAAE,YAAY;IAC/D,IAAI+S,cAAc,CAAC7H,YAAY,CAAC,MAAM,CAAC,EAAE;MACvCqI,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM;MACLC,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;EAEFR,qBAAqB,CAAChT,gBAAgB,CAAC,OAAO,EAAE,UAAUoB,CAAC,EAAE;IAC3D,IAAIA,CAAC,CAAC2C,MAAM,KAAK3C,CAAC,CAAC4M,aAAa,EAAE;MAChC;IACF;IAEAuF,kBAAkB,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,SAASE,aAAaA,CAAA,EAAG;IACvB,MAAM3B,KAAK,GAAGmB,mBAAmB,CAAC9E,KAAK;IACvC,IAAIuF,OAAO;IAEX,IAAI5B,KAAK,CAAChR,MAAM,KAAK,CAAC,EAAE;MACtB4S,OAAO,GAAGC,OAAO,CAACC,GAAG,CAAC,CACpB3C,UAAU,CAAC;QACTrR,IAAI,EAAE8K,IAAY;QAClB3L,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE6T,IAAI,CAACnP,OAAO,CAACmQ,OAAO;QAC3BlU,GAAG,EAAEmT,0BAA0B,CAACpP,OAAO,CAACoQ,aAAa;QACrD1C,UAAU,EAAE,IAAI;QAChBF,KAAK,EAAE;MACT,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,MAAM;MACLwC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAAChB,WAAW,CAACtO,GAAG,CAAEyP,EAAE,IAAKA,EAAE,CAAClC,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC3J,IAAI,CACnE/G,CAAC,IAAKA,CAAC,CAAC4S,IAAI,CAAC,CAChB,CAAC;IACH;IAEAN,OAAO,CAACvL,IAAI,CAAEuL,OAAO,IAAK;MACxBA,OAAO,GAAGpB,sBAAsB,CAACoB,OAAO,CAAC;;MAEzC;MACAP,aAAa,CAAC1W,SAAS,GAAG,EAAE;MAE5B,IAAIqV,KAAK,CAAChR,MAAM,KAAK,CAAC,IAAIjC,MAAM,CAACoV,IAAI,CAACP,OAAO,CAAC,CAAC5S,MAAM,GAAG,CAAC,EAAE;QACzD,KAAK,MAAM,CAACoQ,KAAK,EAAE7P,KAAK,CAAC,IAAIxC,MAAM,CAACqV,OAAO,CAACR,OAAO,CAAC,EAAE;UACpD,IAAIxC,KAAK,KAAK,MAAM,EAAE;YACpB,MAAM9Q,OAAO,GAAG7D,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;YAC3C4D,OAAO,CAAC0H,SAAS,GAAG,2CAA2C;YAC/D1H,OAAO,CAAC6K,SAAS,GAAGiG,KAAK;YACzBiC,aAAa,CAACgB,MAAM,CAAC/T,OAAO,CAAC;UAC/B;UAEAiB,KAAK,CAACmD,OAAO,CAAC,UAAUkO,GAAG,EAAE;YAC3B,MAAMjL,IAAI,GAAGrL,qBAAqB,CAACsW,GAAG,CAACvB,MAAM,CAAC,CAAC,CAAC;YAChD1J,IAAI,CAACzH,gBAAgB,CAAC,YAAY,EAAGoB,CAAC,IAAKgT,cAAc,CAAChT,CAAC,CAAC,CAAC;YAC7D+R,aAAa,CAACgB,MAAM,CAAC1M,IAAI,CAAC;UAC5B,CAAC,CAAC;QACJ;QAEA4M,kBAAkB,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACL,MAAMrV,KAAK,GAAGzC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACzCwC,KAAK,CAAC8I,SAAS,GAAG,+BAA+B;QACjD9I,KAAK,CAACvC,SAAS,GACb,QAAQ,GACRoW,IAAI,CAACnP,OAAO,CAAC4Q,YAAY,GACzB,UAAU,GACVrX,SAAS,CAACgW,mBAAmB,CAAC9E,KAAK,CAAC;QACtCgF,aAAa,CAACgB,MAAM,CAACnV,KAAK,CAAC;MAC7B;MAEAoU,sBAAsB,CAAChF,KAAK,CAACmG,MAAM,GAAGpB,aAAa,CAACqB,YAAY,GAAG,IAAI;MACvEC,gBAAgB,CAACC,MAAM,CAAC,CAAC;MACzBxB,gCAAgC,CAAC7U,SAAS,CAACI,MAAM,CAC/C,yBACF,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,MAAMgW,gBAAgB,GAAGxN,kBAAQ,CAAC,MAAM;IACtCiM,gCAAgC,CAAC7U,SAAS,CAACE,GAAG,CAAC,yBAAyB,CAAC;EAC3E,CAAC,EAAE,GAAG,CAAC;EAEP,MAAMoW,aAAa,GAAG1N,kBAAQ,CAAC,MAAM;IACnCwM,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,GAAG,CAAC;EAEPR,mBAAmB,CAACjT,gBAAgB,CAAC,OAAO,EAAE,MAAM;IAClDyU,gBAAgB,CAAC,CAAC;IAClBE,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;;EAEF;EACA,SAASnB,kBAAkBA,CAAA,EAAG;IAC5BT,cAAc,CAAC6B,SAAS,CAAC,CAAC;IAC1B3B,mBAAmB,CAAC5Q,KAAK,CAAC,CAAC;IAC3B4Q,mBAAmB,CAAC4B,iBAAiB,CAAC,CAAC,EAAE5B,mBAAmB,CAAC9E,KAAK,CAACrN,MAAM,CAAC;IAE1E2S,aAAa,CAAC,CAAC;EACjB;EAEA,SAASF,kBAAkBA,CAAA,EAAG;IAC5BR,cAAc,CAAC7J,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;IAE1C6J,cAAc,CAAC/S,gBAAgB,CAC7B,cAAc,EACd,MAAM;MACJ+S,cAAc,CAAC+B,eAAe,CAAC,SAAS,CAAC;MACzC/B,cAAc,CAACgC,KAAK,CAAC,CAAC;IACxB,CAAC,EACD;MAAEC,IAAI,EAAE;IAAK,CACf,CAAC;EACH;EAEA,SAASZ,cAAcA,CAAC3U,IAAI,EAAE;IAC5B,IAAIwV,YAAY,GAAG1Y,QAAQ,CAACyI,aAAa,CAAC,GAAG,GAAGqO,UAAU,CAAC;IAC3D,IAAI4B,YAAY,EAAE;MAChBA,YAAY,CAAC5W,SAAS,CAACI,MAAM,CAAC4U,UAAU,CAAC;IAC3C;IAEA5T,IAAI,CAACsE,MAAM,CAAC1F,SAAS,CAACE,GAAG,CAAC8U,UAAU,CAAC;EACvC;EAEA,SAASgB,kBAAkBA,CAACa,KAAK,EAA0B;IAAA,IAAxB9S,cAAc,GAAAvB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACvD,MAAMsU,SAAS,GAAGhC,aAAa,CAACiC,oBAAoB,CAAC,GAAG,CAAC,CAACtU,MAAM;IAChE,MAAMuU,WAAW,GAAG9Y,QAAQ,CAACyI,aAAa,CAAC,GAAG,GAAGqO,UAAU,CAAC;IAE5D,IAAIgC,WAAW,EAAE;MACfA,WAAW,CAAChX,SAAS,CAACI,MAAM,CAAC4U,UAAU,CAAC;IAC1C;IAEA,IAAI6B,KAAK,GAAGC,SAAS,EAAE;MACrB,MAAM7S,OAAO,GAAGhB,KAAK,CAACC,IAAI,CAAC4R,aAAa,CAACiC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CACjEF,KAAK,CACN;MACD5S,OAAO,CAACjE,SAAS,CAACE,GAAG,CAAC8U,UAAU,CAAC;MAEjC,IAAIjR,cAAc,EAAE;QAClBE,OAAO,CAACF,cAAc,CAAC,CAAC;MAC1B;IACF;EACF;AACF;AAEA,oDAAe;EAAEkF,IAAIA,sBAAAA;AAAC,CAAC;;AC3LmB;AACS;AAEnD,SAASA,kBAAIA,CAAA,EAAG;EACdrG,MAAM,CAACyI,eAAe,GAAG;IACvB4L,OAAO,EAAE,CAAC;IACVC,KAAK,EAAE,IAAI;IAAE;IACb3G,GAAG,EAAE,IAAI;IAAE;IACX4G,KAAK,EAAE,IAAI;IAAE;IACbC,WAAW,EAAE/K,IAAY;IACzBgL,iBAAiB,EAAE,sBAAsB;IAEzC/L,OAAO,EAAE;MACPgM,UAAU,EAAE,oDAAoD;MAChE/V,IAAI,EAAE8K,OAAef;IACvB,CAAC;IACDW,OAAO,EAAE;MACPqL,UAAU,EAAE,oDAAoD;MAChE/V,IAAI,EAAE8K,OAAeJ;IACvB,CAAC;IACDV,KAAK,EAAE;MACL+L,UAAU,EAAE,kDAAkD;MAC9D/V,IAAI,EAAE8K,KAAa;MACnBkL,MAAM,EAAE;IACV,CAAC;IAEDtO,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAI,IAAI,CAACsH,GAAG,IAAI,IAAI,EAAE;QACpB,IAAI,CAACA,GAAG,GAAGrS,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACxC,IAAI,CAACoS,GAAG,CAAC/S,EAAE,GAAG,kBAAkB;QAChCU,QAAQ,CAACqB,IAAI,CAACuP,YAAY,CAAC,IAAI,CAACyB,GAAG,EAAErS,QAAQ,CAACqB,IAAI,CAAChB,iBAAiB,CAAC;QACrE,MAAMiZ,IAAI,GAAG,IAAI;QACjB,IAAI,CAACjH,GAAG,CAACkH,OAAO,GAAG,YAAY;UAC7BD,IAAI,CAAC3R,IAAI,CAAC,CAAC;QACb,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAC0K,GAAG,CAACnS,SAAS,GAAG,EAAE;MACzB;IACF,CAAC;IACD;IACAsZ,YAAY,EAAE,SAAAA,CAAA,EAAY;MACxB,IAAI,IAAI,CAACP,KAAK,EAAE;QACdvU,MAAM,CAAC8U,YAAY,CAAC,IAAI,CAACP,KAAK,CAAC;MACjC;MACA,IAAI,CAACA,KAAK,GAAG,IAAI;IACnB,CAAC;IACD;IACAtR,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAI,CAAC6R,YAAY,CAAC,CAAC;MACnB,IAAI,CAACnH,GAAG,CAACvQ,SAAS,CAACI,MAAM,CAAC,+BAA+B,CAAC;MAC1D,IAAI,CAACmQ,GAAG,CAACvQ,SAAS,CAACE,GAAG,CAAC,8BAA8B,CAAC;IACxD,CAAC;IACD;IACAwG,IAAI,EAAE,SAAAA,CAAU1F,IAAI,EAAEV,OAAO,EAAE;MAC7BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;MACvB,IAAI,CAAC2I,IAAI,CAAC,CAAC;MAEX,IAAI,CAACsH,GAAG,CAACnK,WAAW,CAClBrI,qBAAqB,CAACuC,OAAO,CAACiB,IAAI,IAAI,IAAI,CAAC6V,WAAW,CACxD,CAAC;MACD,MAAMzM,OAAO,GAAG,IAAI,CAAC4F,GAAG,CAACnK,WAAW,CAAClI,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAAC;MACpEwM,OAAO,CAACvE,WAAW,CAAClI,QAAQ,CAACyZ,cAAc,CAAC3W,IAAI,CAAC,CAAC;MAElD,IAAI,CAACuP,GAAG,CAAC9G,SAAS,GAAGnJ,OAAO,CAACgX,UAAU,IAAI,IAAI,CAACD,iBAAiB;MACjE,IAAI,CAAC9G,GAAG,CAACvQ,SAAS,CAACE,GAAG,CAAC,+BAA+B,CAAC;MAEvD,IAAI,CAACwX,YAAY,CAAC,CAAC;MACnB,MAAMF,IAAI,GAAG,IAAI;MACjB,IAAI,CAAClX,OAAO,CAACiX,MAAM,EAAE;QACnB,IAAI,CAACJ,KAAK,GAAGvU,MAAM,CAACkG,UAAU,CAAC,YAAY;UACzC0O,IAAI,CAAC3R,IAAI,CAAC,CAAC;QACb,CAAC,EAAE,IAAI,CAACqR,KAAK,CAAC;MAChB;IACF;EACF,CAAC;AACH;AAEA,kDAAe;EAAEjO,IAAIA,oBAAAA;AAAC,CAAC;;AC7E4B;AACC;AACR;AAE5C,MAAM2O,cAAc,GAAG,kCAAkC;AAEzD,SAAS3O,eAAIA,CAAA,EAAG;EACd,MAAM4O,eAAe,GAAG3Z,QAAQ,CAACmI,gBAAgB,CAAC,wBAAwB,CAAC;EAE3EpD,KAAK,CAACC,IAAI,CAAC2U,eAAe,CAAC,CACxB/Q,MAAM,CAAEgR,SAAS,IAAKA,SAAS,CAACtH,WAAW,CAAC,CAC5CrK,OAAO,CAAE2R,SAAS,IAAK;IACtB,MAAMC,aAAa,GAAGD,SAAS,CAACE,aAAa,CAACA,aAAa;IAC3D,MAAMjD,sBAAsB,GAAGhX,qBAAqB,CAClD,uDACF,CAAC;IACDga,aAAa,CAAC3R,WAAW,CAAC2O,sBAAsB,CAAC;IACjD,MAAMD,aAAa,GAAG/W,qBAAqB,CACzC,sCACF,CAAC;IACDgX,sBAAsB,CAAC3O,WAAW,CAAC0O,aAAa,CAAC;IAEjDgD,SAAS,CAACnW,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACxC,MAAM8R,KAAK,GAAGqE,SAAS,CAAChI,KAAK,CAACnR,WAAW,CAAC,CAAC;;MAE3C;MACA,IAAI8U,KAAK,CAAChR,MAAM,KAAK,CAAC,EAAE;QACtBwV,oBAAoB,CAAC,CAAC;QACtB;MACF;MAEAC,oBAAoB,CAAC,CAAC;MAEtB,SAASC,aAAaA,CAAC/V,SAAS,EAAEiT,OAAO,EAAE;QACzCA,OAAO,CAAClP,OAAO,CAAC,CAAC/E,IAAI,EAAEyV,KAAK,KAAK;UAC/BzU,SAAS,CAACgE,WAAW,CACnBrI,qBAAqB,CACnB,oCAAoC8Y,KAAK,KAAK,CAAC,GAAGe,cAAc,GAAG,EAAE,WACnExW,IAAI,CAACE,GAAG,+CACqCF,IAAI,CAACG,IAAI,SAAS3C,SAAS,CAACwC,IAAI,CAACT,KAAK,CAAC,MACxF,CACF,CAAC;QACH,CAAC,CAAC;QAEF,IAAI0U,OAAO,CAAC5S,MAAM,KAAK,CAAC,IAAIL,SAAS,KAAK0S,aAAa,EAAE;UACvD1S,SAAS,CAACgE,WAAW,CACnBrI,qBAAqB,CACnB,qEACF,CACF,CAAC;QACH;MACF;;MAEA;MACA,MAAMsX,OAAO,GAAGyC,SAAS,CACtBtH,WAAW,CAAC,CAAC,CACb1J,MAAM,CAAE1F,IAAI,IAAKA,IAAI,CAACT,KAAK,CAAChC,WAAW,CAAC,CAAC,CAAC4E,QAAQ,CAACkQ,KAAK,CAAC,CAAC,CAC1DO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAEdc,aAAa,CAAC1W,SAAS,GAAG,EAAE;MAC5B+Z,aAAa,CAACrD,aAAa,EAAEO,OAAO,CAAC;MACrCN,sBAAsB,CAAChF,KAAK,CAACmG,MAAM,GAAGpB,aAAa,CAACqB,YAAY,GAAG,IAAI;IACzE,CAAC,CAAC;IAEF,SAAS+B,oBAAoBA,CAAA,EAAG;MAC9BnD,sBAAsB,CAAC/U,SAAS,CAACE,GAAG,CAClC,4CACF,CAAC;IACH;IAEA,SAAS+X,oBAAoBA,CAAA,EAAG;MAC9BlD,sBAAsB,CAAC/U,SAAS,CAACI,MAAM,CACrC,4CACF,CAAC;MACD2U,sBAAsB,CAAChF,KAAK,CAACmG,MAAM,GAAG,KAAK;IAC7C;IAEA4B,SAAS,CAACnW,gBAAgB,CAAC,SAAS,EAAGoB,CAAC,IAAK;MAC3C,IAAIA,CAAC,CAACO,GAAG,KAAK,SAAS,IAAIP,CAAC,CAACO,GAAG,KAAK,WAAW,EAAE;QAChDP,CAAC,CAACU,cAAc,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEFtB,qBAAqB,CACnB4S,sBAAsB,EACtB,MAAMD,aAAa,CAACzO,gBAAgB,CAAC,GAAG,CAAC,EACzCuR,cACF,CAAC;;IAED;IACA;IACA;IACA,IAAI,CAAChV,MAAM,CAACwV,WAAW,EAAE;MACvB,IAAIC,cAAc,CAAC,MAAM;QACvBtD,sBAAsB,CAAChF,KAAK,CAACmG,MAAM,GACjCpB,aAAa,CAACqB,YAAY,GAAG,IAAI;MACrC,CAAC,CAAC,CAACmC,OAAO,CAACxD,aAAa,CAAC;IAC3B;IAEAgD,SAAS,CAACnW,gBAAgB,CAAC,SAAS,EAAE,MAAM;MAC1C,IAAImW,SAAS,CAAChI,KAAK,CAACrN,MAAM,KAAK,CAAC,EAAE;QAChCsS,sBAAsB,CAAChF,KAAK,CAACmG,MAAM,GACjCpB,aAAa,CAACqB,YAAY,GAAG,IAAI;QACnC+B,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IAEFha,QAAQ,CAACyD,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MAC5C,IAAImW,aAAa,CAAC9X,QAAQ,CAAC2B,KAAK,CAAC8D,MAAM,CAAC,EAAE;QACxC;MACF;MAEAuS,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACN;AAEA,+CAAe;EAAEhP,IAAIA,iBAAAA;AAAC,CAAC;;ACrHM;AACmB;AAEhD,MAAMsP,YAAY,GAAG;EACnBnZ,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,SAAS;EAChBI,SAAS,EAAE,SAAS;EACpBC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8Y,eAAeA,CAACvU,OAAO,EAAE;EAChC,IAAIA,OAAO,CAACgB,MAAM,IAAIhB,OAAO,CAACgB,MAAM,CAACC,KAAK,CAAC7F,KAAK,KAAK,SAAS,EAAE;IAC9D4E,OAAO,CAACgB,MAAM,CAACE,OAAO,CAAC,CAAC;EAC1B;EAEA,MAAMlE,OAAO,GAAGgD,OAAO,CAACiE,YAAY,CAAC,SAAS,CAAC;EAC/C,MAAMuQ,WAAW,GAAGxU,OAAO,CAACiE,YAAY,CAAC,mBAAmB,CAAC;EAC7D,MAAMwQ,KAAK,GAAGzU,OAAO,CAACiE,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC;EAC7D,IAAI5I,QAAQ,GAAGpB,QAAQ,CAACqB,IAAI;EAC5B,IAAI0E,OAAO,CAAC4I,YAAY,CAAC,+BAA+B,CAAC,EAAE;IACzDvN,QAAQ,GAAG,QAAQ;EACrB;EACA,IACE2B,OAAO,KAAK,IAAI,IAChBA,OAAO,CAAC5C,IAAI,CAAC,CAAC,CAACoE,MAAM,GAAG,CAAC,KACxBgW,WAAW,KAAK,IAAI,IAAIA,WAAW,CAACpa,IAAI,CAAC,CAAC,CAACoE,MAAM,IAAI,CAAC,CAAC,EACxD;IACAkC,6BAAK,CACHV,OAAO,EACPzD,MAAM,CAACC,MAAM,CACX;MACEnC,OAAO,EAAEA,CAAA,KAAM2C,OAAO,CAACvC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC;MACzD4G,QAAQA,CAAC1F,QAAQ,EAAE;QACjBA,QAAQ,CAACE,SAAS,CAAC+K,YAAY,CAAC,OAAO,EAAEjL,QAAQ,CAACsF,KAAK,CAAC5G,OAAO,CAAC;MAClE,CAAC;MACDqB,MAAMA,CAACC,QAAQ,EAAE;QACfA,QAAQ,CAACE,SAAS,CAAC2W,eAAe,CAAC,OAAO,CAAC;MAC7C,CAAC;MACDkC,QAAQA,CAAC/Y,QAAQ,EAAE;QACjBA,QAAQ,CAACE,SAAS,CAAC+K,YAAY,CAAC,OAAO,EAAEjL,QAAQ,CAACsF,KAAK,CAAC5G,OAAO,CAAC;MAClE,CAAC;MACDgB,QAAQ,EAAEA,QAAQ;MAClBoZ,KAAK,EAAE,CAACA,KAAK,EAAE,IAAI;IACrB,CAAC,EACDH,YACF,CACF,CAAC;EACH;EAEA,IAAIE,WAAW,KAAK,IAAI,IAAIA,WAAW,CAACpa,IAAI,CAAC,CAAC,CAACoE,MAAM,GAAG,CAAC,EAAE;IACzDkC,6BAAK,CACHV,OAAO,EACPzD,MAAM,CAACC,MAAM,CACX;MACEnC,OAAO,EAAEA,CAAA,KAAMma,WAAW;MAC1BvZ,SAAS,EAAE,IAAI;MACfoG,QAAQA,CAAC1F,QAAQ,EAAE;QACjBA,QAAQ,CAACsF,KAAK,CAAClG,WAAW,GACxBY,QAAQ,CAACE,SAAS,CAACoI,YAAY,CAAC,0BAA0B,CAAC,KAC3D,MAAM;MACV,CAAC;MACD5I,QAAQ,EAAEA,QAAQ;MAClBoZ,KAAK,EAAE,CAACA,KAAK,EAAE,IAAI;IACrB,CAAC,EACDH,YACF,CACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASK,iBAAiBA,CAAC5X,IAAI,EAAEiD,OAAO,EAAE;EACxC,MAAMhD,OAAO,GAAG0D,6BAAK,CACnBV,OAAO,EACPzD,MAAM,CAACC,MAAM,CACX;IACExB,OAAO,EAAE,OAAO;IAChBO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdlB,OAAO,EAAE0C,IAAI;IACbrB,MAAMA,CAACC,QAAQ,EAAE;MACfkJ,UAAU,CAAC,MAAM;QACflJ,QAAQ,CAACiG,IAAI,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EACD0S,YACF,CACF,CAAC;EACDtX,OAAO,CAACyF,IAAI,CAAC,CAAC;AAChB;AAEA,SAASuC,aAAIA,CAAA,EAAG;EACdrE,aAAY,CAACtH,OAAO,CAClB,gCAAgC,EAChC,WAAW,EACX,IAAI,EACH2G,OAAO,IAAK;IACXuU,eAAe,CAACvU,OAAO,CAAC;EAC1B,CACF,CAAC;EAEDrB,MAAM,CAACgW,iBAAiB,GAAGA,iBAAiB;AAC9C;AAEA,6CAAe;EAAE3P,IAAIA,eAAAA;AAAC,CAAC;;AClHyB;AAEhD,SAAS4P,kBAAkBA,CAACzP,IAAI,EAAE;EAChC,IAAI0P,QAAQ,GAAG1P,IAAI,CAAClB,YAAY,CAAC,cAAc,CAAC;EAChD,IAAI5G,GAAG,GAAG8H,IAAI,CAAClB,YAAY,CAAC,MAAM,CAAC;EACnCkB,IAAI,CAACzH,gBAAgB,CAAC,OAAO,EAAE,UAAUoB,CAAC,EAAE;IAC1CA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB,IAAI+P,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB3J,KAAK,CAACvI,GAAG,EAAE;QACT2G,MAAM,EAAE,MAAM;QACdgD,OAAO,EAAEH,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI4N,QAAQ,IAAI,IAAI,EAAE;MACpBrO,MAAM,CAACC,OAAO,CAACoO,QAAQ,CAAC,CAAChP,IAAI,CAAC,MAAM;QAClC0J,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;AACJ;AAEA,SAASvK,qBAAIA,CAAA,EAAG;EACdrE,aAAY,CAACtH,OAAO,CAClB,mBAAmB,EACnB,kBAAkB,EAClB,CAAC,EACA2G,OAAO,IAAK;IACX4U,kBAAkB,CAAC5U,OAAO,CAAC;EAC7B,CACF,CAAC;AACH;AAEA,qDAAe;EAAEgF,IAAIA,uBAAAA;AAAC,CAAC;;AClCyB;AAEhD,SAAS8P,wBAAwBA,CAAC9U,OAAO,EAAE;EACzC,MAAMsG,IAAI,GAAGtG,OAAO,CAACiE,YAAY,CAAC,WAAW,CAAC,KAAK,MAAM;EACzD,MAAMwB,IAAI,GAAGzF,OAAO,CAACiE,YAAY,CAAC,UAAU,CAAC;EAC7C,MAAMyC,OAAO,GAAG1G,OAAO,CAACiE,YAAY,CAAC,cAAc,CAAC;EACpD,MAAMsF,KAAK,GAAGvJ,OAAO,CAACiE,YAAY,CAAC,YAAY,CAAC;EAChD,MAAM8Q,WAAW,GAAG/U,OAAO,CAACiE,YAAY,CAAC,kBAAkB,CAAC;EAC5D,IAAIxH,IAAI,GAAG,SAAS;EACpB,IAAIsY,WAAW,KAAK,MAAM,EAAE;IAC1BtY,IAAI,GAAG,aAAa;EACtB;EAEAuD,OAAO,CAACtC,gBAAgB,CAAC,OAAO,EAAE,UAAUoB,CAAC,EAAE;IAC7CA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBgH,MAAM,CAACC,OAAO,CAAC8C,KAAK,EAAE;MAAE7C,OAAO,EAAEA,OAAO;MAAEjK,IAAI,EAAEA;IAAK,CAAC,CAAC,CAACoJ,IAAI,CAC1D,MAAM;MACJ,IAAIc,IAAI,GAAG1M,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MACzCyM,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAEN,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;MAClDK,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAEnB,IAAI,CAAC;MACjC,IAAIa,IAAI,EAAE;QACRO,KAAK,CAACC,YAAY,CAACH,IAAI,CAAC;MAC1B;MACA1M,QAAQ,CAACqB,IAAI,CAAC6G,WAAW,CAACwE,IAAI,CAAC;MAC/BA,IAAI,CAACI,MAAM,CAAC,CAAC;IACf,CAAC,EACD,MAAM,CAAC,CACT,CAAC;IACD,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AAEA,SAAS/B,sBAAIA,CAAA,EAAG;EACdrE,aAAY,CAACtH,OAAO,CAClB,qBAAqB,EACrB,mBAAmB,EACnB,CAAC,EACA2G,OAAO,IAAK;IACX8U,wBAAwB,CAAC9U,OAAO,CAAC;EACnC,CACF,CAAC;AACH;AAEA,sDAAe;EAAEgF,IAAIA,wBAAAA;AAAC,CAAC;;;;;;;;;;AC3CvB;AACA;AACA;AACuB;AACQ;AACK;AAEpC,IAAImQ,KAAK,GAAG,KAAK;AACjB,IAAIC,OAAO,GAAG,CAAC,CAAC;;AAEhB;AACAA,OAAO,CAACC,OAAO,GAAG,YAAY;EAC5B,IAAIC,CAAC,GAAGN,gBAAC,CAAC,MAAM,CAAC,CAACO,IAAI,CAAC,cAAc,CAAC;EACtC,IAAI,CAACD,CAAC,EAAE;IACNA,CAAC,GAAG,EAAE;EACR;EACA,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACAF,OAAO,CAACI,IAAI,GAAG,UAAUnY,GAAG,EAAE;EAC5B4X,uBAAY,CAAC,CAAC,CAACS,QAAQ,CAACjb,OAAO,CAAC2a,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGhY,GAAG,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACA+X,OAAO,CAACO,GAAG,GAAG,UAAUtY,GAAG,EAAEuY,OAAO,EAAEvZ,OAAO,EAAE;EAC7C,IAAI8Y,KAAK,EAAE;IACT7Q,OAAO,CAAC6B,GAAG,CAAC,OAAO,GAAG9I,GAAG,CAAC;EAC5B;EACA,IAAIwY,IAAI,GAAG;IACTxY,GAAG,EAAE+X,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGhY,GAAG;IAC5BZ,IAAI,EAAE,KAAK;IACXqZ,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,MAAM;IAChBH,OAAO,EAAEA;EACX,CAAC;EACD,IAAIvZ,OAAO,YAAYE,MAAM,EAAE;IAC7ByY,uBAAQ,CAACa,IAAI,EAAExZ,OAAO,CAAC;EACzB;EACA2Y,qBAAM,CAACa,IAAI,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACA;AACAT,OAAO,CAAC9O,IAAI,GAAG,UAAUjJ,GAAG,EAAEyS,IAAI,EAAE8F,OAAO,EAAEvZ,OAAO,EAAE;EACpD,IAAI8Y,KAAK,EAAE;IACT7Q,OAAO,CAAC6B,GAAG,CAAC,QAAQ,GAAG9I,GAAG,CAAC;EAC7B;;EAEA;EACA,IAAI2J,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIkP,GAAG,GAAGjB,uBAAY,CAAC,CAAC;EACxB,IAAIpO,KAAK;EACT,IAAI,OAAO,IAAIxK,OAAO,EAAE;IACtBwK,KAAK,GAAGxK,OAAO,CAACwK,KAAK;EACvB,CAAC,MAAM,IAAI,OAAO,IAAIqP,GAAG,EAAE;IACzBrP,KAAK,GAAGqP,GAAG,CAACrP,KAAK;EACnB;EAEA,IAAIA,KAAK,EAAE;IACTG,OAAO,CAACH,KAAK,CAACsP,SAAS,CAAC,GAAGtP,KAAK,CAACgF,KAAK;EACxC;EAEA,IAAIuK,QAAQ,GAAGtG,IAAI;EACnB,IAAIsG,QAAQ,YAAY7Z,MAAM,EAAE;IAC9B,IAAIsK,KAAK,EAAE;MACTuP,QAAQ,GAAGpB,uBAAQ,CAAC,CAAC,CAAC,EAAEoB,QAAQ,CAAC;MACjCA,QAAQ,CAACvP,KAAK,CAACsP,SAAS,CAAC,GAAGtP,KAAK,CAACgF,KAAK;IACzC;IACAuK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC;EACrC;EAEA,IAAIP,IAAI,GAAG;IACTxY,GAAG,EAAE+X,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGhY,GAAG;IAC5BZ,IAAI,EAAE,MAAM;IACZqZ,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,MAAM;IAChBjG,IAAI,EAAEsG,QAAQ;IACdG,WAAW,EAAE,kBAAkB;IAC/BX,OAAO,EAAEA,OAAO;IAChB5O,OAAO,EAAEA;EACX,CAAC;EACD,IAAI3K,OAAO,YAAYE,MAAM,EAAE;IAC7ByY,uBAAQ,CAACa,IAAI,EAAExZ,OAAO,CAAC;EACzB;EACA2Y,qBAAM,CAACa,IAAI,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACAT,OAAO,CAACoB,cAAc,GAAG,YAAY;EACnC,OAAOtB,mBAAU;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACAE,OAAO,CAACqB,gBAAgB,GAAG,UAAUC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACjExB,OAAO,CAACO,GAAG,CAAC,gCAAgC,GAAGe,UAAU,EAAE,UAAUG,GAAG,EAAE;IACxE,IAAIA,GAAG,CAACC,MAAM,KAAK,IAAI,EAAE;MACvB,IAAIF,OAAO,EAAE;QACXA,OAAO,CAACC,GAAG,CAACnQ,OAAO,CAAC;MACtB;MACA,MAAM,oCAAoC,GAAGmQ,GAAG,CAACnQ,OAAO;IAC1D;IAEA,IAAIqQ,YAAY,GAAGF,GAAG,CAAC/G,IAAI;IAE3B,IAAI,WAAW,KAAK,OAAOkH,KAAK,EAAE;MAChCD,YAAY,GAAG,IAAIC,KAAK,CAACD,YAAY,EAAE;QACrCpB,GAAG,EAAE,SAAAA,CAAUlU,MAAM,EAAEwV,QAAQ,EAAE;UAC/B,IAAIA,QAAQ,IAAIxV,MAAM,EAAE;YACtB,OAAOA,MAAM,CAACwV,QAAQ,CAAC;UACzB;UACA,IAAI9B,KAAK,EAAE;YACT7Q,OAAO,CAAC6B,GAAG,CAAC,GAAG,GAAG8Q,QAAQ,GAAG,oCAAoC,CAAC;UACpE;UACA,OAAOA,QAAQ;QACjB;MACF,CAAC,CAAC;IACJ;IAEAN,OAAO,CAACI,YAAY,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA3B,OAAO,CAAC8B,gBAAgB,GAAG,UAAUC,MAAM,EAAER,OAAO,EAAE;EACpD;EACA,IAAIO,gBAAgB,GAAG,SAAAA,CAAA,EAAY;IACjC9B,OAAO,CAACO,GAAG,CACT,wCAAwC,GAAGwB,MAAM,EACjD,UAAUrR,QAAQ,EAAE;MAClB,IAAIA,QAAQ,CAACgR,MAAM,KAAK,IAAI,EAAE;QAC5BH,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE7Q,QAAQ,CAACY,OAAO,CAAC;MACxC;;MAEA;MACA;MACA,IAAI0Q,iBAAiB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;MAC7D,IACEA,iBAAiB,CAACle,OAAO,CAAC4M,QAAQ,CAACgK,IAAI,CAACuH,UAAU,CAAC,IAAI,CAAC,IACxDD,iBAAiB,CAACle,OAAO,CAAC4M,QAAQ,CAACgK,IAAI,CAACwH,QAAQ,CAAC,IAAI,CAAC,EACtD;QACAzS,UAAU,CAACqS,gBAAgB,EAAE,GAAG,CAAC;MACnC,CAAC,MAAM;QACL;QACA;QACA,IACEpR,QAAQ,CAACgR,MAAM,KAAK,IAAI,IACxBhR,QAAQ,CAACgK,IAAI,CAACuH,UAAU,KAAK,IAAI,IAChCvR,QAAQ,CAACgK,IAAI,CAACwH,QAAQ,KAAK,IAAI,IAC9BxR,QAAQ,CAACgK,IAAI,CAACwH,QAAQ,KAAK,SAAU,EACvC;UACA;UACAX,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;QACvB,CAAC,MAAM;UACLA,OAAO,CAAC,IAAI,CAAC;QACf;MACF;IACF,CAAC,EACD;MACEzQ,KAAK,EAAE,SAAAA,CAAUqR,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAC7C,IAAIF,GAAG,CAACT,MAAM,KAAK,GAAG,EAAE;UACtB1B,OAAO,CAACI,IAAI,CAAC,QAAQ,CAAC;QACxB,CAAC,MAAM;UACLmB,OAAO,CAAC3L,IAAI,CAAC;YAAE0M,OAAO,EAAE,IAAI;YAAEC,YAAY,EAAEF;UAAY,CAAC,CAAC;QAC5D;MACF;IACF,CACF,CAAC;EACH,CAAC;EACDP,gBAAgB,CAAC,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA9B,OAAO,CAACK,SAAS,GAAG,UAAUmC,KAAK,EAAE;EACnCA,KAAK,GAAG5C,gBAAC,CAAC4C,KAAK,CAAC;EAChB,IAAI1B,GAAG,GAAGjB,uBAAY,CAAC,CAAC;EACxBD,gBAAC,CAAC7U,GAAG,CAAClG,QAAQ,CAAC,CACZkF,IAAI,CAAC,QAAQ,CAAC,CACd0Y,IAAI,CAAC,YAAY;IAChB,IAAIC,WAAW,GAAG,IAAI,CAACC,aAAa;IACpC,IAAIC,EAAE,GAAGhD,gBAAC,CAAC,IAAI,CAAC,CAAC/S,QAAQ,CAAC,CAAC,CAAC9C,IAAI,CAAC,MAAM,CAAC;IACxC6Y,EAAE,CAACH,IAAI,CAAC,YAAY;MAClB,IAAID,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACrB1B,GAAG,GAAG4B,WAAW;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACJ,OAAO5B,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACAd,OAAO,CAAC6C,aAAa,GAAG,UAAUL,KAAK,EAAE;EACvCA,KAAK,GAAG5C,gBAAC,CAAC4C,KAAK,CAAC;EAChB,IAAI1B,GAAG,GAAGd,OAAO,CAACK,SAAS,CAACmC,KAAK,CAAC;EAClC,IAAIjR,IAAI,GAAGiR,KAAK,CAAC,CAAC,CAAC;EACnB,IAAI1B,GAAG,CAACgC,aAAa,CAACvR,IAAI,CAAC,EAAE;IAC3B,OACEiR,KAAK,CAACO,SAAS,CAAC,CAAC,GACjB,GAAG,GACHnD,sBAAO,CAAC;MACN,YAAY,EAAE,EAAE;MAChBqD,MAAM,EAAE,MAAM;MACdtS,IAAI,EAAE6R,KAAK,CAACzY,IAAI,CAAC,kBAAkB,CAAC,CAACmZ,GAAG,CAAC;IAC3C,CAAC,CAAC;EAEN;EACA,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACAlD,OAAO,CAACmD,YAAY,GAAG,UAAUX,KAAK,EAAE;EACtCA,KAAK,GAAG5C,gBAAC,CAAC4C,KAAK,CAAC;EAChB,IAAI1B,GAAG,GAAGd,OAAO,CAACK,SAAS,CAACmC,KAAK,CAAC;EAClC,OAAO1B,GAAG,CAACrP,KAAK;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACAuO,OAAO,CAACoD,WAAW,GAAG,UAAUnb,GAAG,EAAEua,KAAK,EAAEhC,OAAO,EAAEvZ,OAAO,EAAE;EAC5Dub,KAAK,GAAG5C,gBAAC,CAAC4C,KAAK,CAAC;EAChB,IAAIa,QAAQ,GAAGrD,OAAO,CAAC6C,aAAa,CAACL,KAAK,CAAC;EAC3C,IAAI/Q,KAAK,GAAGuO,OAAO,CAACmD,YAAY,CAACX,KAAK,CAAC;EACvCxC,OAAO,CAAC9O,IAAI,CACVjJ,GAAG,EACHob,QAAQ,EACR7C,OAAO,EACPZ,uBAAQ,CACN;IACE0D,WAAW,EAAE,KAAK;IAClBnC,WAAW,EAAE,mCAAmC;IAChD1P,KAAK,EAAEA;EACT,CAAC,EACDxK,OACF,CACF,CAAC;AACH,CAAC;AAED,iDAAe+Y,OAAO;;ACnQ6B;AACZ;AACS;AACX;AAErC,IAAIuD,SAAS,GAAG;EACdpP,KAAK,EAAE,IAAI;EACX7C,OAAO,EAAE,IAAI;EACb0L,MAAM,EAAE,IAAI;EACZwG,QAAQ,EAAE,OAAO;EACjBxL,QAAQ,EAAE,OAAO;EACjB3Q,IAAI,EAAE,SAAS;EACfoc,eAAe,EAAE,KAAK;EACtBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;AAChB,CAAC;AAED,IAAIC,aAAa,GAAG;EAClBC,OAAO,EAAE,EAAE;EACXlE,WAAW,EAAE;AACf,CAAC;AAEDK,YAAO,CAACqB,gBAAgB,CAAC,iBAAiB,EAAE,UAAUyC,aAAa,EAAE;EACnEva,MAAM,CAAC6H,MAAM,CAACuQ,YAAY,GAAGmC,aAAa;EAC1CP,SAAS,CAACQ,UAAU,GAAGD,aAAa,CAAC9G,MAAM;EAC3CuG,SAAS,CAACS,MAAM,GAAGF,aAAa,CAAC/R,EAAE;AACrC,CAAC,CAAC;AAEF,SAASkS,MAAMA,CAACC,UAAU,EAAEjd,OAAO,EAAE;EACnC,IAAI,CAACid,UAAU,GAAGA,UAAU;EAC5B,IAAI,CAACjd,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEmc,SAAS,EAAEtc,OAAO,CAAC;EACpD,IAAI,CAAC2I,IAAI,CAAC,CAAC;AACb;AAEAqU,MAAM,CAACE,SAAS,CAACvU,IAAI,GAAG,YAAY;EAClC,IAAI,CAACwB,MAAM,GAAGvM,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9C,IAAI,CAACsM,MAAM,CAACzK,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;EAC3C,IAAI,CAACuK,MAAM,CAACsF,KAAK,CAAC8M,QAAQ,GAAG,IAAI,CAACvc,OAAO,CAACuc,QAAQ;EAClD,IAAI,CAACpS,MAAM,CAACsF,KAAK,CAACsB,QAAQ,GAAG,IAAI,CAAC/Q,OAAO,CAAC+Q,QAAQ;EAClDnT,QAAQ,CAACqB,IAAI,CAAC6G,WAAW,CAAC,IAAI,CAACqE,MAAM,CAAC;EAEtC,IAAI,IAAI,CAACnK,OAAO,CAACkN,KAAK,IAAI,IAAI,EAAE;IAC9B,MAAMA,KAAK,GAAGzP,qBAAqB,CAAC,sCAAsC,CAAC;IAC3E,IAAI,CAAC0M,MAAM,CAACrE,WAAW,CAACoH,KAAK,CAAC;IAC9BA,KAAK,CAACZ,SAAS,GAAG,IAAI,CAACtM,OAAO,CAACkN,KAAK;EACtC;EAEA,IAAI,IAAI,CAAC+P,UAAU,KAAK,OAAO,EAAE;IAC/B,IAAI,IAAI,CAACjd,OAAO,CAAChC,OAAO,IAAI,IAAI,EAAE;MAChC,MAAMA,OAAO,GAAGP,qBAAqB,CACnC,yEACF,CAAC;MACDO,OAAO,CAAC8H,WAAW,CAAC,IAAI,CAAC9F,OAAO,CAAChC,OAAO,CAAC;MACzC,IAAI,CAACmM,MAAM,CAACrE,WAAW,CAAC9H,OAAO,CAAC;IAClC;IACA,IAAI,IAAI,CAACgC,OAAO,CAACwc,eAAe,KAAK,IAAI,EAAE;MACzC,MAAMW,WAAW,GAAG1f,qBAAqB,CAAC;AAChD;AACA;AACA,cAAcmO,KAAK;AACnB;AACA,SAAS,CAAC;MACJ,IAAI,CAACzB,MAAM,CAACrE,WAAW,CAACqX,WAAW,CAAC;MACpCA,WAAW,CAAC9b,gBAAgB,CAAC,OAAO,EAAE,MACpC,IAAI,CAAC8I,MAAM,CAACiT,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAC/C,CAAC;IACH;IACA,IAAI,CAAClT,MAAM,CAAC9I,gBAAgB,CAAC,OAAO,EAAE,UAAUoB,CAAC,EAAE;MACjD,IAAIA,CAAC,CAAC2C,MAAM,KAAK3C,CAAC,CAAC4M,aAAa,EAAE;QAChC;MACF;MACA,IAAI,CAAC+N,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAACvS,EAAE,GAAG,IAAI;EAChB,CAAC,MAAM;IACL,IAAI,CAACR,IAAI,GAAG,IAAI;IAChB,IAAI,IAAI,CAACtK,OAAO,CAACsK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC2S,UAAU,KAAK,MAAM,EAAE;MAC3D,MAAMrX,QAAQ,GAAGnI,qBAAqB,CACpC,yCACF,CAAC;MACD,IAAI,CAAC6M,IAAI,GAAG,IAAI,CAACtK,OAAO,CAACsK,IAAI;MAC7B1E,QAAQ,CAACE,WAAW,CAAC,IAAI,CAAC9F,OAAO,CAACsK,IAAI,CAAC;MACvC,IAAI,CAACH,MAAM,CAACrE,WAAW,CAACF,QAAQ,CAAC;MACjCtB,aAAY,CAAChH,YAAY,CAACsI,QAAQ,EAAE,IAAI,CAAC;IAC3C;IACA,IAAI,IAAI,CAAC5F,OAAO,CAACqK,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC4S,UAAU,KAAK,MAAM,EAAE;MAC9D,MAAM5S,OAAO,GAAG5M,qBAAqB,CACnC,yCACF,CAAC;MACD,IAAI,CAAC0M,MAAM,CAACrE,WAAW,CAACuE,OAAO,CAAC;MAChCA,OAAO,CAACiC,SAAS,GAAG,IAAI,CAACtM,OAAO,CAACqK,OAAO;IAC1C;IAEA,IAAI,IAAI,CAAC4S,UAAU,KAAK,QAAQ,EAAE;MAChC,IAAIK,QAAQ,GAAG7f,qBAAqB,CAAC;AAC3C,0EAA0E,CAAC;MACrE,IAAI,CAAC0M,MAAM,CAACrE,WAAW,CAACwX,QAAQ,CAAC;MACjC,IAAI,CAACC,KAAK,GAAGD,QAAQ,CAACjX,aAAa,CAAC,iBAAiB,CAAC;MACtD,IAAI,CAAC,IAAI,CAACrG,OAAO,CAACyc,UAAU,EAAE;QAC5B,IAAI,CAACc,KAAK,CAAClc,gBAAgB,CAAC,OAAO,EAAE,MAAM,IAAI,CAACmc,UAAU,CAAC,CAAC,CAAC;MAC/D;IACF;IAEA,IAAI,CAACC,aAAa,CAAC,CAAC;IAEpB,IAAI,CAACtT,MAAM,CAAC9I,gBAAgB,CAAC,SAAS,EAAGoB,CAAC,IAAK;MAC7C,IAAIA,CAAC,CAACO,GAAG,KAAK,OAAO,EAAE;QACrBP,CAAC,CAACU,cAAc,CAAC,CAAC;QAClB,IAAI,IAAI,CAAC2H,EAAE,CAAClJ,QAAQ,IAAI,KAAK,EAAE;UAC7B,IAAI,CAACkJ,EAAE,CAACsS,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3C;MACF;MACA,IAAI5a,CAAC,CAACO,GAAG,KAAK,QAAQ,EAAE;QACtBP,CAAC,CAACU,cAAc,CAAC,CAAC;QAClB,IAAI,CAACgH,MAAM,CAACiT,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAEDL,MAAM,CAACE,SAAS,CAACM,UAAU,GAAG,YAAY;EACxC,IAAI,IAAI,CAACD,KAAK,CAAC/N,KAAK,CAACzR,IAAI,CAAC,CAAC,EAAE;IAC3B,IAAI,CAAC+M,EAAE,CAAClJ,QAAQ,GAAG,KAAK;EAC1B,CAAC,MAAM;IACL,IAAI,CAACkJ,EAAE,CAAClJ,QAAQ,GAAG,IAAI;EACzB;AACF,CAAC;AAEDob,MAAM,CAACE,SAAS,CAACO,aAAa,GAAG,YAAY;EAC3C,MAAMC,OAAO,GAAGjgB,qBAAqB,CAAC;AACxC;AACA,mCACQ,IAAI,CAACuC,OAAO,CAAC0c,YAAY,GAAG,QAAQ,GAAG,QAAQ,mDAE/CC,aAAa,CAAC,IAAI,CAAC3c,OAAO,CAACI,IAAI,CAAC,KAC7B,IAAI,CAACJ,OAAO,CAAC+c,MAAM;AAC9B,wDACQ,IAAI,CAAC/c,OAAO,CAAC8c,UAAU;AAC/B,WACW,CAAC;EAEV,IAAI,IAAI,CAACG,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAAC3S,IAAI,CAACxE,WAAW,CAAC4X,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,IAAI,CAACvT,MAAM,CAACrE,WAAW,CAAC4X,OAAO,CAAC;EAClC;EAEA,IAAI,CAAC5S,EAAE,GAAG4S,OAAO,CAACrX,aAAa,CAAC,cAAc,CAAC;EAC/C,IAAI,CAAC0P,MAAM,GAAG2H,OAAO,CAACrX,aAAa,CAAC,kBAAkB,CAAC;EACvD,IAAI,CAAC,IAAI,CAACrG,OAAO,CAAC+V,MAAM,EAAE;IACxB,IAAI,CAACA,MAAM,CAACtG,KAAK,CAACC,OAAO,GAAG,MAAM;EACpC,CAAC,MAAM;IACL,IAAI,CAACqG,MAAM,CAAC1U,gBAAgB,CAAC,OAAO,EAAGoB,CAAC,IAAK;MAC3CA,CAAC,CAACU,cAAc,CAAC,CAAC;MAClB,IAAI,CAACgH,MAAM,CAACiT,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC,CAAC;EACJ;EACA,IAAI,IAAI,CAACJ,UAAU,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACjd,OAAO,CAACyc,UAAU,EAAE;IAC5D,IAAI,CAAC3R,EAAE,CAAClJ,QAAQ,GAAG,IAAI;EACzB;AACF,CAAC;AAEDob,MAAM,CAACE,SAAS,CAAC9W,IAAI,GAAG,YAAY;EAClC,OAAO,IAAI4O,OAAO,CAAC,CAAC2I,OAAO,EAAE5H,MAAM,KAAK;IACtC,IAAI,CAAC5L,MAAM,CAAC8L,SAAS,CAAC,CAAC;IACvB,IAAI,CAAC9L,MAAM,CAAC9I,gBAAgB,CAC1B,QAAQ,EACPoB,CAAC,IAAK;MACLA,CAAC,CAACU,cAAc,CAAC,CAAC;MAClB,IAAI,CAACgH,MAAM,CAACrK,MAAM,CAAC,CAAC;MACpBiW,MAAM,CAAC,CAAC;IACV,CAAC,EACD;MAAEM,IAAI,EAAE;IAAK,CACf,CAAC;IACD,IAAI,CAAClM,MAAM,CAACzG,KAAK,CAAC,CAAC;IACnB,IAAI,IAAI,CAAC6Z,KAAK,IAAI,IAAI,EAAE;MACtB,IAAI,CAACA,KAAK,CAAC7Z,KAAK,CAAC,CAAC;IACpB;IACA,IACE,IAAI,CAACoH,EAAE,IAAI,IAAI,KACd,IAAI,CAACmS,UAAU,IAAI,MAAM,IAAI,CAAC,IAAI,CAACjd,OAAO,CAAC0c,YAAY,CAAC,EACzD;MACA,IAAI,CAAC5R,EAAE,CAACzJ,gBAAgB,CACtB,OAAO,EACNoB,CAAC,IAAK;QACLA,CAAC,CAACU,cAAc,CAAC,CAAC;QAElB,IAAIqM,KAAK,GAAG,IAAI;QAChB,IAAI,IAAI,CAACyN,UAAU,KAAK,QAAQ,EAAE;UAChCzN,KAAK,GAAG,IAAI,CAAC+N,KAAK,CAAC/N,KAAK;QAC1B;QACA,IAAI,IAAI,CAACyN,UAAU,KAAK,MAAM,EAAE;UAC9BzN,KAAK,GAAG,IAAIoO,QAAQ,CAAC,IAAI,CAACtT,IAAI,CAAC;QACjC;QACA,IAAI,CAACH,MAAM,CAACrK,MAAM,CAAC,CAAC;QACpB6d,OAAO,CAACnO,KAAK,CAAC;MAChB,CAAC,EACD;QAAE6G,IAAI,EAAE;MAAK,CACf,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AAED,SAAS1N,YAAIA,CAAA,EAAG;EACdrG,MAAM,CAAC6H,MAAM,GAAG;IACd0T,KAAK,EAAE,SAAAA,CAAU7f,OAAO,EAAEgC,OAAO,EAAE;MACjC,MAAM8d,QAAQ,GAAG;QACf9f,OAAO,EAAEA;MACX,CAAC;MACDgC,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2d,QAAQ,EAAE9d,OAAO,CAAC;MAC9C,IAAImK,MAAM,GAAG,IAAI6S,MAAM,CAAC,OAAO,EAAEhd,OAAO,CAAC;MACzCmK,MAAM,CACH/D,IAAI,CAAC,CAAC,CACNoD,IAAI,CAAC,CAAC,CACNI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;IAEDmU,KAAK,EAAE,SAAAA,CAAU7Q,KAAK,EAAElN,OAAO,EAAE;MAC/B,MAAM8d,QAAQ,GAAG;QACf5Q,KAAK,EAAEA,KAAK;QACZ6I,MAAM,EAAE;MACV,CAAC;MACD/V,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2d,QAAQ,EAAE9d,OAAO,CAAC;MAC9C,IAAImK,MAAM,GAAG,IAAI6S,MAAM,CAAC,OAAO,EAAEhd,OAAO,CAAC;MACzCmK,MAAM,CACH/D,IAAI,CAAC,CAAC,CACNoD,IAAI,CAAC,CAAC,CACNI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;IAEDQ,OAAO,EAAE,SAAAA,CAAU8C,KAAK,EAAElN,OAAO,EAAE;MACjC,MAAM8d,QAAQ,GAAG;QACf5Q,KAAK,EAAEA,KAAK;QACZ6P,MAAM,EAAEza,MAAM,CAAC6H,MAAM,CAACuQ,YAAY,CAACsD;MACrC,CAAC;MACDhe,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2d,QAAQ,EAAE9d,OAAO,CAAC;MAC9C,IAAImK,MAAM,GAAG,IAAI6S,MAAM,CAAC,SAAS,EAAEhd,OAAO,CAAC;MAC3C,OAAOmK,MAAM,CAAC/D,IAAI,CAAC,CAAC;IACtB,CAAC;IAED6X,MAAM,EAAE,SAAAA,CAAU/Q,KAAK,EAAElN,OAAO,EAAE;MAChC,MAAM8d,QAAQ,GAAG;QACf5Q,KAAK,EAAEA;MACT,CAAC;MACDlN,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2d,QAAQ,EAAE9d,OAAO,CAAC;MAC9C,IAAImK,MAAM,GAAG,IAAI6S,MAAM,CAAC,QAAQ,EAAEhd,OAAO,CAAC;MAC1C,OAAOmK,MAAM,CAAC/D,IAAI,CAAC,CAAC;IACtB,CAAC;IAEDkE,IAAI,EAAE,SAAAA,CAAUA,IAAI,EAAEtK,OAAO,EAAE;MAC7B,MAAM8d,QAAQ,GAAG;QACfxT,IAAI,EAAEA,IAAI;QACVyG,QAAQ,EAAE,OAAO;QACjBwL,QAAQ,EAAE,OAAO;QACjBG,YAAY,EAAE,IAAI;QAClBK,MAAM,EAAEza,MAAM,CAAC6H,MAAM,CAACuQ,YAAY,CAAChQ;MACrC,CAAC;MACD1K,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2d,QAAQ,EAAE9d,OAAO,CAAC;MAC9C,IAAImK,MAAM,GAAG,IAAI6S,MAAM,CAAC,MAAM,EAAEhd,OAAO,CAAC;MACxC,OAAOmK,MAAM,CAAC/D,IAAI,CAAC,CAAC;IACtB;EACF,CAAC;AACH;AAEA,4CAAe;EAAEuC,IAAIA,cAAAA;AAAC,CAAC;;ACxQwB;AACW;AACH;AACP;AACH;AACc;AACG;AACnB;AAE3CuV,SAAS,CAACvV,IAAI,CAAC,CAAC;AAChBwV,eAAc,CAACxV,IAAI,CAAC,CAAC;AACrByV,aAAa,CAACzV,IAAI,CAAC,CAAC;AACpB0V,UAAS,CAAC1V,IAAI,CAAC,CAAC;AAChB2V,QAAQ,CAAC3V,IAAI,CAAC,CAAC;AACf4V,gBAAc,CAAC5V,IAAI,CAAC,CAAC;AACrB6V,iBAAgB,CAAC7V,IAAI,CAAC,CAAC;AACvB8V,OAAO,CAAC9V,IAAI,CAAC,CAAC;;;;;;UChBd;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA;WACA;WACA,kBAAkB,qBAAqB;WACvC,oHAAoH,iDAAiD;WACrK;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC7BA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD,8CAA8C;;;;;WCA9C;;;;;WCAA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,8CAA8C;;WAE9C;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,iCAAiC,mCAAmC;WACpE;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;WClDA;;;;;UEAA;UACA;UACA;UACA,8EAA8E,mCAAmC;UACjH", "sources": ["webpack://jenkins-ui/./src/main/js/util/path.js", "webpack://jenkins-ui/./src/main/js/util/behavior-shim.js", "webpack://jenkins-ui/./src/main/js/util/dom.js", "webpack://jenkins-ui/./src/main/js/util/security.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/templates.js", "webpack://jenkins-ui/./src/main/js/util/keyboard.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/utils.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/jumplists.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/inpage-jumplist.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/overflow-button.js", "webpack://jenkins-ui/./src/main/js/util/symbols.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/hetero-list.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/combo-box.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/autocomplete.js", "webpack://jenkins-ui/./src/main/js/components/dropdowns/index.js", "webpack://jenkins-ui/./src/main/js/components/command-palette/symbols.js", "webpack://jenkins-ui/./src/main/js/components/command-palette/models.js", "webpack://jenkins-ui/./src/main/js/api/search.js", "webpack://jenkins-ui/./src/main/js/components/command-palette/datasources.js", "webpack://jenkins-ui/./src/main/js/components/command-palette/utils.js", "webpack://jenkins-ui/./src/main/js/components/command-palette/index.js", "webpack://jenkins-ui/./src/main/js/components/notifications/index.js", "webpack://jenkins-ui/./src/main/js/components/search-bar/index.js", "webpack://jenkins-ui/./src/main/js/components/tooltips/index.js", "webpack://jenkins-ui/./src/main/js/components/stop-button-link/index.js", "webpack://jenkins-ui/./src/main/js/components/confirmation-link/index.js", "webpack://jenkins-ui/./src/main/js/util/jenkins.js", "webpack://jenkins-ui/./src/main/js/components/dialogs/index.js", "webpack://jenkins-ui/./src/main/js/app.js", "webpack://jenkins-ui/webpack/bootstrap", "webpack://jenkins-ui/webpack/runtime/chunk loaded", "webpack://jenkins-ui/webpack/runtime/compat get default export", "webpack://jenkins-ui/webpack/runtime/define property getters", "webpack://jenkins-ui/webpack/runtime/global", "webpack://jenkins-ui/webpack/runtime/hasOwnProperty shorthand", "webpack://jenkins-ui/webpack/runtime/runtimeId", "webpack://jenkins-ui/webpack/runtime/jsonp chunk loading", "webpack://jenkins-ui/webpack/runtime/nonce", "webpack://jenkins-ui/webpack/before-startup", "webpack://jenkins-ui/webpack/startup", "webpack://jenkins-ui/webpack/after-startup"], "sourcesContent": ["function combinePath(pathOne, pathTwo) {\n  let queryParams;\n  let i = pathOne.indexOf(\"?\");\n  if (i >= 0) {\n    queryParams = pathOne.substring(i);\n  } else {\n    queryParams = \"\";\n  }\n\n  i = pathOne.indexOf(\"#\");\n  if (i >= 0) {\n    pathOne = pathOne.substring(0, i);\n  }\n\n  if (pathOne.endsWith(\"/\")) {\n    return pathOne + pathTwo + queryParams;\n  }\n  return pathOne + \"/\" + pathTwo + queryParams;\n}\n\nexport default { combinePath };\n", "function specify(selector, id, priority, behavior) {\n  Behaviour.specify(selector, id, priority, behavior);\n}\n\nfunction applySubtree(startNode, includeSelf) {\n  Behaviour.applySubtree(startNode, includeSelf);\n}\n\nexport default { specify, applySubtree };\n", "export function createElementFromHtml(html) {\n  const template = document.createElement(\"template\");\n  template.innerHTML = html.trim();\n  return template.content.firstElementChild;\n}\n\nexport function toId(string) {\n  return string\n    .trim()\n    .replace(/[\\W_]+/g, \"-\")\n    .toLowerCase();\n}\n", "function xmlEscape(str) {\n  return str.replace(/[<>&'\"]/g, (match) => {\n    switch (match) {\n      case \"<\":\n        return \"&lt;\";\n      case \">\":\n        return \"&gt;\";\n      case \"&\":\n        return \"&amp;\";\n      case \"'\":\n        return \"&apos;\";\n      case '\"':\n        return \"&quot;\";\n    }\n  });\n}\n\nexport { xmlEscape };\n", "import { createElementFromHtml } from \"@/util/dom\";\nimport { xmlEscape } from \"@/util/security\";\n\nfunction dropdown() {\n  return {\n    content: \"<p class='jenkins-spinner'></p>\",\n    interactive: true,\n    trigger: \"click\",\n    allowHTML: true,\n    placement: \"bottom-start\",\n    arrow: false,\n    theme: \"dropdown\",\n    appendTo: document.body,\n    offset: [0, 0],\n    animation: \"dropdown\",\n    duration: 250,\n    onShow: (instance) => {\n      const referenceParent = instance.reference.parentNode;\n\n      if (referenceParent.classList.contains(\"model-link\")) {\n        referenceParent.classList.add(\"model-link--open\");\n      }\n    },\n    onHide: (instance) => {\n      const referenceParent = instance.reference.parentNode;\n      referenceParent.classList.remove(\"model-link--open\");\n    },\n  };\n}\n\nfunction menuItem(options) {\n  const itemOptions = Object.assign(\n    {\n      type: \"link\",\n    },\n    options,\n  );\n\n  const label = xmlEscape(itemOptions.label);\n  let badgeText;\n  let badgeTooltip;\n  let badgeSeverity;\n  if (itemOptions.badge) {\n    badgeText = xmlEscape(itemOptions.badge.text);\n    badgeTooltip = xmlEscape(itemOptions.badge.tooltip);\n    badgeSeverity = xmlEscape(itemOptions.badge.severity);\n  }\n  const tag = itemOptions.type === \"link\" ? \"a\" : \"button\";\n\n  const item = createElementFromHtml(`\n      <${tag} class=\"jenkins-dropdown__item ${itemOptions.clazz ? xmlEscape(itemOptions.clazz) : \"\"}\" ${itemOptions.url ? `href=\"${xmlEscape(itemOptions.url)}\"` : \"\"} ${itemOptions.id ? `id=\"${xmlEscape(itemOptions.id)}\"` : \"\"}>\n          ${\n            itemOptions.icon\n              ? `<div class=\"jenkins-dropdown__item__icon\">${\n                  itemOptions.iconXml\n                    ? itemOptions.iconXml\n                    : `<img alt=\"${label}\" src=\"${itemOptions.icon}\" />`\n                }</div>`\n              : ``\n          }\n          ${label}\n                    ${\n                      itemOptions.badge != null\n                        ? `<span class=\"jenkins-dropdown__item__badge jenkins-badge jenkins-!-${badgeSeverity}-color\" tooltip=\"${badgeTooltip}\">${badgeText}</span>`\n                        : ``\n                    }\n          ${\n            itemOptions.subMenu != null\n              ? `<span class=\"jenkins-dropdown__item__chevron\"></span>`\n              : ``\n          }\n      </${tag}>\n    `);\n\n  if (options.onClick) {\n    item.addEventListener(\"click\", (event) => options.onClick(event));\n  }\n  if (options.onKeyPress) {\n    item.onkeypress = options.onKeyPress;\n  }\n  return item;\n}\n\nfunction heading(label) {\n  return createElementFromHtml(\n    `<p class=\"jenkins-dropdown__heading\">${label}</p>`,\n  );\n}\n\nfunction separator() {\n  return createElementFromHtml(\n    `<div class=\"jenkins-dropdown__separator\"></div>`,\n  );\n}\n\nfunction placeholder(label) {\n  return createElementFromHtml(\n    `<p class=\"jenkins-dropdown__placeholder\">${label}</p>`,\n  );\n}\n\nfunction disabled(label) {\n  return createElementFromHtml(\n    `<p class=\"jenkins-dropdown__disabled\">${label}</p>`,\n  );\n}\n\nexport default {\n  dropdown,\n  menuItem,\n  heading,\n  separator,\n  placeholder,\n  disabled,\n};\n", "/**\n * @param {Element} container - the container for the items\n * @param {function(): NodeListOf<Element>} itemsFunc - function which returns the list of items\n * @param {string} selectedClass - the class to apply to the selected item\n * @param {function()} additionalBehaviours - add additional keyboard shortcuts to the focused item\n * @param hasKeyboardPriority - set if custom behaviour is needed to decide whether the element has keyboard priority\n */\nexport default function makeKeyboardNavigable(\n  container,\n  itemsFunc,\n  selectedClass,\n  additionalBehaviours = () => {},\n  hasKeyboardPriority = () =>\n    window.getComputedStyle(container).visibility === \"visible\",\n) {\n  window.addEventListener(\"keyup\", (e) => {\n    let items = Array.from(itemsFunc());\n    let selectedItem = items.find((a) => a.classList.contains(selectedClass));\n    if (container && hasKeyboardPriority(container)) {\n      if (e.key === \"Tab\") {\n        if (items.includes(document.activeElement)) {\n          if (selectedItem) {\n            selectedItem.classList.remove(selectedClass);\n          }\n          selectedItem = document.activeElement;\n          selectedItem.classList.add(selectedClass);\n        }\n      }\n    }\n  });\n  window.addEventListener(\"keydown\", (e) => {\n    let items = Array.from(itemsFunc());\n    let selectedItem = items.find((a) => a.classList.contains(selectedClass));\n\n    // Only navigate through the list of items if the container is active on the screen\n    if (container && hasKeyboardPriority(container)) {\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n\n        if (selectedItem) {\n          selectedItem.classList.remove(selectedClass);\n          const next = items[items.indexOf(selectedItem) + 1];\n\n          if (next) {\n            selectedItem = next;\n          } else {\n            selectedItem = items[0];\n          }\n        } else {\n          selectedItem = items[0];\n        }\n\n        scrollAndSelect(selectedItem, selectedClass, items);\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n\n        if (selectedItem) {\n          selectedItem.classList.remove(selectedClass);\n          const previous = items[items.indexOf(selectedItem) - 1];\n\n          if (previous) {\n            selectedItem = previous;\n          } else {\n            selectedItem = items[items.length - 1];\n          }\n        } else {\n          selectedItem = items[items.length - 1];\n        }\n\n        scrollAndSelect(selectedItem, selectedClass, items);\n      } else if (e.key === \"Enter\") {\n        if (selectedItem) {\n          e.preventDefault();\n          selectedItem.click();\n        }\n      } else {\n        additionalBehaviours(selectedItem, e.key, e);\n      }\n    }\n  });\n}\n\nfunction scrollAndSelect(selectedItem, selectedClass, items) {\n  if (selectedItem) {\n    if (!isInViewport(selectedItem)) {\n      selectedItem.scrollIntoView(false);\n    }\n    selectedItem.classList.add(selectedClass);\n    if (items.includes(document.activeElement)) {\n      selectedItem.focus();\n    }\n  }\n}\n\nfunction isInViewport(element) {\n  const rect = element.getBoundingClientRect();\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= window.innerHeight &&\n    rect.right <= window.innerWidth\n  );\n}\n", "import Templates from \"@/components/dropdowns/templates\";\nimport make<PERSON><PERSON>boardNavigable from \"@/util/keyboard\";\nimport tippy from \"tippy.js\";\nimport behaviorShim from \"@/util/behavior-shim\";\n\nconst SELECTED_ITEM_CLASS = \"jenkins-dropdown__item--selected\";\n\n/*\n * Generates the dropdowns for the given element\n * Preloads the data on hover for speed\n * @param element - the element to generate the dropdown for\n * @param callback - called to retrieve the list of dropdown items\n */\nfunction generateDropdown(element, callback, immediate) {\n  if (element._tippy && element._tippy.props.theme === \"dropdown\") {\n    element._tippy.destroy();\n  }\n\n  tippy(\n    element,\n    Object.assign({}, Templates.dropdown(), {\n      hideOnClick:\n        element.dataset[\"hideOnClick\"] !== \"false\" ? \"toggle\" : false,\n      onCreate(instance) {\n        const onload = () => {\n          if (instance.loaded) {\n            return;\n          }\n\n          document.addEventListener(\"click\", (event) => {\n            const isClickInAnyDropdown =\n              !!event.target.closest(\"[data-tippy-root]\");\n            const isClickOnReference = instance.reference.contains(\n              event.target,\n            );\n\n            if (!isClickInAnyDropdown && !isClickOnReference) {\n              instance.hide();\n            }\n          });\n\n          callback(instance);\n        };\n        if (immediate) {\n          onload();\n        } else {\n          instance.reference.addEventListener(\"mouseenter\", onload);\n        }\n      },\n    }),\n  );\n}\n\n/*\n * Generates the contents for the dropdown\n */\nfunction generateDropdownItems(items, compact) {\n  const menuItems = document.createElement(\"div\");\n  menuItems.classList.add(\"jenkins-dropdown\");\n  if (compact === true) {\n    menuItems.classList.add(\"jenkins-dropdown--compact\");\n  }\n\n  items\n    .map((item) => {\n      if (item.type === \"CUSTOM\") {\n        return item.contents;\n      }\n\n      if (item.type === \"HEADER\") {\n        return Templates.heading(item.label);\n      }\n\n      if (item.type === \"SEPARATOR\") {\n        return Templates.separator();\n      }\n\n      if (item.type === \"DISABLED\") {\n        return Templates.disabled(item.label);\n      }\n\n      const menuItem = Templates.menuItem(item);\n\n      if (item.subMenu != null) {\n        tippy(\n          menuItem,\n          Object.assign({}, Templates.dropdown(), {\n            content: generateDropdownItems(item.subMenu()),\n            trigger: \"mouseenter\",\n            placement: \"right-start\",\n            offset: [-8, 0],\n          }),\n        );\n      }\n\n      return menuItem;\n    })\n    .forEach((item) => menuItems.appendChild(item));\n\n  if (items.length === 0) {\n    menuItems.appendChild(Templates.placeholder(\"No items\"));\n  }\n\n  makeKeyboardNavigable(\n    menuItems,\n    () => menuItems.querySelectorAll(\".jenkins-dropdown__item\"),\n    SELECTED_ITEM_CLASS,\n    (selectedItem, key, evt) => {\n      if (!selectedItem) {\n        return;\n      }\n      switch (key) {\n        case \"ArrowLeft\": {\n          const root = selectedItem.closest(\"[data-tippy-root]\");\n          if (root) {\n            const tippyReference = root._tippy;\n            if (tippyReference) {\n              tippyReference.hide();\n            }\n          }\n          break;\n        }\n        case \"ArrowRight\": {\n          const tippyRef = selectedItem._tippy;\n          if (!tippyRef) {\n            break;\n          }\n\n          tippyRef.show();\n          tippyRef.props.content\n            .querySelector(\".jenkins-dropdown__item\")\n            .classList.add(SELECTED_ITEM_CLASS);\n          break;\n        }\n        default:\n          if (selectedItem.onkeypress) {\n            selectedItem.onkeypress(evt);\n          }\n      }\n    },\n    (container) => {\n      const isVisible =\n        window.getComputedStyle(container).visibility === \"visible\";\n      const isLastDropdown = Array.from(\n        document.querySelectorAll(\".jenkins-dropdown\"),\n      )\n        .filter((dropdown) => container !== dropdown)\n        .filter(\n          (dropdown) =>\n            window.getComputedStyle(dropdown).visibility === \"visible\",\n        )\n        .every(\n          (dropdown) =>\n            !(\n              container.compareDocumentPosition(dropdown) &\n              Node.DOCUMENT_POSITION_FOLLOWING\n            ),\n        );\n\n      return isVisible && isLastDropdown;\n    },\n  );\n\n  behaviorShim.applySubtree(menuItems);\n\n  return menuItems;\n}\n\nfunction convertHtmlToItems(children) {\n  const items = [];\n  Array.from(children).forEach((child) => {\n    const attributes = child.dataset;\n    const type = child.dataset.dropdownType;\n\n    switch (type) {\n      case \"ITEM\": {\n        const item = {\n          label: attributes.dropdownText,\n          id: attributes.dropdownId,\n          icon: attributes.dropdownIcon,\n          iconXml: attributes.dropdownIcon,\n          clazz: attributes.dropdownClazz,\n        };\n\n        if (attributes.dropdownHref) {\n          item.url = attributes.dropdownHref;\n          item.type = \"link\";\n        } else {\n          item.type = \"button\";\n        }\n\n        items.push(item);\n        break;\n      }\n      case \"SUBMENU\":\n        items.push({\n          type: \"ITEM\",\n          label: attributes.dropdownText,\n          icon: attributes.dropdownIcon,\n          iconXml: attributes.dropdownIcon,\n          subMenu: () => convertHtmlToItems(child.content.children),\n        });\n        break;\n      case \"SEPARATOR\":\n        items.push({ type: type });\n        break;\n      case \"HEADER\":\n        items.push({ type: type, label: attributes.dropdownText });\n        break;\n      case \"CUSTOM\":\n        items.push({ type: type, contents: child.content.cloneNode(true) });\n        break;\n    }\n  });\n  return items;\n}\n\nfunction validateDropdown(e) {\n  if (e.targetUrl) {\n    const method = e.getAttribute(\"checkMethod\") || \"post\";\n    try {\n      FormChecker.delayedCheck(e.targetUrl(), method, e.targetElement);\n    } catch (x) {\n      console.warn(x);\n    }\n  }\n}\n\nfunction getMaxSuggestionCount(e, defaultValue) {\n  return parseInt(e.dataset[\"maxsuggestions\"]) || defaultValue;\n}\n\nfunction debounce(callback) {\n  callback.running = false;\n  return () => {\n    if (!callback.running) {\n      callback.running = true;\n      setTimeout(() => {\n        callback();\n        callback.running = false;\n      }, 300);\n    }\n  };\n}\n\nexport default {\n  convertHtmlToItems,\n  generateDropdown,\n  generateDropdownItems,\n  validateDropdown,\n  getMaxSuggestionCount,\n  debounce,\n};\n", "import Path from \"@/util/path\";\nimport behaviorShim from \"@/util/behavior-shim\";\nimport Utils from \"@/components/dropdowns/utils\";\n\nfunction init() {\n  generateJumplistAccessors();\n  generateDropdowns();\n}\n\n/*\n * Appends a ⌄ button at the end of links which support jump lists\n */\nfunction generateJumplistAccessors() {\n  behaviorShim.specify(\"A.model-link\", \"-jumplist-\", 999, (link) => {\n    const isFirefox = navigator.userAgent.indexOf(\"Firefox\") !== -1;\n    // Firefox adds unwanted lines when copying buttons in text, so use a span instead\n    const dropdownChevron = document.createElement(\n      isFirefox ? \"span\" : \"button\",\n    );\n    dropdownChevron.className = \"jenkins-menu-dropdown-chevron\";\n    dropdownChevron.dataset.href = link.href;\n    dropdownChevron.addEventListener(\"click\", (event) => {\n      event.preventDefault();\n    });\n    link.appendChild(dropdownChevron);\n  });\n}\n\n/*\n * Generates the dropdowns for the jump lists\n */\nfunction generateDropdowns() {\n  behaviorShim.specify(\n    \"li.children, .jenkins-jumplist-link, #menuSelector, .jenkins-menu-dropdown-chevron\",\n    \"-dropdown-\",\n    1000,\n    (element) =>\n      Utils.generateDropdown(element, (instance) => {\n        const href = element.dataset.href;\n        const jumplistType = !element.classList.contains(\"children\")\n          ? \"contextMenu\"\n          : \"childrenContextMenu\";\n\n        if (element.items) {\n          instance.setContent(Utils.generateDropdownItems(element.items));\n          return;\n        }\n\n        fetch(Path.combinePath(href, jumplistType))\n          .then((response) => response.json())\n          .then((json) =>\n            instance.setContent(\n              Utils.generateDropdownItems(\n                mapChildrenItemsToDropdownItems(json.items),\n              ),\n            ),\n          )\n          .catch((error) => console.log(`Jumplist request failed: ${error}`))\n          .finally(() => (instance.loaded = true));\n      }),\n  );\n}\n\n/*\n * Generates the contents for the dropdown\n */\nfunction mapChildrenItemsToDropdownItems(items) {\n  return items.map((item) => {\n    if (item.type === \"HEADER\") {\n      return {\n        type: \"HEADER\",\n        label: item.displayName,\n      };\n    }\n\n    if (item.type === \"SEPARATOR\") {\n      return {\n        type: \"SEPARATOR\",\n      };\n    }\n\n    return {\n      icon: item.icon,\n      iconXml: item.iconXml,\n      label: item.displayName,\n      url: item.url,\n      type: item.post || item.requiresConfirmation ? \"button\" : \"link\",\n      badge: item.badge,\n      onClick: () => {\n        if (item.post || item.requiresConfirmation) {\n          if (item.requiresConfirmation) {\n            dialog\n              .confirm(item.displayName, { message: item.message })\n              .then(() => {\n                const form = document.createElement(\"form\");\n                form.setAttribute(\"method\", item.post ? \"POST\" : \"GET\");\n                form.setAttribute(\"action\", item.url);\n                if (item.post) {\n                  crumb.appendToForm(form);\n                }\n                document.body.appendChild(form);\n                form.submit();\n              });\n          } else {\n            fetch(item.url, {\n              method: \"post\",\n              headers: crumb.wrap({}),\n            }).then((rsp) => {\n              if (rsp.ok) {\n                notificationBar.show(\n                  item.displayName + \": Done.\",\n                  notificationBar.SUCCESS,\n                );\n              } else {\n                notificationBar.show(\n                  item.displayName + \": Failed.\",\n                  notificationBar.ERROR,\n                );\n              }\n            });\n          }\n        }\n      },\n      subMenu: item.subMenu\n        ? () => {\n            return mapChildrenItemsToDropdownItems(item.subMenu.items);\n          }\n        : null,\n    };\n  });\n}\n\nexport default { init };\n", "import { toId } from \"@/util/dom\";\n\n/*\n * Generates a jump list for the active breadcrumb to jump to\n * sections on the page (if using <f:breadcrumb-config-outline />)\n */\nfunction init() {\n  const inpageNavigationBreadcrumb = document.querySelector(\"#inpage-nav\");\n\n  if (inpageNavigationBreadcrumb) {\n    const chevron = document.createElement(\"li\");\n    chevron.classList.add(\"children\");\n    chevron.items = Array.from(\n      document.querySelectorAll(\n        \"form > div > .jenkins-section > .jenkins-section__title\",\n      ),\n    ).map((section) => {\n      section.id = toId(section.textContent);\n      return { label: section.textContent, url: \"#\" + section.id };\n    });\n\n    inpageNavigationBreadcrumb.after(chevron);\n  }\n}\n\nexport default { init };\n", "import Utils from \"@/components/dropdowns/utils\";\nimport behaviorShim from \"@/util/behavior-shim\";\n\n/**\n * Creates a new dropdown based on the element's next sibling\n */\nfunction init() {\n  behaviorShim.specify(\n    \"[data-dropdown='true']\",\n    \"-dropdown-\",\n    1000,\n    (element) => {\n      Utils.generateDropdown(element, (instance) => {\n        const elements =\n          element.nextElementSibling.content.children[0].children;\n        const mappedItems = Utils.convertHtmlToItems(elements);\n\n        instance.setContent(Utils.generateDropdownItems(mappedItems));\n      });\n    },\n  );\n}\n\nexport default { init };\n", "export const INFO = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M256 56C145.72 56 56 145.72 56 256s89.72 200 200 200 200-89.72 200-200S366.28 56 256 56zm0 82a26 26 0 11-26 26 26 26 0 0126-26zm48 226h-88a16 16 0 010-32h28v-88h-16a16 16 0 010-32h32a16 16 0 0116 16v104h28a16 16 0 010 32z\" fill='currentColor' /></svg>`;\nexport const SUCCESS = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm108.25 138.29l-134.4 160a16 16 0 01-12 5.71h-.27a16 16 0 01-11.89-5.3l-57.6-64a16 16 0 1123.78-21.4l45.29 50.32 122.59-145.91a16 16 0 0124.5 20.58z\" fill='currentColor'/></svg>`;\nexport const WARNING = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M449.07 399.08L278.64 82.58c-12.08-22.44-44.26-22.44-56.35 0L51.87 399.08A32 32 0 0080 446.25h340.89a32 32 0 0028.18-47.17zm-198.6-1.83a20 20 0 1120-20 20 20 0 01-20 20zm21.72-201.15l-5.74 122a16 16 0 01-32 0l-5.74-121.95a21.73 21.73 0 0121.5-22.69h.21a21.74 21.74 0 0121.73 22.7z\" fill='currentColor'/></svg>`;\nexport const ERROR = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm0 319.91a20 20 0 1120-20 20 20 0 01-20 20zm21.72-201.15l-5.74 122a16 16 0 01-32 0l-5.74-121.94v-.05a21.74 21.74 0 1143.44 0z\" fill='currentColor'/></svg>`;\nexport const CLOSE = `<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"ionicon\" viewBox=\"0 0 512 512\"><path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"32\" d=\"M368 368L144 144M368 144L144 368\"/></svg>`;\nexport const CHEVRON_DOWN = `<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"ionicon\" viewBox=\"0 0 512 512\"><title>Chevron Down</title><path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"48\" d=\"M112 184l144 144 144-144\"/></svg>`;\nexport const FUNNEL = `<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"ionicon\" viewBox=\"0 0 512 512\"><path d=\"M35.4 87.12l168.65 196.44A16.07 16.07 0 01208 294v119.32a7.93 7.93 0 005.39 7.59l80.15 26.67A7.94 7.94 0 00304 440V294a16.07 16.07 0 014-10.44L476.6 87.12A14 14 0 00466 64H46.05A14 14 0 0035.4 87.12z\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"32\"/></svg>`;\n", "import behaviorShim from \"@/util/behavior-shim\";\nimport Templates from \"@/components/dropdowns/templates\";\nimport Utils from \"@/components/dropdowns/utils\";\nimport * as Symbols from \"@/util/symbols\";\nimport { createElementFromHtml } from \"@/util/dom\";\nimport tippy from \"tippy.js\";\n\nfunction init() {\n  generateButtons();\n  generateHandles();\n}\n\nfunction generateHandles() {\n  behaviorShim.specify(\"DIV.dd-handle\", \"hetero-list\", -100, function (e) {\n    e.addEventListener(\"mouseover\", function () {\n      this.closest(\".repeated-chunk\").classList.add(\"hover\");\n    });\n    e.addEventListener(\"mouseout\", function () {\n      this.closest(\".repeated-chunk\").classList.remove(\"hover\");\n    });\n  });\n}\n\nfunction convertInputsToButtons(e) {\n  let oldInputs = e.querySelectorAll(\"INPUT.hetero-list-add\");\n  oldInputs.forEach((oldbtn) => {\n    let btn = document.createElement(\"button\");\n    btn.setAttribute(\"type\", \"button\");\n    btn.classList.add(\"hetero-list-add\", \"jenkins-button\");\n    btn.innerText = oldbtn.getAttribute(\"value\");\n    if (oldbtn.hasAttribute(\"suffix\")) {\n      btn.setAttribute(\"suffix\", oldbtn.getAttribute(\"suffix\"));\n    }\n    let chevron = createElementFromHtml(Symbols.CHEVRON_DOWN);\n    btn.appendChild(chevron);\n    oldbtn.parentNode.appendChild(btn);\n    oldbtn.remove();\n  });\n}\n\nfunction generateButtons() {\n  behaviorShim.specify(\n    \"DIV.hetero-list-container\",\n    \"hetero-list-new\",\n    -100,\n    function (e) {\n      if (isInsideRemovable(e)) {\n        return;\n      }\n\n      convertInputsToButtons(e);\n      let btn = Array.from(e.querySelectorAll(\"BUTTON.hetero-list-add\")).pop();\n\n      let prototypes = e.lastElementChild;\n      while (!prototypes.classList.contains(\"prototypes\")) {\n        prototypes = prototypes.previousElementSibling;\n      }\n      let insertionPoint = prototypes.previousElementSibling; // this is where the new item is inserted.\n\n      let templates = [];\n      let children = prototypes.children;\n      for (let i = 0; i < children.length; i++) {\n        let n = children[i];\n        let name = n.getAttribute(\"name\");\n        let descriptorId = n.getAttribute(\"descriptorId\");\n        let title = n.getAttribute(\"title\");\n\n        templates.push({\n          html: n.innerHTML,\n          name: name,\n          descriptorId: descriptorId,\n          title: title,\n        });\n      }\n      prototypes.remove();\n      let withDragDrop = registerSortableDragDrop(e);\n\n      function insert(instance, template) {\n        let nc = document.createElement(\"div\");\n        nc.className = \"repeated-chunk fade-in\";\n        nc.setAttribute(\"name\", template.name);\n        nc.setAttribute(\"descriptorId\", template.descriptorId);\n        nc.innerHTML = template.html;\n\n        instance.hide();\n\n        renderOnDemand(\n          nc.querySelector(\"div.config-page\"),\n          function () {\n            function findInsertionPoint() {\n              // given the element to be inserted 'prospect',\n              // and the array of existing items 'current',\n              // and preferred ordering function, return the position in the array\n              // the prospect should be inserted.\n              // (for example 0 if it should be the first item)\n              function findBestPosition(prospect, current, order) {\n                function desirability(pos) {\n                  let count = 0;\n                  for (let i = 0; i < current.length; i++) {\n                    if (i < pos == order(current[i]) <= order(prospect)) {\n                      count++;\n                    }\n                  }\n                  return count;\n                }\n\n                let bestScore = -1;\n                let bestPos = 0;\n                for (let i = 0; i <= current.length; i++) {\n                  let d = desirability(i);\n                  if (bestScore <= d) {\n                    // prefer to insert them toward the end\n                    bestScore = d;\n                    bestPos = i;\n                  }\n                }\n                return bestPos;\n              }\n\n              let current = Array.from(e.children).filter(function (e) {\n                return e.matches(\"DIV.repeated-chunk\");\n              });\n\n              function o(did) {\n                if (did instanceof Element) {\n                  did = did.getAttribute(\"descriptorId\");\n                }\n                for (let i = 0; i < templates.length; i++) {\n                  if (templates[i].descriptorId == did) {\n                    return i;\n                  }\n                }\n                return 0; // can't happen\n              }\n\n              let bestPos = findBestPosition(template.descriptorId, current, o);\n              if (bestPos < current.length) {\n                return current[bestPos];\n              } else {\n                return insertionPoint;\n              }\n            }\n            let referenceNode = e.classList.contains(\"honor-order\")\n              ? findInsertionPoint()\n              : insertionPoint;\n            referenceNode.parentNode.insertBefore(nc, referenceNode);\n\n            // Initialize drag & drop for this component\n            if (withDragDrop) {\n              registerSortableDragDrop(nc);\n            }\n            Behaviour.applySubtree(nc, true);\n            ensureVisible(nc);\n            nc.classList.remove(\"fade-in\");\n            layoutUpdateCallback.call();\n          },\n          true,\n        );\n      }\n\n      function has(id) {\n        return (\n          e.querySelector('DIV.repeated-chunk[descriptorId=\"' + id + '\"]') !=\n          null\n        );\n      }\n\n      let oneEach = e.classList.contains(\"one-each\");\n\n      generateDropDown(btn, (instance) => {\n        let menuItems = [];\n        for (let i = 0; i < templates.length; i++) {\n          let n = templates[i];\n          let disabled = oneEach && has(n.descriptorId);\n          let type = disabled ? \"DISABLED\" : \"button\";\n          let item = {\n            label: n.title,\n            onClick: (event) => {\n              event.preventDefault();\n              event.stopPropagation();\n              insert(instance, n);\n            },\n            type: type,\n          };\n          menuItems.push(item);\n        }\n        const menuContainer = document.createElement(\"div\");\n        const menu = Utils.generateDropdownItems(menuItems, true);\n        menuContainer.appendChild(createFilter(menu));\n        menuContainer.appendChild(menu);\n        instance.setContent(menuContainer);\n      });\n    },\n  );\n}\n\nfunction createFilter(menu) {\n  const filterInput = createElementFromHtml(`\n    <input class=\"jenkins-dropdown__filter-input\" placeholder=\"Filter\" spellcheck=\"false\" type=\"search\"/>\n  `);\n\n  filterInput.addEventListener(\"input\", (event) =>\n    applyFilterKeyword(menu, event.currentTarget),\n  );\n  filterInput.addEventListener(\"click\", (event) => event.stopPropagation());\n  filterInput.addEventListener(\"keydown\", (event) => {\n    if (event.key === \"Enter\") {\n      event.preventDefault();\n    }\n  });\n\n  const filterContainer = createElementFromHtml(`\n    <div class=\"jenkins-dropdown__filter\">\n      <div class=\"jenkins-dropdown__item__icon\">\n        ${Symbols.FUNNEL}\n      </div>\n    </div>\n  `);\n  filterContainer.appendChild(filterInput);\n  return filterContainer;\n}\n\nfunction applyFilterKeyword(menu, filterInput) {\n  const filterKeyword = (filterInput.value || \"\").toLowerCase();\n  let items = menu.querySelectorAll(\n    \".jenkins-dropdown__item, .jenkins-dropdown__disabled\",\n  );\n  for (let item of items) {\n    let match = item.innerText.toLowerCase().includes(filterKeyword);\n    item.style.display = match ? \"inline-flex\" : \"none\";\n  }\n}\n\nfunction generateDropDown(button, callback) {\n  tippy(\n    button,\n    Object.assign({}, Templates.dropdown(), {\n      appendTo: undefined,\n      onCreate(instance) {\n        if (instance.loaded) {\n          return;\n        }\n        instance.popper.addEventListener(\"click\", () => {\n          instance.hide();\n        });\n        instance.popper.addEventListener(\"keydown\", () => {\n          if (event.key === \"Escape\") {\n            instance.hide();\n          }\n        });\n      },\n      onShow(instance) {\n        callback(instance);\n        button.dataset.expanded = \"true\";\n      },\n      onHide() {\n        button.dataset.expanded = \"false\";\n      },\n    }),\n  );\n}\n\nexport default { init };\n", "import behaviorShim from \"@/util/behavior-shim\";\nimport Utils from \"@/components/dropdowns/utils\";\n\nfunction init() {\n  function convertSuggestionToItem(suggestion, e) {\n    const confirm = () => {\n      e.value = suggestion.name;\n      Utils.validateDropdown(e);\n      e.focus();\n    };\n    return {\n      label: suggestion.name,\n      onClick: confirm,\n      onKeyPress: (evt) => {\n        if (evt.key === \"Tab\") {\n          confirm();\n          e.dropdown.hide();\n          evt.preventDefault();\n        }\n      },\n    };\n  }\n\n  function createAndShowDropdown(e, div, suggestions) {\n    const items = suggestions\n      .splice(0, Utils.getMaxSuggestionCount(e, 20))\n      .map((s) => convertSuggestionToItem(s, e));\n    if (!e.dropdown) {\n      Utils.generateDropdown(\n        div,\n        (instance) => {\n          e.dropdown = instance;\n        },\n        true,\n      );\n    }\n    e.dropdown.setContent(Utils.generateDropdownItems(items, true));\n    e.dropdown.show();\n  }\n\n  function updateSuggestions(e, div, items) {\n    const text = e.value.trim();\n\n    let filteredItems = text\n      ? items.filter((item) => item.indexOf(text) === 0)\n      : items;\n\n    const suggestions = filteredItems\n      .filter((item) => item.indexOf(text) === 0)\n      .map((item) => {\n        return { name: item };\n      });\n    createAndShowDropdown(e, div, suggestions || []);\n  }\n\n  behaviorShim.specify(\"INPUT.combobox2\", \"combobox\", 100, function (e) {\n    // form field with auto-completion support\n    // insert the auto-completion container\n    refillOnChange(e, function (params) {\n      const div = document.createElement(\"DIV\");\n      e.parentNode.insertBefore(div, e.nextElementSibling);\n      e.style.position = \"relative\";\n\n      const url = e.getAttribute(\"fillUrl\");\n      fetch(url, {\n        headers: crumb.wrap({\n          \"Content-Type\": \"application/x-www-form-urlencoded\",\n        }),\n        method: \"post\",\n        body: new URLSearchParams(params),\n      })\n        .then((rsp) => (rsp.ok ? rsp.json() : {}))\n        .then((items) => {\n          e.addEventListener(\"focus\", () => updateSuggestions(e, div, items));\n\n          // otherwise menu won't hide on tab with nothing selected\n          // needs delay as without that it blocks click selection of an item\n          e.addEventListener(\"focusout\", () =>\n            setTimeout(() => e.dropdown.hide(), 200),\n          );\n\n          e.addEventListener(\n            \"input\",\n            Utils.debounce(() => {\n              updateSuggestions(e, div, items);\n            }),\n          );\n        });\n    });\n  });\n}\n\nexport default { init };\n", "import behaviorShim from \"@/util/behavior-shim\";\nimport Utils from \"@/components/dropdowns/utils\";\n\nfunction init() {\n  function addValue(value, item, delimiter) {\n    const prev = value.includes(delimiter)\n      ? value.substring(0, value.lastIndexOf(delimiter) + 1) + \" \"\n      : \"\";\n    return prev + item + delimiter + \" \";\n  }\n\n  function convertSuggestionToItem(suggestion, e) {\n    const delimiter = e.getAttribute(\"autoCompleteDelimChar\");\n    const confirm = () => {\n      e.value = delimiter\n        ? addValue(e.value, suggestion.name, delimiter)\n        : suggestion.name;\n      Utils.validateDropdown(e);\n      e.focus();\n    };\n    return {\n      label: suggestion.name,\n      onClick: confirm,\n      onKeyPress: (evt) => {\n        if (evt.key === \"Tab\") {\n          confirm();\n          e.dropdown.hide();\n          evt.preventDefault();\n        }\n      },\n    };\n  }\n\n  function createAndShowDropdown(e, suggestions) {\n    const items = suggestions\n      .splice(0, Utils.getMaxSuggestionCount(e, 10))\n      .map((s) => convertSuggestionToItem(s, e));\n    if (!e.dropdown) {\n      Utils.generateDropdown(\n        e,\n        (instance) => {\n          e.dropdown = instance;\n          instance.popper.style.minWidth = e.offsetWidth + \"px\";\n        },\n        true,\n      );\n    }\n    e.dropdown.setContent(Utils.generateDropdownItems(items, true));\n    e.dropdown.show();\n  }\n\n  function updateSuggestions(e) {\n    const text = e.value.trim();\n    const delimiter = e.getAttribute(\"autoCompleteDelimChar\");\n    const word = delimiter ? text.split(delimiter).reverse()[0].trim() : text;\n    if (!word) {\n      if (e.dropdown) {\n        e.dropdown.hide();\n      }\n      return;\n    }\n\n    const url = e.getAttribute(\"autoCompleteUrl\");\n\n    const depends = e.getAttribute(\"fillDependsOn\");\n    const q = qs(e).addThis();\n    if (depends && depends.length > 0) {\n      depends.split(\" \").forEach(\n        TryEach(function (n) {\n          q.nearBy(n);\n        }),\n      );\n    }\n\n    const queryString = q.toString();\n    const idx = queryString.indexOf(\"?\");\n    const parameters = queryString.substring(idx + 1);\n\n    fetch(url, {\n      method: \"post\",\n      headers: crumb.wrap({\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      }),\n      body: parameters,\n    })\n      .then((rsp) => (rsp.ok ? rsp.json() : {}))\n      .then((response) => createAndShowDropdown(e, response.suggestions || []));\n  }\n\n  behaviorShim.specify(\n    \"INPUT.auto-complete\",\n    \"input-auto-complete\",\n    0,\n    function (e) {\n      e.setAttribute(\"autocomplete\", \"off\");\n      e.dataset[\"hideOnClick\"] = \"false\";\n      // form field with auto-completion support\n      e.style.position = \"relative\";\n      // otherwise menu won't hide on tab with nothing selected\n      // needs delay as without that it blocks click selection of an item\n      e.addEventListener(\"focusout\", () =>\n        setTimeout(() => e.dropdown && e.dropdown.hide(), 200),\n      );\n      e.addEventListener(\n        \"input\",\n        Utils.debounce(() => {\n          updateSuggestions(e);\n        }),\n      );\n    },\n  );\n}\n\nexport default { init };\n", "import Jumplists from \"@/components/dropdowns/jumplists\";\nimport InpageJumplist from \"@/components/dropdowns/inpage-jumplist\";\nimport OverflowButton from \"@/components/dropdowns/overflow-button\";\nimport HeteroLists from \"@/components/dropdowns/hetero-list\";\nimport ComboBox from \"@/components/dropdowns/combo-box\";\nimport Autocomplete from \"@/components/dropdowns/autocomplete\";\n\nfunction init() {\n  Jumplists.init();\n  InpageJumplist.init();\n  OverflowButton.init();\n  HeteroLists.init();\n  ComboBox.init();\n  Autocomplete.init();\n}\n\nexport default { init };\n", "export const EXTERNAL_LINK = `<svg class=\"jenkins-command-palette__results__item__chevron\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M384 224v184a40 40 0 01-40 40H104a40 40 0 01-40-40V168a40 40 0 0140-40h167.48M336 64h112v112M224 288L440 72\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"40\"/></svg>`;\nexport const HELP = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M256 40a216 216 0 10216 216A216 216 0 00256 40z\" fill=\"none\" stroke=\"currentColor\" stroke-miterlimit=\"10\" stroke-width=\"38\"/><path d=\"M200 202.29s.84-17.5 19.57-32.57C230.68 160.77 244 158.18 256 158c10.93-.14 20.69 1.67 26.53 4.45 10 4.76 29.47 16.38 29.47 41.09 0 26-17 37.81-36.37 50.8S251 281.43 251 296\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-miterlimit=\"10\" stroke-width=\"38\"/><circle cx=\"250\" cy=\"360\" r=\"25\" fill=\"currentColor\"/></svg>`;\n", "import * as Symbols from \"./symbols\";\nimport { xmlEscape } from \"@/util/security\";\n\n/**\n * @param {Object} params\n * @param {string} params.icon\n * @param {string} params.label\n * @param {'symbol' | 'image'} params.type\n * @param {string} params.url\n * @param {string | null} params.group\n * @param {boolean | undefined} params.isExternal\n */\nexport function LinkResult(params) {\n  return {\n    label: params.label,\n    url: params.url,\n    group: params.group,\n    render: () => {\n      return `<a class=\"jenkins-command-palette__results__item\" href=\"${xmlEscape(\n        params.url,\n      )}\">\n        ${params.type === \"image\" ? `<img alt=\"${xmlEscape(params.label)}\" class=\"jenkins-command-palette__results__item__icon jenkins-avatar\" src=\"${params.icon}\" />` : \"\"}\n        ${params.type !== \"image\" ? `<div class=\"jenkins-command-palette__results__item__icon\">${params.icon}</div>` : \"\"}\n        ${xmlEscape(params.label)}\n        ${params.isExternal ? Symbols.EXTERNAL_LINK : \"\"}\n    </a>`;\n    },\n  };\n}\n", "/**\n * @param {string} searchTerm\n */\nfunction search(searchTerm) {\n  const address = document.getElementById(\"button-open-command-palette\").dataset\n    .searchUrl;\n  return fetch(`${address}?query=${encodeURIComponent(searchTerm)}`);\n}\n\nexport default { search: search };\n", "import { LinkResult } from \"./models\";\nimport Search from \"@/api/search\";\n\nexport const JenkinsSearchSource = {\n  execute(query) {\n    const rootUrl = document.head.dataset.rooturl;\n\n    function correctAddress(url) {\n      if (url.startsWith(\"/\")) {\n        url = url.substring(1);\n      }\n\n      return rootUrl + \"/\" + url;\n    }\n\n    return Search.search(query).then((rsp) =>\n      rsp.json().then((data) => {\n        return data[\"suggestions\"].slice().map((e) =>\n          LinkResult({\n            icon: e.icon,\n            type: e.type,\n            label: e.name,\n            url: correctAddress(e.url),\n            group: e.group,\n          }),\n        );\n      }),\n    );\n  },\n};\n", "/**\n * Group results by 'group' field into a map\n */\nexport function groupResultsByCategory(array) {\n  return array.reduce((hash, obj) => {\n    if (obj.group === undefined) {\n      return hash;\n    }\n    return Object.assign(hash, {\n      [obj.group]: (hash[obj.group] || []).concat(obj),\n    });\n  }, {});\n}\n", "import { LinkResult } from \"@/components/command-palette/models\";\nimport { JenkinsSearchSource } from \"./datasources\";\nimport debounce from \"lodash/debounce\";\nimport * as Symbols from \"./symbols\";\nimport makeKeyboardNavigable from \"@/util/keyboard\";\nimport { xmlEscape } from \"@/util/security\";\nimport { createElementFromHtml } from \"@/util/dom\";\nimport { groupResultsByCategory } from \"@/components/command-palette/utils\";\n\nconst datasources = [JenkinsSearchSource];\n\nfunction init() {\n  const i18n = document.getElementById(\"command-palette-i18n\");\n  const headerCommandPaletteButton = document.getElementById(\n    \"button-open-command-palette\",\n  );\n  if (headerCommandPaletteButton === null) {\n    return; // no JenkinsHeader, no h:searchbox\n  }\n  const commandPalette = document.getElementById(\"command-palette\");\n  const commandPaletteWrapper = commandPalette.querySelector(\n    \".jenkins-command-palette__wrapper\",\n  );\n  const commandPaletteInput = document.getElementById(\"command-bar\");\n  const commandPaletteSearchBarContainer = commandPalette.querySelector(\n    \".jenkins-command-palette__search\",\n  );\n  const searchResults = document.getElementById(\"search-results\");\n  const searchResultsContainer = document.getElementById(\n    \"search-results-container\",\n  );\n\n  const hoverClass = \"jenkins-command-palette__results__item--hover\";\n\n  makeKeyboardNavigable(\n    searchResultsContainer,\n    () => searchResults.querySelectorAll(\"a\"),\n    hoverClass,\n    () => {},\n    () => commandPalette.open,\n  );\n\n  // Events\n  headerCommandPaletteButton.addEventListener(\"click\", function () {\n    if (commandPalette.hasAttribute(\"open\")) {\n      hideCommandPalette();\n    } else {\n      showCommandPalette();\n    }\n  });\n\n  commandPaletteWrapper.addEventListener(\"click\", function (e) {\n    if (e.target !== e.currentTarget) {\n      return;\n    }\n\n    hideCommandPalette();\n  });\n\n  function renderResults() {\n    const query = commandPaletteInput.value;\n    let results;\n\n    if (query.length === 0) {\n      results = Promise.all([\n        LinkResult({\n          icon: Symbols.HELP,\n          type: \"symbol\",\n          label: i18n.dataset.getHelp,\n          url: headerCommandPaletteButton.dataset.searchHelpUrl,\n          isExternal: true,\n          group: null,\n        }),\n      ]);\n    } else {\n      results = Promise.all(datasources.map((ds) => ds.execute(query))).then(\n        (e) => e.flat(),\n      );\n    }\n\n    results.then((results) => {\n      results = groupResultsByCategory(results);\n\n      // Clear current search results\n      searchResults.innerHTML = \"\";\n\n      if (query.length === 0 || Object.keys(results).length > 0) {\n        for (const [group, items] of Object.entries(results)) {\n          if (group !== \"null\") {\n            const heading = document.createElement(\"p\");\n            heading.className = \"jenkins-command-palette__results__heading\";\n            heading.innerText = group;\n            searchResults.append(heading);\n          }\n\n          items.forEach(function (obj) {\n            const link = createElementFromHtml(obj.render());\n            link.addEventListener(\"mouseenter\", (e) => itemMouseEnter(e));\n            searchResults.append(link);\n          });\n        }\n\n        updateSelectedItem(0);\n      } else {\n        const label = document.createElement(\"p\");\n        label.className = \"jenkins-command-palette__info\";\n        label.innerHTML =\n          \"<span>\" +\n          i18n.dataset.noResultsFor +\n          \"</span> \" +\n          xmlEscape(commandPaletteInput.value);\n        searchResults.append(label);\n      }\n\n      searchResultsContainer.style.height = searchResults.offsetHeight + \"px\";\n      debouncedSpinner.cancel();\n      commandPaletteSearchBarContainer.classList.remove(\n        \"jenkins-search--loading\",\n      );\n    });\n  }\n\n  const debouncedSpinner = debounce(() => {\n    commandPaletteSearchBarContainer.classList.add(\"jenkins-search--loading\");\n  }, 150);\n\n  const debouncedLoad = debounce(() => {\n    renderResults();\n  }, 150);\n\n  commandPaletteInput.addEventListener(\"input\", () => {\n    debouncedSpinner();\n    debouncedLoad();\n  });\n\n  // Helper methods for visibility of command palette\n  function showCommandPalette() {\n    commandPalette.showModal();\n    commandPaletteInput.focus();\n    commandPaletteInput.setSelectionRange(0, commandPaletteInput.value.length);\n\n    renderResults();\n  }\n\n  function hideCommandPalette() {\n    commandPalette.setAttribute(\"closing\", \"\");\n\n    commandPalette.addEventListener(\n      \"animationend\",\n      () => {\n        commandPalette.removeAttribute(\"closing\");\n        commandPalette.close();\n      },\n      { once: true },\n    );\n  }\n\n  function itemMouseEnter(item) {\n    let hoveredItems = document.querySelector(\".\" + hoverClass);\n    if (hoveredItems) {\n      hoveredItems.classList.remove(hoverClass);\n    }\n\n    item.target.classList.add(hoverClass);\n  }\n\n  function updateSelectedItem(index, scrollIntoView = false) {\n    const maxLength = searchResults.getElementsByTagName(\"a\").length;\n    const hoveredItem = document.querySelector(\".\" + hoverClass);\n\n    if (hoveredItem) {\n      hoveredItem.classList.remove(hoverClass);\n    }\n\n    if (index < maxLength) {\n      const element = Array.from(searchResults.getElementsByTagName(\"a\"))[\n        index\n      ];\n      element.classList.add(hoverClass);\n\n      if (scrollIntoView) {\n        element.scrollIntoView();\n      }\n    }\n  }\n}\n\nexport default { init };\n", "import * as Symbols from \"@/util/symbols\";\nimport { createElementFromHtml } from \"@/util/dom\";\n\nfunction init() {\n  window.notificationBar = {\n    OPACITY: 1,\n    DELAY: 3000, // milliseconds to auto-close the notification\n    div: null, // the main 'notification-bar' DIV\n    token: null, // timer for cancelling auto-close\n    defaultIcon: Symbols.INFO,\n    defaultAlertClass: \"jenkins-notification\",\n\n    SUCCESS: {\n      alertClass: \"jenkins-notification jenkins-notification--success\",\n      icon: Symbols.SUCCESS,\n    },\n    WARNING: {\n      alertClass: \"jenkins-notification jenkins-notification--warning\",\n      icon: Symbols.WARNING,\n    },\n    ERROR: {\n      alertClass: \"jenkins-notification jenkins-notification--error\",\n      icon: Symbols.ERROR,\n      sticky: true,\n    },\n\n    init: function () {\n      if (this.div == null) {\n        this.div = document.createElement(\"div\");\n        this.div.id = \"notification-bar\";\n        document.body.insertBefore(this.div, document.body.firstElementChild);\n        const self = this;\n        this.div.onclick = function () {\n          self.hide();\n        };\n      } else {\n        this.div.innerHTML = \"\";\n      }\n    },\n    // cancel pending auto-hide timeout\n    clearTimeout: function () {\n      if (this.token) {\n        window.clearTimeout(this.token);\n      }\n      this.token = null;\n    },\n    // hide the current notification bar, if it's displayed\n    hide: function () {\n      this.clearTimeout();\n      this.div.classList.remove(\"jenkins-notification--visible\");\n      this.div.classList.add(\"jenkins-notification--hidden\");\n    },\n    // show a notification bar\n    show: function (text, options) {\n      options = options || {};\n      this.init();\n\n      this.div.appendChild(\n        createElementFromHtml(options.icon || this.defaultIcon),\n      );\n      const message = this.div.appendChild(document.createElement(\"span\"));\n      message.appendChild(document.createTextNode(text));\n\n      this.div.className = options.alertClass || this.defaultAlertClass;\n      this.div.classList.add(\"jenkins-notification--visible\");\n\n      this.clearTimeout();\n      const self = this;\n      if (!options.sticky) {\n        this.token = window.setTimeout(function () {\n          self.hide();\n        }, this.DELAY);\n      }\n    },\n  };\n}\n\nexport default { init };\n", "import { createElementFromHtml } from \"@/util/dom\";\nimport makeKeyboardNavigable from \"@/util/keyboard\";\nimport { xmlEscape } from \"@/util/security\";\n\nconst SELECTED_CLASS = \"jenkins-dropdown__item--selected\";\n\nfunction init() {\n  const searchBarInputs = document.querySelectorAll(\".jenkins-search__input\");\n\n  Array.from(searchBarInputs)\n    .filter((searchBar) => searchBar.suggestions)\n    .forEach((searchBar) => {\n      const searchWrapper = searchBar.parentElement.parentElement;\n      const searchResultsContainer = createElementFromHtml(\n        `<div class=\"jenkins-search__results-container\"></div>`,\n      );\n      searchWrapper.appendChild(searchResultsContainer);\n      const searchResults = createElementFromHtml(\n        `<div class=\"jenkins-dropdown\"></div>`,\n      );\n      searchResultsContainer.appendChild(searchResults);\n\n      searchBar.addEventListener(\"input\", () => {\n        const query = searchBar.value.toLowerCase();\n\n        // Hide the suggestions if the search query is empty\n        if (query.length === 0) {\n          hideResultsContainer();\n          return;\n        }\n\n        showResultsContainer();\n\n        function appendResults(container, results) {\n          results.forEach((item, index) => {\n            container.appendChild(\n              createElementFromHtml(\n                `<a class=\"jenkins-dropdown__item ${index === 0 ? SELECTED_CLASS : \"\"}\" href=\"${\n                  item.url\n                }\"><div class=\"jenkins-dropdown__item__icon\">${item.icon}</div>${xmlEscape(item.label)}</a>`,\n              ),\n            );\n          });\n\n          if (results.length === 0 && container === searchResults) {\n            container.appendChild(\n              createElementFromHtml(\n                `<p class=\"jenkins-search__results__no-results-label\">No results</p>`,\n              ),\n            );\n          }\n        }\n\n        // Filter results\n        const results = searchBar\n          .suggestions()\n          .filter((item) => item.label.toLowerCase().includes(query))\n          .slice(0, 5);\n\n        searchResults.innerHTML = \"\";\n        appendResults(searchResults, results);\n        searchResultsContainer.style.height = searchResults.offsetHeight + \"px\";\n      });\n\n      function showResultsContainer() {\n        searchResultsContainer.classList.add(\n          \"jenkins-search__results-container--visible\",\n        );\n      }\n\n      function hideResultsContainer() {\n        searchResultsContainer.classList.remove(\n          \"jenkins-search__results-container--visible\",\n        );\n        searchResultsContainer.style.height = \"1px\";\n      }\n\n      searchBar.addEventListener(\"keydown\", (e) => {\n        if (e.key === \"ArrowUp\" || e.key === \"ArrowDown\") {\n          e.preventDefault();\n        }\n      });\n\n      makeKeyboardNavigable(\n        searchResultsContainer,\n        () => searchResults.querySelectorAll(\"a\"),\n        SELECTED_CLASS,\n      );\n\n      // Workaround: Firefox doesn't update the dropdown height correctly so\n      // let's bind the container's height to it's child\n      // Disabled in HtmlUnit\n      if (!window.isRunAsTest) {\n        new ResizeObserver(() => {\n          searchResultsContainer.style.height =\n            searchResults.offsetHeight + \"px\";\n        }).observe(searchResults);\n      }\n\n      searchBar.addEventListener(\"focusin\", () => {\n        if (searchBar.value.length !== 0) {\n          searchResultsContainer.style.height =\n            searchResults.offsetHeight + \"px\";\n          showResultsContainer();\n        }\n      });\n\n      document.addEventListener(\"click\", (event) => {\n        if (searchWrapper.contains(event.target)) {\n          return;\n        }\n\n        hideResultsContainer();\n      });\n    });\n}\n\nexport default { init };\n", "import tippy from \"tippy.js\";\nimport behaviorShim from \"@/util/behavior-shim\";\n\nconst TOOLTIP_BASE = {\n  arrow: false,\n  theme: \"tooltip\",\n  animation: \"tooltip\",\n  duration: 250,\n};\n\n/**\n * Registers tooltips for the given element\n * If called again, destroys any existing tooltip for the element and\n * registers them again (useful for progressive rendering)\n * @param {HTMLElement} element - Registers the tooltips for the given element\n */\nfunction registerTooltip(element) {\n  if (element._tippy && element._tippy.props.theme === \"tooltip\") {\n    element._tippy.destroy();\n  }\n\n  const tooltip = element.getAttribute(\"tooltip\");\n  const htmlTooltip = element.getAttribute(\"data-html-tooltip\");\n  const delay = element.getAttribute(\"data-tooltip-delay\") || 0;\n  let appendTo = document.body;\n  if (element.hasAttribute(\"data-tooltip-append-to-parent\")) {\n    appendTo = \"parent\";\n  }\n  if (\n    tooltip !== null &&\n    tooltip.trim().length > 0 &&\n    (htmlTooltip === null || htmlTooltip.trim().length == 0)\n  ) {\n    tippy(\n      element,\n      Object.assign(\n        {\n          content: () => tooltip.replace(/<br[ /]?\\/?>|\\\\n/g, \"\\n\"),\n          onCreate(instance) {\n            instance.reference.setAttribute(\"title\", instance.props.content);\n          },\n          onShow(instance) {\n            instance.reference.removeAttribute(\"title\");\n          },\n          onHidden(instance) {\n            instance.reference.setAttribute(\"title\", instance.props.content);\n          },\n          appendTo: appendTo,\n          delay: [delay, null],\n        },\n        TOOLTIP_BASE,\n      ),\n    );\n  }\n\n  if (htmlTooltip !== null && htmlTooltip.trim().length > 0) {\n    tippy(\n      element,\n      Object.assign(\n        {\n          content: () => htmlTooltip,\n          allowHTML: true,\n          onCreate(instance) {\n            instance.props.interactive =\n              instance.reference.getAttribute(\"data-tooltip-interactive\") ===\n              \"true\";\n          },\n          appendTo: appendTo,\n          delay: [delay, null],\n        },\n        TOOLTIP_BASE,\n      ),\n    );\n  }\n}\n\n/**\n * Displays a tooltip for three seconds on the provided element after interaction\n * @param {string} text - The tooltip text\n * @param {HTMLElement} element - The element to show the tooltip\n */\nfunction hoverNotification(text, element) {\n  const tooltip = tippy(\n    element,\n    Object.assign(\n      {\n        trigger: \"hover\",\n        offset: [0, 0],\n        content: text,\n        onShow(instance) {\n          setTimeout(() => {\n            instance.hide();\n          }, 3000);\n        },\n      },\n      TOOLTIP_BASE,\n    ),\n  );\n  tooltip.show();\n}\n\nfunction init() {\n  behaviorShim.specify(\n    \"[tooltip], [data-html-tooltip]\",\n    \"-tooltip-\",\n    1000,\n    (element) => {\n      registerTooltip(element);\n    },\n  );\n\n  window.hoverNotification = hoverNotification;\n}\n\nexport default { init };\n", "import behaviorShim from \"@/util/behavior-shim\";\n\nfunction registerStopButton(link) {\n  let question = link.getAttribute(\"data-confirm\");\n  let url = link.getAttribute(\"href\");\n  link.addEventListener(\"click\", function (e) {\n    e.preventDefault();\n    var execute = function () {\n      fetch(url, {\n        method: \"post\",\n        headers: crumb.wrap({}),\n      });\n    };\n    if (question != null) {\n      dialog.confirm(question).then(() => {\n        execute();\n      });\n    } else {\n      execute();\n    }\n  });\n}\n\nfunction init() {\n  behaviorShim.specify(\n    \".stop-button-link\",\n    \"stop-button-link\",\n    0,\n    (element) => {\n      registerStopButton(element);\n    },\n  );\n}\n\nexport default { init };\n", "import behaviorShim from \"@/util/behavior-shim\";\n\nfunction registerConfirmationLink(element) {\n  const post = element.getAttribute(\"data-post\") === \"true\";\n  const href = element.getAttribute(\"data-url\");\n  const message = element.getAttribute(\"data-message\");\n  const title = element.getAttribute(\"data-title\");\n  const destructive = element.getAttribute(\"data-destructive\");\n  let type = \"default\";\n  if (destructive === \"true\") {\n    type = \"destructive\";\n  }\n\n  element.addEventListener(\"click\", function (e) {\n    e.preventDefault();\n    dialog.confirm(title, { message: message, type: type }).then(\n      () => {\n        var form = document.createElement(\"form\");\n        form.setAttribute(\"method\", post ? \"POST\" : \"GET\");\n        form.setAttribute(\"action\", href);\n        if (post) {\n          crumb.appendToForm(form);\n        }\n        document.body.appendChild(form);\n        form.submit();\n      },\n      () => {},\n    );\n    return false;\n  });\n}\n\nfunction init() {\n  behaviorShim.specify(\n    \"A.confirmation-link\",\n    \"confirmation-link\",\n    0,\n    (element) => {\n      registerConfirmationLink(element);\n    },\n  );\n}\n\nexport default { init };\n", "/**\n * Jenkins JS Modules common utility functions\n */\nimport $ from \"jquery\";\nimport wh from \"window-handle\";\nimport Handlebars from \"handlebars\";\n\nvar debug = false;\nvar jenkins = {};\n\n// gets the base Jenkins URL including context path\njenkins.baseUrl = function () {\n  var u = $(\"head\").attr(\"data-rooturl\");\n  if (!u) {\n    u = \"\";\n  }\n  return u;\n};\n\n/**\n * redirect\n */\njenkins.goTo = function (url) {\n  wh.getWindow().location.replace(jenkins.baseUrl() + url);\n};\n\n/**\n * Jenkins AJAX GET callback.\n * If last parameter is an object, will be extended to jQuery options (e.g. pass { error: function() ... } to handle errors)\n */\njenkins.get = function (url, success, options) {\n  if (debug) {\n    console.log(\"get: \" + url);\n  }\n  var args = {\n    url: jenkins.baseUrl() + url,\n    type: \"GET\",\n    cache: false,\n    dataType: \"json\",\n    success: success,\n  };\n  if (options instanceof Object) {\n    $.extend(args, options);\n  }\n  $.ajax(args);\n};\n\n/**\n * Jenkins AJAX POST callback, formats data as a JSON object post\n * If last parameter is an object, will be extended to jQuery options (e.g. pass { error: function() ... } to handle errors)\n */\njenkins.post = function (url, data, success, options) {\n  if (debug) {\n    console.log(\"post: \" + url);\n  }\n\n  // handle crumbs\n  var headers = {};\n  var wnd = wh.getWindow();\n  var crumb;\n  if (\"crumb\" in options) {\n    crumb = options.crumb;\n  } else if (\"crumb\" in wnd) {\n    crumb = wnd.crumb;\n  }\n\n  if (crumb) {\n    headers[crumb.fieldName] = crumb.value;\n  }\n\n  var formBody = data;\n  if (formBody instanceof Object) {\n    if (crumb) {\n      formBody = $.extend({}, formBody);\n      formBody[crumb.fieldName] = crumb.value;\n    }\n    formBody = JSON.stringify(formBody);\n  }\n\n  var args = {\n    url: jenkins.baseUrl() + url,\n    type: \"POST\",\n    cache: false,\n    dataType: \"json\",\n    data: formBody,\n    contentType: \"application/json\",\n    success: success,\n    headers: headers,\n  };\n  if (options instanceof Object) {\n    $.extend(args, options);\n  }\n  $.ajax(args);\n};\n\n/**\n *  handlebars setup, done for backwards compatibility because some plugins depend on it\n */\njenkins.initHandlebars = function () {\n  return Handlebars;\n};\n\n/**\n * Load translations for the given bundle ID, provide the message object to the handler.\n * Optional error handler as the last argument.\n */\njenkins.loadTranslations = function (bundleName, handler, onError) {\n  jenkins.get(\"/i18n/resourceBundle?baseName=\" + bundleName, function (res) {\n    if (res.status !== \"ok\") {\n      if (onError) {\n        onError(res.message);\n      }\n      throw \"Unable to load localization data: \" + res.message;\n    }\n\n    var translations = res.data;\n\n    if (\"undefined\" !== typeof Proxy) {\n      translations = new Proxy(translations, {\n        get: function (target, property) {\n          if (property in target) {\n            return target[property];\n          }\n          if (debug) {\n            console.log('\"' + property + '\" not found in translation bundle.');\n          }\n          return property;\n        },\n      });\n    }\n\n    handler(translations);\n  });\n};\n\n/**\n * Runs a connectivity test, calls handler with a boolean whether there is sufficient connectivity to the internet\n */\njenkins.testConnectivity = function (siteId, handler) {\n  // check the connectivity api\n  var testConnectivity = function () {\n    jenkins.get(\n      \"/updateCenter/connectionStatus?siteId=\" + siteId,\n      function (response) {\n        if (response.status !== \"ok\") {\n          handler(false, true, response.message);\n        }\n\n        // Define statuses, which need additional check iteration via async job on the Jenkins master\n        // Statuses like \"OK\" or \"SKIPPED\" are considered as fine.\n        var uncheckedStatuses = [\"PRECHECK\", \"CHECKING\", \"UNCHECKED\"];\n        if (\n          uncheckedStatuses.indexOf(response.data.updatesite) >= 0 ||\n          uncheckedStatuses.indexOf(response.data.internet) >= 0\n        ) {\n          setTimeout(testConnectivity, 100);\n        } else {\n          // Update site should be always reachable, but we do not require the internet connection\n          // if it's explicitly skipped by the update center\n          if (\n            response.status !== \"ok\" ||\n            response.data.updatesite !== \"OK\" ||\n            (response.data.internet !== \"OK\" &&\n              response.data.internet !== \"SKIPPED\")\n          ) {\n            // no connectivity, but not fatal\n            handler(false, false);\n          } else {\n            handler(true);\n          }\n        }\n      },\n      {\n        error: function (xhr, textStatus, errorThrown) {\n          if (xhr.status === 403) {\n            jenkins.goTo(\"/login\");\n          } else {\n            handler.call({ isError: true, errorMessage: errorThrown });\n          }\n        },\n      },\n    );\n  };\n  testConnectivity();\n};\n\n/**\n * gets the window containing a form, taking in to account top-level iframes\n */\njenkins.getWindow = function ($form) {\n  $form = $($form);\n  var wnd = wh.getWindow();\n  $(top.document)\n    .find(\"iframe\")\n    .each(function () {\n      var windowFrame = this.contentWindow;\n      var $f = $(this).contents().find(\"form\");\n      $f.each(function () {\n        if ($form[0] === this) {\n          wnd = windowFrame;\n        }\n      });\n    });\n  return wnd;\n};\n\n/**\n * Builds a stapler form post\n */\njenkins.buildFormPost = function ($form) {\n  $form = $($form);\n  var wnd = jenkins.getWindow($form);\n  var form = $form[0];\n  if (wnd.buildFormTree(form)) {\n    return (\n      $form.serialize() +\n      \"&\" +\n      $.param({\n        \"core:apply\": \"\",\n        Submit: \"Save\",\n        json: $form.find(\"input[name=json]\").val(),\n      })\n    );\n  }\n  return \"\";\n};\n\n/**\n * Gets the crumb, if crumbs are enabled\n */\njenkins.getFormCrumb = function ($form) {\n  $form = $($form);\n  var wnd = jenkins.getWindow($form);\n  return wnd.crumb;\n};\n\n/**\n * Jenkins Stapler JSON POST callback\n * If last parameter is an object, will be extended to jQuery options (e.g. pass { error: function() ... } to handle errors)\n */\njenkins.staplerPost = function (url, $form, success, options) {\n  $form = $($form);\n  var postBody = jenkins.buildFormPost($form);\n  var crumb = jenkins.getFormCrumb($form);\n  jenkins.post(\n    url,\n    postBody,\n    success,\n    $.extend(\n      {\n        processData: false,\n        contentType: \"application/x-www-form-urlencoded\",\n        crumb: crumb,\n      },\n      options,\n    ),\n  );\n};\n\nexport default jenkins;\n", "import { createElementFromHtml } from \"@/util/dom\";\nimport { CLOSE } from \"@/util/symbols\";\nimport behaviorShim from \"@/util/behavior-shim\";\nimport jenkins from \"@/util/jenkins\";\n\nlet _defaults = {\n  title: null,\n  message: null,\n  cancel: true,\n  maxWidth: \"475px\",\n  minWidth: \"450px\",\n  type: \"default\",\n  hideCloseButton: false,\n  allowEmpty: false,\n  submitButton: false,\n};\n\nlet _typeClassMap = {\n  default: \"\",\n  destructive: \"jenkins-!-destructive-color\",\n};\n\njenkins.loadTranslations(\"jenkins.dialogs\", function (localizations) {\n  window.dialog.translations = localizations;\n  _defaults.cancelText = localizations.cancel;\n  _defaults.okText = localizations.ok;\n});\n\nfunction Dialog(dialogType, options) {\n  this.dialogType = dialogType;\n  this.options = Object.assign({}, _defaults, options);\n  this.init();\n}\n\nDialog.prototype.init = function () {\n  this.dialog = document.createElement(\"dialog\");\n  this.dialog.classList.add(\"jenkins-dialog\");\n  this.dialog.style.maxWidth = this.options.maxWidth;\n  this.dialog.style.minWidth = this.options.minWidth;\n  document.body.appendChild(this.dialog);\n\n  if (this.options.title != null) {\n    const title = createElementFromHtml(`<div class='jenkins-dialog__title'/>`);\n    this.dialog.appendChild(title);\n    title.innerText = this.options.title;\n  }\n\n  if (this.dialogType === \"modal\") {\n    if (this.options.content != null) {\n      const content = createElementFromHtml(\n        `<div class='jenkins-dialog__contents jenkins-dialog__contents--modal'/>`,\n      );\n      content.appendChild(this.options.content);\n      this.dialog.appendChild(content);\n    }\n    if (this.options.hideCloseButton !== true) {\n      const closeButton = createElementFromHtml(`\n          <button class=\"jenkins-dialog__close-button jenkins-button\">\n            <span class=\"jenkins-visually-hidden\">Close</span>\n            ${CLOSE}\n          </button>\n        `);\n      this.dialog.appendChild(closeButton);\n      closeButton.addEventListener(\"click\", () =>\n        this.dialog.dispatchEvent(new Event(\"cancel\")),\n      );\n    }\n    this.dialog.addEventListener(\"click\", function (e) {\n      if (e.target !== e.currentTarget) {\n        return;\n      }\n      this.dispatchEvent(new Event(\"cancel\"));\n    });\n    this.ok = null;\n  } else {\n    this.form = null;\n    if (this.options.form != null && this.dialogType === \"form\") {\n      const contents = createElementFromHtml(\n        `<div class='jenkins-dialog__contents'/>`,\n      );\n      this.form = this.options.form;\n      contents.appendChild(this.options.form);\n      this.dialog.appendChild(contents);\n      behaviorShim.applySubtree(contents, true);\n    }\n    if (this.options.message != null && this.dialogType !== \"form\") {\n      const message = createElementFromHtml(\n        `<div class='jenkins-dialog__contents'/>`,\n      );\n      this.dialog.appendChild(message);\n      message.innerText = this.options.message;\n    }\n\n    if (this.dialogType === \"prompt\") {\n      let inputDiv = createElementFromHtml(`<div class=\"jenkins-dialog__input\">\n          <input data-id=\"input\" type=\"text\" class='jenkins-input'></div>`);\n      this.dialog.appendChild(inputDiv);\n      this.input = inputDiv.querySelector(\"[data-id=input]\");\n      if (!this.options.allowEmpty) {\n        this.input.addEventListener(\"input\", () => this.checkInput());\n      }\n    }\n\n    this.appendButtons();\n\n    this.dialog.addEventListener(\"keydown\", (e) => {\n      if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (this.ok.disabled == false) {\n          this.ok.dispatchEvent(new Event(\"click\"));\n        }\n      }\n      if (e.key === \"Escape\") {\n        e.preventDefault();\n        this.dialog.dispatchEvent(new Event(\"cancel\"));\n      }\n    });\n  }\n};\n\nDialog.prototype.checkInput = function () {\n  if (this.input.value.trim()) {\n    this.ok.disabled = false;\n  } else {\n    this.ok.disabled = true;\n  }\n};\n\nDialog.prototype.appendButtons = function () {\n  const buttons = createElementFromHtml(`<div\n      class=\"jenkins-buttons-row jenkins-buttons-row--equal-width jenkins-dialog__buttons\">\n      <button data-id=\"ok\" type=\"${\n        this.options.submitButton ? \"submit\" : \"button\"\n      }\" class=\"jenkins-button jenkins-button--primary ${\n        _typeClassMap[this.options.type]\n      }\">${this.options.okText}</button>\n      <button data-id=\"cancel\" class=\"jenkins-button\">${\n        this.options.cancelText\n      }</button>\n    </div>`);\n\n  if (this.dialogType === \"form\") {\n    this.form.appendChild(buttons);\n  } else {\n    this.dialog.appendChild(buttons);\n  }\n\n  this.ok = buttons.querySelector(\"[data-id=ok]\");\n  this.cancel = buttons.querySelector(\"[data-id=cancel]\");\n  if (!this.options.cancel) {\n    this.cancel.style.display = \"none\";\n  } else {\n    this.cancel.addEventListener(\"click\", (e) => {\n      e.preventDefault();\n      this.dialog.dispatchEvent(new Event(\"cancel\"));\n    });\n  }\n  if (this.dialogType === \"prompt\" && !this.options.allowEmpty) {\n    this.ok.disabled = true;\n  }\n};\n\nDialog.prototype.show = function () {\n  return new Promise((resolve, cancel) => {\n    this.dialog.showModal();\n    this.dialog.addEventListener(\n      \"cancel\",\n      (e) => {\n        e.preventDefault();\n        this.dialog.remove();\n        cancel();\n      },\n      { once: true },\n    );\n    this.dialog.focus();\n    if (this.input != null) {\n      this.input.focus();\n    }\n    if (\n      this.ok != null &&\n      (this.dialogType != \"form\" || !this.options.submitButton)\n    ) {\n      this.ok.addEventListener(\n        \"click\",\n        (e) => {\n          e.preventDefault();\n\n          let value = true;\n          if (this.dialogType === \"prompt\") {\n            value = this.input.value;\n          }\n          if (this.dialogType === \"form\") {\n            value = new FormData(this.form);\n          }\n          this.dialog.remove();\n          resolve(value);\n        },\n        { once: true },\n      );\n    }\n  });\n};\n\nfunction init() {\n  window.dialog = {\n    modal: function (content, options) {\n      const defaults = {\n        content: content,\n      };\n      options = Object.assign({}, defaults, options);\n      let dialog = new Dialog(\"modal\", options);\n      dialog\n        .show()\n        .then()\n        .catch(() => {});\n    },\n\n    alert: function (title, options) {\n      const defaults = {\n        title: title,\n        cancel: false,\n      };\n      options = Object.assign({}, defaults, options);\n      let dialog = new Dialog(\"alert\", options);\n      dialog\n        .show()\n        .then()\n        .catch(() => {});\n    },\n\n    confirm: function (title, options) {\n      const defaults = {\n        title: title,\n        okText: window.dialog.translations.yes,\n      };\n      options = Object.assign({}, defaults, options);\n      let dialog = new Dialog(\"confirm\", options);\n      return dialog.show();\n    },\n\n    prompt: function (title, options) {\n      const defaults = {\n        title: title,\n      };\n      options = Object.assign({}, defaults, options);\n      let dialog = new Dialog(\"prompt\", options);\n      return dialog.show();\n    },\n\n    form: function (form, options) {\n      const defaults = {\n        form: form,\n        minWidth: \"600px\",\n        maxWidth: \"900px\",\n        submitButton: true,\n        okText: window.dialog.translations.submit,\n      };\n      options = Object.assign({}, defaults, options);\n      let dialog = new Dialog(\"form\", options);\n      return dialog.show();\n    },\n  };\n}\n\nexport default { init };\n", "import Dropdowns from \"@/components/dropdowns\";\nimport CommandPalette from \"@/components/command-palette\";\nimport Notifications from \"@/components/notifications\";\nimport SearchBar from \"@/components/search-bar\";\nimport Tooltips from \"@/components/tooltips\";\nimport StopButtonLink from \"@/components/stop-button-link\";\nimport ConfirmationLink from \"@/components/confirmation-link\";\nimport Dialogs from \"@/components/dialogs\";\n\nDropdowns.init();\nCommandPalette.init();\nNotifications.init();\nSearchBar.init();\nTooltips.init();\nStopButtonLink.init();\nConfirmationLink.init();\nDialogs.init();\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "__webpack_require__.j = 524;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkjenkins_ui\"] = self[\"webpackChunkjenkins_ui\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [96], function() { return __webpack_require__(7274); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["combinePath", "pathOne", "pathTwo", "queryParams", "i", "indexOf", "substring", "endsWith", "specify", "selector", "id", "priority", "behavior", "Behaviour", "applySubtree", "startNode", "includeSelf", "createElementFromHtml", "html", "template", "document", "createElement", "innerHTML", "trim", "content", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "toId", "string", "replace", "toLowerCase", "xmlEscape", "str", "match", "dropdown", "interactive", "trigger", "allowHTML", "placement", "arrow", "theme", "appendTo", "body", "offset", "animation", "duration", "onShow", "instance", "referenceParent", "reference", "parentNode", "classList", "contains", "add", "onHide", "remove", "menuItem", "options", "itemOptions", "Object", "assign", "type", "label", "badgeText", "badgeTooltip", "badgeSeverity", "badge", "text", "tooltip", "severity", "tag", "item", "clazz", "url", "icon", "iconXml", "subMenu", "onClick", "addEventListener", "event", "onKeyPress", "onkeypress", "heading", "separator", "placeholder", "disabled", "makeKeyboardNavigable", "container", "itemsFunc", "selectedClass", "additionalBehaviours", "arguments", "length", "undefined", "hasKeyboardPriority", "window", "getComputedStyle", "visibility", "e", "items", "Array", "from", "selectedItem", "find", "a", "key", "includes", "activeElement", "preventDefault", "next", "scrollAndSelect", "previous", "click", "isInViewport", "scrollIntoView", "focus", "element", "rect", "getBoundingClientRect", "top", "left", "bottom", "innerHeight", "right", "innerWidth", "Templates", "tippy", "behaviorShim", "SELECTED_ITEM_CLASS", "generateDropdown", "callback", "immediate", "_tippy", "props", "destroy", "hideOnClick", "dataset", "onCreate", "onload", "loaded", "isClickInAnyDropdown", "target", "closest", "isClickOnReference", "hide", "generateDropdownItems", "compact", "menuItems", "map", "contents", "for<PERSON>ach", "append<PERSON><PERSON><PERSON>", "querySelectorAll", "evt", "root", "tippyReference", "tippy<PERSON><PERSON>", "show", "querySelector", "isVisible", "isLastDropdown", "filter", "every", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "convertHtmlToItems", "children", "child", "attributes", "dropdownType", "dropdownText", "dropdownId", "dropdownIcon", "dropdownClazz", "dropdownHref", "push", "cloneNode", "validateDropdown", "targetUrl", "method", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "targetElement", "x", "console", "warn", "getMaxSuggestionCount", "defaultValue", "parseInt", "debounce", "running", "setTimeout", "Path", "Utils", "init", "generateJumplistAccessors", "generateDropdowns", "link", "isFirefox", "navigator", "userAgent", "dropdownChevron", "className", "href", "jumplistType", "<PERSON><PERSON><PERSON><PERSON>", "fetch", "then", "response", "json", "mapChildrenItemsToDropdownItems", "catch", "error", "log", "finally", "displayName", "post", "requiresConfirmation", "dialog", "confirm", "message", "form", "setAttribute", "crumb", "appendToForm", "submit", "headers", "wrap", "rsp", "ok", "notificationBar", "SUCCESS", "ERROR", "inpageNavigationBreadcrumb", "chevron", "section", "textContent", "after", "elements", "nextElement<PERSON><PERSON>ling", "mappedItems", "INFO", "WARNING", "CLOSE", "CHEVRON_DOWN", "FUNNEL", "Symbols", "generateButtons", "generateHandles", "convertInputsToButtons", "oldInputs", "oldbtn", "btn", "innerText", "hasAttribute", "isInsideRemovable", "pop", "prototypes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "insertionPoint", "templates", "n", "name", "descriptorId", "title", "withDragDrop", "registerSortableDragDrop", "insert", "nc", "renderOnDemand", "findInsertionPoint", "findBestPosition", "prospect", "current", "order", "desirability", "pos", "count", "bestScore", "bestPos", "d", "matches", "o", "did", "Element", "referenceNode", "insertBefore", "ensureVisible", "layoutUpdateCallback", "call", "has", "oneEach", "generateDropDown", "stopPropagation", "menuContainer", "menu", "createFilter", "filterInput", "applyFilterKeyword", "currentTarget", "filterContainer", "filterKeyword", "value", "style", "display", "button", "popper", "expanded", "convertSuggestionToItem", "suggestion", "createAndShowDropdown", "div", "suggestions", "splice", "s", "updateSuggestions", "filteredItems", "refillOnChange", "params", "position", "URLSearchParams", "addValue", "delimiter", "prev", "lastIndexOf", "min<PERSON><PERSON><PERSON>", "offsetWidth", "word", "split", "reverse", "depends", "q", "qs", "addThis", "Try<PERSON>ach", "nearBy", "queryString", "toString", "idx", "parameters", "Jumplists", "InpageJumplist", "OverflowButton", "HeteroLists", "ComboBox", "Autocomplete", "EXTERNAL_LINK", "HELP", "LinkResult", "group", "render", "isExternal", "search", "searchTerm", "address", "getElementById", "searchUrl", "encodeURIComponent", "Search", "JenkinsSearchSource", "execute", "query", "rootUrl", "head", "rooturl", "correctAddress", "startsWith", "data", "slice", "groupResultsByCategory", "array", "reduce", "hash", "obj", "concat", "datasources", "i18n", "headerCommandPaletteButton", "commandPalette", "commandPaletteWrapper", "commandPaletteInput", "commandPaletteSearchBarContainer", "searchResults", "searchResultsContainer", "hoverClass", "open", "hideCommandPalette", "showCommandPalette", "renderResults", "results", "Promise", "all", "getHelp", "searchHelpUrl", "ds", "flat", "keys", "entries", "append", "itemMouseEnter", "updateSelectedItem", "noResultsFor", "height", "offsetHeight", "debounced<PERSON><PERSON><PERSON>", "cancel", "debouncedLoad", "showModal", "setSelectionRange", "removeAttribute", "close", "once", "hoveredItems", "index", "max<PERSON><PERSON><PERSON>", "getElementsByTagName", "hoveredItem", "OPACITY", "DELAY", "token", "defaultIcon", "defaultAlertClass", "alertClass", "sticky", "self", "onclick", "clearTimeout", "createTextNode", "SELECTED_CLASS", "searchBarInputs", "searchBar", "searchWrapper", "parentElement", "hideResultsContainer", "showResultsContainer", "appendResults", "isRunAsTest", "ResizeObserver", "observe", "TOOLTIP_BASE", "registerTooltip", "htmlTooltip", "delay", "onHidden", "hoverNotification", "registerStopButton", "question", "registerConfirmationLink", "destructive", "$", "wh", "Handlebars", "debug", "jenkins", "baseUrl", "u", "attr", "goTo", "getWindow", "location", "get", "success", "args", "cache", "dataType", "extend", "ajax", "wnd", "fieldName", "formBody", "JSON", "stringify", "contentType", "initHandlebars", "loadTranslations", "bundleName", "handler", "onError", "res", "status", "translations", "Proxy", "property", "testConnectivity", "siteId", "uncheckedStatuses", "updatesite", "internet", "xhr", "textStatus", "errorThrown", "isError", "errorMessage", "$form", "each", "windowFrame", "contentWindow", "$f", "buildFormPost", "buildFormTree", "serialize", "param", "Submit", "val", "getFormCrumb", "staplerPost", "postBody", "processData", "_defaults", "max<PERSON><PERSON><PERSON>", "hideClose<PERSON><PERSON>on", "allowEmpty", "submitButton", "_typeClassMap", "default", "localizations", "cancelText", "okText", "Dialog", "dialogType", "prototype", "closeButton", "dispatchEvent", "Event", "inputDiv", "input", "checkInput", "appendButtons", "buttons", "resolve", "FormData", "modal", "defaults", "alert", "yes", "prompt", "Dropdowns", "CommandPalette", "Notifications", "SearchBar", "Tooltips", "StopButtonLink", "ConfirmationLink", "Dialogs"], "sourceRoot": ""}