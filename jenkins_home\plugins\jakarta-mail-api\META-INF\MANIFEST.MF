Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Jakarta Mail API
Specification-Version: 2.1
Implementation-Title: Jakarta Mail API
Implementation-Version: 2.1.3-2
Group-Id: io.jenkins.plugins
Artifact-Id: jakarta-mail-api
Short-Name: jakarta-mail-api
Long-Name: Jakarta Mail API
Url: https://github.com/jenkinsci/jakarta-mail-api-plugin
Plugin-Version: 2.1.3-2
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: jakarta-activation-api:2.1.3-1
Plugin-Developers: 
Support-Dynamic-Loading: true
Plugin-License-Name: EPL-2.0
Plugin-License-Url: https://opensource.org/licenses/EPL-2.0
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/jakarta-mail-
 api-plugin.git
Plugin-ScmTag: jakarta-mail-api-2.1.3-2
Plugin-ScmUrl: https://github.com/jenkinsci/jakarta-mail-api-plugin
Implementation-Build: f6136798dfeb47cfe479200d266cc717bd10b9f4

