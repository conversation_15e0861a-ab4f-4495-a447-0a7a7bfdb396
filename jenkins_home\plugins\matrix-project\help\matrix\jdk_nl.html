<div>
  Geef uw JDK(s) op waarmee U uw bouwpoging wenst te lanceren.  Indien U ervoor kiest geen specifieke JDK(s) op te geven, zal de standaard JDK gebruikt worden.  Hierbij wordt er vanuit gegaan dat er geen <tt>JAVA_HOME</tt> gedefinieerd werd en dat het <tt>java</tt> commando niet op uw <tt>PATH</tt> aanwezig is. Indien U meerdere JDKs selecteert, zal uw matrixconfiguratie al deze omvatten.
  <br>
  Meerdere waarden zijn typisch nuttig wanneer een job onder meerdere JDKs getest dient te worden.
  <br>
  Gedurende uw bouwpoging is de gekozen versie van de JDK beschikbaar als de "jdk"-dimensie.
  Zie de hulpagina over dimensies voor meer informatie over het gebruik van dimensies.
</div>