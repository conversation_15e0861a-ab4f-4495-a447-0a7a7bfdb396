Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Jenkins Git client plugin
Specification-Version: 6.1
Implementation-Title: Jenkins Git client plugin
Implementation-Version: 6.1.3
Group-Id: org.jenkins-ci.plugins
Artifact-Id: git-client
Short-Name: git-client
Long-Name: Jenkins Git client plugin
Url: https://github.com/jenkinsci/git-client-plugin
Plugin-Version: 6.1.3
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: mina-sshd-api-common:2.14.0-143.v2b_362fc39576,mina
 -sshd-api-core:2.14.0-143.v2b_362fc39576,gson-api:2.12.1-113.v347686d67
 29f,configuration-as-code:1932.v75cb_b_f1b_698d;resolution:=optional,ap
 ache-httpcomponents-client-4-api:4.5.14-269.vfa_2321039a_83,credentials
 :1408.va_622a_b_f5b_1b_1,script-security:1369.v9b_98a_4e95b_2d,ssh-cred
 entials:349.vb_8b_6b_9709f5b_,structs:338.v848422169819
Plugin-Developers: 
Plugin-License-Name: MIT License
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/git-client-pl
 ugin
Plugin-ScmTag: git-client-6.1.3
Plugin-ScmUrl: https://github.com/jenkinsci/git-client-plugin
Implementation-Build: 9d423899843347b3d5de94002605aa03258b64ab

