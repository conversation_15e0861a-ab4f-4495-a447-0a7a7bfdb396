<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>antisamy-markup-formatter</artifactId>
  <version>173.v680e3a_b_69ff3</version>
  <packaging>hpi</packaging>
  <name>OWASP Markup Formatter Plugin</name>
  <description>Sanitize HTML markup in user-submitted text to be displayed on the Jenkins UI.</description>
  <url>https://github.com/jenkinsci/antisamy-markup-formatter-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/antisamy-markup-formatter-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/antisamy-markup-formatter-plugin.git</developerConnection>
    <tag>680e3ab69ff3d7069b328fb6322a906a509f9a53</tag>
    <url>https://github.com/jenkinsci/antisamy-markup-formatter-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
      <artifactId>owasp-java-html-sanitizer</artifactId>
      <version>20220608.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
