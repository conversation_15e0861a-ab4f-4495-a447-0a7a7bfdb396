<div>
  <PERSON>'in bu agent &#252;z<PERSON><PERSON> e&#351; z<PERSON><PERSON>&#305; ka&#231; tane
  yap&#305;land&#305;rma y&#252;r&#252;tebilece&#287;ini kontrol eder. <PERSON><PERSON>,
  b<PERSON><PERSON> girile<PERSON>k de&#287;er, <PERSON>'in ne kadar y&#252;ke maruz
  kalaca&#287;&#305;n&#305; belirler. Ba&#351;lang&#305;&#231; i&#231;in,
  i&#351;lemci say&#305;s&#305; iyi bir rakam olabilir.

  <p>
    Bu say&#305;y&#305; art&#305;rmak, yap&#305;land&#305;rmalar&#305;n daha
    uzun s&#252;rmesine yol a&#231;sa da, toplam &#252;retim kapasitesinde bir
    art&#305;&#351; sa&#287;layacakt&#305;r, &#231;&#252;nk&#252; bu durum, bir
    yap&#305;land&#305;rma I/O i&#231;in bekle<PERSON>en, <PERSON>'nun ba&#351;ka bir
    yap&#305;land&#305;rma ile u&#287;ra&#351;mas&#305;na yol
    a&#231;acakt&#305;r.
  </p>

  <p>
    Bu de&#287;eri 0 yapmak, Jenkins'in, konfig&#252;rasyonunu kaybetmeden,
    devre d&#305;&#351;&#305; b&#305;rak&#305;lm&#305;&#351; bir agent'i
    silmesine olanak tan&#305;r.
  </p>
</div>
