<?xml version='1.1' encoding='UTF-8'?>
<flow-build plugin="workflow-job@1532.va_9a_d244074a_3">
  <actions>
    <hudson.model.CauseAction>
      <causeBag class="linked-hash-map">
        <entry>
          <hudson.model.Cause_-UserIdCause>
            <userId>admin</userId>
          </hudson.model.Cause_-UserIdCause>
          <int>1</int>
        </entry>
      </causeBag>
    </hudson.model.CauseAction>
    <jenkins.metrics.impl.TimeInQueueAction plugin="metrics@4.2.32-476.v5042e1c1edd7">
      <queuingDurationMillis>23</queuingDurationMillis>
      <blockedDurationMillis>0</blockedDurationMillis>
      <buildableDurationMillis>0</buildableDurationMillis>
      <waitingDurationMillis>1</waitingDurationMillis>
    </jenkins.metrics.impl.TimeInQueueAction>
  </actions>
  <queueId>16</queueId>
  <timestamp>1750094204999</timestamp>
  <startTime>1750094205084</startTime>
  <result>FAILURE</result>
  <duration>411</duration>
  <charset>UTF-8</charset>
  <keepLog>false</keepLog>
  <completed>true</completed>
  <checkouts class="hudson.util.PersistedList"/>
</flow-build>