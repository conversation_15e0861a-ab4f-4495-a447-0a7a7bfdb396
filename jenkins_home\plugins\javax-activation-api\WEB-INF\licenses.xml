<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='javax-activation-api' version='1.2.0-8'><l:dependency name='JavaBeans Activation Framework (JAF) API' groupId='io.jenkins.plugins' artifactId='javax-activation-api' version='1.2.0-8' url='https://github.com/jenkinsci/javax-activation-api-plugin'><l:description>Plugin providing the JavaBeans Activation Framework (JAF) API for other plugins</l:description><l:license name='CDDL-1.1' url='https://spdx.org/licenses/CDDL-1.1.html'/></l:dependency><l:dependency name='JavaBeans Activation Framework' groupId='com.sun.activation' artifactId='javax.activation' version='1.2.0' url='http://java.net/all/javax.activation/'><l:description>JavaBeans Activation Framework</l:description><l:license name='CDDL/GPLv2+CE' url='https://github.com/javaee/activation/blob/master/LICENSE.txt'/></l:dependency></l:dependencies>