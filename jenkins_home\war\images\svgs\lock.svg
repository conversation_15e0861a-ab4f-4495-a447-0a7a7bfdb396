<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:export-ydpi="90.000000"
   inkscape:export-xdpi="90.000000"
   inkscape:export-filename="/home/<USER>/Desktop/wi-fi.png"
   width="48px"
   height="48px"
   id="svg11300"
   sodipodi:version="0.32"
   inkscape:version="0.43+devel"
   sodipodi:docbase="/home/<USER>/src/cvs/tango-icon-theme/scalable/emblems"
   sodipodi:docname="emblem-readonly.svg">
  <defs
     id="defs3">
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5060"
       id="radialGradient6719"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-2.774389,0,0,1.969706,112.7623,-872.8854)"
       cx="605.71429"
       cy="486.64789"
       fx="605.71429"
       fy="486.64789"
       r="117.14286" />
    <linearGradient
       inkscape:collect="always"
       id="linearGradient5060">
      <stop
         style="stop-color:black;stop-opacity:1;"
         offset="0"
         id="stop5062" />
      <stop
         style="stop-color:black;stop-opacity:0;"
         offset="1"
         id="stop5064" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5060"
       id="radialGradient6717"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(2.774389,0,0,1.969706,-1891.633,-872.8854)"
       cx="605.71429"
       cy="486.64789"
       fx="605.71429"
       fy="486.64789"
       r="117.14286" />
    <linearGradient
       id="linearGradient5048">
      <stop
         style="stop-color:black;stop-opacity:0;"
         offset="0"
         id="stop5050" />
      <stop
         id="stop5056"
         offset="0.5"
         style="stop-color:black;stop-opacity:1;" />
      <stop
         style="stop-color:black;stop-opacity:0;"
         offset="1"
         id="stop5052" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5048"
       id="linearGradient6715"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(2.774389,0,0,1.969706,-1892.179,-872.8854)"
       x1="302.85715"
       y1="366.64789"
       x2="302.85715"
       y2="609.50507" />
    <linearGradient
       id="linearGradient11327">
      <stop
         style="stop-color:#7d6400;stop-opacity:1;"
         offset="0"
         id="stop11329" />
      <stop
         style="stop-color:#be9700;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop11331" />
    </linearGradient>
    <linearGradient
       id="linearGradient2092">
      <stop
         id="stop2094"
         offset="0"
         style="stop-color:#fff7b0;stop-opacity:1;" />
      <stop
         style="stop-color:#ffec41;stop-opacity:1.0000000;"
         offset="0.20999999"
         id="stop2098" />
      <stop
         id="stop2293"
         offset="0.83999997"
         style="stop-color:#e2cc00;stop-opacity:1;" />
      <stop
         id="stop2100"
         offset="1"
         style="stop-color:#c3af00;stop-opacity:1;" />
    </linearGradient>
    <linearGradient
       id="linearGradient11335">
      <stop
         style="stop-color:#6f716d;stop-opacity:1;"
         offset="0"
         id="stop11337" />
      <stop
         style="stop-color:#9ea09c;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop11339" />
    </linearGradient>
    <linearGradient
       id="linearGradient10591">
      <stop
         style="stop-color:#cad0c6;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop10593" />
      <stop
         id="stop10599"
         offset="0.50000000"
         style="stop-color:#eaece9;stop-opacity:1.0000000;" />
      <stop
         style="stop-color:#c5cbc0;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop10595" />
    </linearGradient>
    <linearGradient
       id="linearGradient11520">
      <stop
         id="stop11522"
         offset="0.0000000"
         style="stop-color:#ffffff;stop-opacity:1.0000000;" />
      <stop
         id="stop11524"
         offset="1.0000000"
         style="stop-color:#dcdcdc;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient11508"
       inkscape:collect="always">
      <stop
         id="stop11510"
         offset="0"
         style="stop-color:#000000;stop-opacity:1;" />
      <stop
         id="stop11512"
         offset="1"
         style="stop-color:#000000;stop-opacity:0;" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient11508"
       id="radialGradient1348"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.338462,-1.435476e-15,29.48178)"
       cx="30.203562"
       cy="44.565483"
       fx="30.203562"
       fy="44.565483"
       r="6.5659914" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient11520"
       id="radialGradient1366"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.995058,-1.651527e-32,0.000000,1.995058,-24.32488,-35.70087)"
       cx="24.445690"
       cy="35.878170"
       fx="24.445690"
       fy="35.878170"
       r="20.530962" />
  </defs>
  <sodipodi:namedview
     stroke="#ef2929"
     fill="#729fcf"
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="0.25490196"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="1"
     inkscape:cx="24.194962"
     inkscape:cy="-13.983676"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:showpageshadow="false"
     inkscape:window-width="872"
     inkscape:window-height="707"
     inkscape:window-x="155"
     inkscape:window-y="230" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://jimmac.musichall.cz</dc:source>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:title>Read Only Emblem</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>emblem</rdf:li>
            <rdf:li>read-only</rdf:li>
            <rdf:li>nowrite</rdf:li>
          </rdf:Bag>
        </dc:subject>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <g
       transform="matrix(2.243788e-2,0,0,2.086758e-2,44.06795,40.547)"
       id="g6707">
      <rect
         style="opacity:0.40206185;color:black;fill:url(#linearGradient6715);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
         id="rect6709"
         width="1339.6335"
         height="478.35718"
         x="-1559.2523"
         y="-150.69685" />
      <path
         style="opacity:0.40206185;color:black;fill:url(#radialGradient6717);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
         d="M -219.61876,-150.68038 C -219.61876,-150.68038 -219.61876,327.65041 -219.61876,327.65041 C -76.744594,328.55086 125.78146,220.48075 125.78138,88.454235 C 125.78138,-43.572302 -33.655436,-150.68036 -219.61876,-150.68038 z "
         id="path6711"
         sodipodi:nodetypes="cccc" />
      <path
         sodipodi:nodetypes="cccc"
         id="path6713"
         d="M -1559.2523,-150.68038 C -1559.2523,-150.68038 -1559.2523,327.65041 -1559.2523,327.65041 C -1702.1265,328.55086 -1904.6525,220.48075 -1904.6525,88.454235 C -1904.6525,-43.572302 -1745.2157,-150.68036 -1559.2523,-150.68038 z "
         style="opacity:0.40206185;color:black;fill:url(#radialGradient6719);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible" />
    </g>
    <rect
       ry="5.4548240"
       rx="5.4548240"
       y="3.5233452"
       x="4.4147282"
       height="40.061924"
       width="40.061924"
       id="rect11518"
       style="opacity:1.0000000;color:#000000;fill:url(#radialGradient1366);fill-opacity:1.0000000;fill-rule:evenodd;stroke:#9b9b9b;stroke-width:1.0000000;stroke-linecap:butt;stroke-linejoin:bevel;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible" />
    <rect
       style="opacity:1.0000000;color:#000000;fill:none;fill-opacity:1.0000000;fill-rule:evenodd;stroke:#ffffff;stroke-width:0.99999976;stroke-linecap:butt;stroke-linejoin:bevel;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible"
       id="rect11528"
       width="37.696587"
       height="37.696587"
       x="5.5973887"
       y="4.7060070"
       rx="4.2426391"
       ry="4.2426391" />
    <path
       style="opacity:0.69886361;color:#000000;fill:#888a85;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.9999997;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:block;overflow:visible"
       d="M 23.906254,10.878347 C 19.835833,11.005873 17.137508,12.963117 17.137508,17.000087 L 17.137508,21.120489 L 16.221429,21.120489 C 15.54928,21.120489 15,21.583436 15,22.163203 L 15,32.993974 C 15,33.573741 15.54928,34.036689 16.221429,34.036688 L 32.795528,34.036688 C 33.467687,34.036688 34,33.57374 34,32.993974 L 34,22.163203 C 34,21.583437 33.467687,21.120489 32.795528,21.120489 L 31.862492,21.120489 L 31.87946,17.067359 C 31.87946,12.782023 28.950269,10.931241 24.516968,10.878347 C 24.310278,10.875868 24.10644,10.872076 23.906254,10.878347 z M 24.313392,13.58604 C 24.388241,13.584353 24.4735,13.58604 24.550894,13.58604 C 29.469737,13.58604 29.092541,17.084436 29.165175,18.126891 L 29.165175,21.120489 L 19.902675,21.120489 L 19.902675,18.143709 C 19.885214,17.109531 19.597992,13.692412 24.313392,13.58604 z "
       id="path2086"
       inkscape:r_cx="true"
       inkscape:r_cy="true" />
  </g>
</svg>
