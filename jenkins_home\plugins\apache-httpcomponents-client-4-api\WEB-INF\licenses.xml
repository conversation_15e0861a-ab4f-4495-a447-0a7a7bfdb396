<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='apache-httpcomponents-client-4-api' version='4.5.14-269.vfa_2321039a_83'><l:dependency name='Jenkins Apache HttpComponents Client 4.x API Plugin' groupId='org.jenkins-ci.plugins' artifactId='apache-httpcomponents-client-4-api' version='4.5.14-269.vfa_2321039a_83' url='https://github.com/jenkinsci/apache-httpcomponents-client-4-api-plugin'><l:description>Bundles Apache HttpComponents Client 4.x and allows it to be used by Jenkins plugins</l:description><l:license name='MIT License' url='https://opensource.org/license/mit/'/></l:dependency><l:dependency name='Apache HttpCore NIO' groupId='org.apache.httpcomponents' artifactId='httpcore-nio' version='4.4.16' url='http://hc.apache.org/httpcomponents-core-ga'><l:description>Apache HttpComponents Core (non-blocking I/O)</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpClient' groupId='org.apache.httpcomponents' artifactId='httpclient' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents Client</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpClient Fluent API' groupId='org.apache.httpcomponents' artifactId='fluent-hc' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents Client fluent API</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpClient Cache' groupId='org.apache.httpcomponents' artifactId='httpclient-cache' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents HttpClient - Cache</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpAsyncClient Cache' groupId='org.apache.httpcomponents' artifactId='httpasyncclient-cache' version='4.1.5' url='http://hc.apache.org/httpcomponents-asyncclient'><l:description>Apache HttpComponents AsyncClient Cache</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpClient Mime' groupId='org.apache.httpcomponents' artifactId='httpmime' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents HttpClient - MIME coded entities</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpCore' groupId='org.apache.httpcomponents' artifactId='httpcore' version='4.4.16' url='http://hc.apache.org/httpcomponents-core-ga'><l:description>Apache HttpComponents Core (blocking I/O)</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpAsyncClient' groupId='org.apache.httpcomponents' artifactId='httpasyncclient' version='4.1.5' url='http://hc.apache.org/httpcomponents-asyncclient'><l:description>Apache HttpComponents AsyncClient</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>