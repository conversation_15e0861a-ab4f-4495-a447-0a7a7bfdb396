<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>json-api</artifactId>
  <version>20250517-153.vc8a_a_d87c0ce3</version>
  <packaging>hpi</packaging>
  <name>JSON Api Plugin</name>
  <description>The Jenkins Plugins Parent POM Project</description>
  <url>https://github.com/jenkinsci/json-api-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT No Attribution License</name>
      <url>https://opensource.org/license/mit-0/</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/json-api-plugin</connection>
    <developerConnection>scm:git:https://github.com/jenkinsci/json-api-plugin</developerConnection>
    <tag>c8aad87c0ce32e1773a4291bc81fff3413e09477</tag>
    <url>https://github.com/jenkinsci/json-api-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>org.json</groupId>
      <artifactId>json</artifactId>
      <version>20250517</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.main</groupId>
      <artifactId>jenkins-core</artifactId>
      <version>2.479.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.4</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.65</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
