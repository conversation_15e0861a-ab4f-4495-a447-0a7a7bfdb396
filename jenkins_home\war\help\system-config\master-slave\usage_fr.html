﻿<div>
  Contr<PERSON><PERSON> comment <PERSON> programme les builds sur cette machine.

  <dl>
    <dt><b>Utiliser ce noeud autant que possible</b></dt>
    <dd>
      C'est l'option normale, configurée par défaut. Dans ce mode, Jenkins
      utilise librement cet agent. A chaque fois qu'un build peut être exécuté
      sur cet agent, <PERSON> le fera.
    </dd>

    <dt>
      <b>Réserver ce noeud uniquement pour les jobs qui lui sont attachés</b>
    </dt>
    <dd>
      Dans ce mode, Jenkins ne construira un projet sur cette machine que
      lorsque un projet aura été assigné spécifiquement à cet agent. Cela permet
      à un agent de n'être utilisé que pour certains types de jobs. Par exemple,
      pour lancer des tests de performance en continu à partir de Jenkins, vous
      pouvez utiliser cette option avec le
      <i>nombre d'exécuteurs</i>
      à 1, afin de ne lancer qu'un test de performance à la fois. Cet exécuteur
      ne sera pas bloqué par les autres builds qui peuvent être lancés sur les
      autres agents.
    </dd>
  </dl>
</div>
