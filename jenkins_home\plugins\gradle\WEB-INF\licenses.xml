<l:dependencies xmlns:l='licenses' version='2.15' artifactId='gradle-plugin' groupId='org.jenkins-ci.plugins'>
  <l:dependency version='2.15' artifactId='gradle-plugin' groupId='org.jenkins-ci.plugins' name='Gradle Plugin' url='https://github.com/jenkinsci/gradle-plugin'>
    <l:description>This plugin adds Gradle support to Jenkins</l:description>
  </l:dependency>
  <l:dependency version='1.9.0' artifactId='commons-validator' groupId='commons-validator' name='Apache Commons Validator' url='http://commons.apache.org/proper/commons-validator/'>
    <l:description>
    Apache Commons Validator provides the building blocks for both client side validation and server side data validation.
    It may be used standalone or with a framework like Struts.
  </l:description>
  </l:dependency>
  <l:dependency version='2.1' artifactId='commons-digester' groupId='commons-digester' name='Commons Digester' url='http://commons.apache.org/digester/'>
    <l:description>
    The Digester package lets you configure an XML to Java object mapping module
    which triggers certain actions called rules whenever a particular 
    pattern of nested XML elements is recognized.
  </l:description>
  </l:dependency>
  <l:dependency version='3.2.2' artifactId='commons-collections' groupId='commons-collections' name='Apache Commons Collections' url='http://commons.apache.org/collections/'>
    <l:description>Types that extend and augment the Java Collections Framework.</l:description>
  </l:dependency>
</l:dependencies>