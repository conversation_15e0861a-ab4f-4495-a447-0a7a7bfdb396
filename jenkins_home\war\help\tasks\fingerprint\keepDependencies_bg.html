<div>
  Когато опцията е избрана, всички
  <a href="lastSuccessfulBuild/fingerprint">реферирани изграждания</a>
  от изграждането на този проект (чрез цифрови отпечатъци) няма да бъдат
  засегнати от редовната смяна на журналните файлове.

  <p>
    Когато едно изграждане зависи от други в Jenkins и от време на време трябва
    да задавате етикет на състоянието на работното пространство, е много удобно
    да зададете етикета и на всички зависимости в Jenkins. Проблем възниква,
    когато смяната на журналните файлове се е случила между момента на
    изграждането и момента на задаване на етикет. В такъв случай е невъзможно да
    дадете етикета и на всички зависимости.
  </p>

  <p>
    Тази настройка предотвратява това като „заключва“ изгражданията, от които
    това зависи и задаването на етикет е винаги възможно.
  </p>
</div>
