FROM jenkins/jenkins:lts-jdk17

# Switch to root to install Docker and fix permissions
USER root

# Install Docker CLI
RUN apt-get update && \
    apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release && \
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli docker-compose-plugin && \
    rm -rf /var/lib/apt/lists/*

# Add jenkins user to docker group
RUN groupadd -g 999 docker || true && \
    usermod -aG docker jenkins

# Create jenkins_home directory with correct permissions
RUN mkdir -p /var/jenkins_home && \
    chown -R jenkins:jenkins /var/jenkins_home && \
    chmod -R 755 /var/jenkins_home

# Switch back to jenkins user
USER jenkins

# Copy initialization script
COPY jenkins-init.groovy /usr/share/jenkins/ref/init.groovy.d/

# Skip the setup wizard
ENV JAVA_OPTS="-Djenkins.install.runSetupWizard=false"

# Install suggested plugins
RUN jenkins-plugin-cli --plugins \
    ant:latest \
    build-timeout:latest \
    credentials-binding:latest \
    email-ext:latest \
    git:latest \
    github-branch-source:latest \
    gradle:latest \
    ldap:latest \
    mailer:latest \
    matrix-auth:latest \
    pam-auth:latest \
    pipeline-github-lib:latest \
    pipeline-stage-view:latest \
    ssh-slaves:latest \
    timestamper:latest \
    workflow-aggregator:latest \
    ws-cleanup:latest \
    docker-workflow:latest \
    docker-plugin:latest