﻿<div>
  Если ваша конфигурационная матрица нуждается в дополнительных осях (размерностях, степенях свободы),
  вы можете настроить их здесь.
  <p>
  Например, скажем вы собираетесь запускать тесты для вашего приложения использующего СУБД
  и вам необходимо проверить его с тремя СУБД: MySQL, PostgreSQL и Oracle. Ваш сборочный 
  скрипт разработан таким образом, что вы можете протестировать работу приложения с конкретной 
  СУБД с помощью команды <tt>ant -Ddatabase=mysql</tt>.
  <p>
  Это и есть концепция осей. Вы можете создать переменную с именем "database", которая 
  может принимать 3 значения. Когда вы её настроите, Jenkins будет запускать 3 сборки, каждую с
  одним элементом из области значений переменной "database", чтобы полностью покрыть 
  конфигурационную матрицу.
  <p>
  Переменные, описанные здесь, доступны сборке как переменные окружения. Вдобавок, 
  для Ant и Maven, переменные также предоставляются как свойства, как будто 
  в командной строке было указано <tt>-D<i>ИмяПеременной</i>=<i>Значение</i></tt>.
  <p>
  В случае, когда определено несколько осей, все возможные комбинации будут использованы 
  при сборке. Множественные значения в метках и JDK интерпретируются таким же образом.
  Так что если вы укажете jdk=[JDK5,JDK6], database=[mysql,postgresql,oracle], 
  container=[jetty,tomcat], тогда каждая сборка будет содержать 2x3x2=12 различных 
  подсборок.
</div>