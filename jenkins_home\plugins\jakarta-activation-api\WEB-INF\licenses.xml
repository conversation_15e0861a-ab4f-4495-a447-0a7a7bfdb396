<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='jakarta-activation-api' version='2.1.3-2'><l:dependency name='Jakarta Activation API' groupId='io.jenkins.plugins' artifactId='jakarta-activation-api' version='2.1.3-2' url='https://github.com/jenkinsci/jakarta-activation-api-plugin'><l:description>Plugin providing the Jakarta Activation API for other plugins</l:description><l:license name='BSD-3-Clause' url='https://opensource.org/licenses/BSD-3-Clause'/></l:dependency><l:dependency name='Jakarta Activation API' groupId='jakarta.activation' artifactId='jakarta.activation-api' version='2.1.3' url='https://github.com/jakartaee/jaf-api'><l:description>Jakarta Activation API 2.1 Specification</l:description><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Angus Activation Registries' groupId='org.eclipse.angus' artifactId='angus-activation' version='2.0.2' url='https://github.com/eclipse-ee4j/angus-activation/angus-activation'><l:description>Angus Activation Registries Implementation</l:description><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency></l:dependencies>