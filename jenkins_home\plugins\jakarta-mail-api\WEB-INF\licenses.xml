<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='jakarta-mail-api' version='2.1.3-2'><l:dependency name='Jakarta Mail API' groupId='io.jenkins.plugins' artifactId='jakarta-mail-api' version='2.1.3-2' url='https://github.com/jenkinsci/jakarta-mail-api-plugin'><l:description>Plugin providing the Jakarta Mail API for other plugins</l:description><l:license name='EPL-2.0' url='https://opensource.org/licenses/EPL-2.0'/></l:dependency><l:dependency name='Angus Mail Provider' groupId='org.eclipse.angus' artifactId='angus-mail' version='2.0.3' url='http://eclipse-ee4j.github.io/angus-mail/angus-mail'><l:description>Angus Mail Provider</l:description><l:license name='EPL 2.0' url='http://www.eclipse.org/legal/epl-2.0'/><l:license name='GPL2 w/ CPE' url='https://www.gnu.org/software/classpath/license.html'/><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Jakarta Mail API' groupId='jakarta.mail' artifactId='jakarta.mail-api' version='2.1.3' url='https://projects.eclipse.org/projects/ee4j/jakarta.mail-api'><l:description>Jakarta Mail API 2.1 Specification API</l:description><l:license name='EPL 2.0' url='http://www.eclipse.org/legal/epl-2.0'/><l:license name='GPL2 w/ CPE' url='https://www.gnu.org/software/classpath/license.html'/><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency></l:dependencies>