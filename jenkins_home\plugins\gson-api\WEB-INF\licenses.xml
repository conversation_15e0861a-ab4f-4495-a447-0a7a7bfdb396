<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='gson-api' version='2.13.1-139.v4569c2ef303f'><l:dependency name='Gson API Plugin' groupId='io.jenkins.plugins' artifactId='gson-api' version='2.13.1-139.v4569c2ef303f' url='https://github.com/jenkinsci/gson-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Gson' groupId='com.google.code.gson' artifactId='gson' version='2.13.1' url='https://github.com/google/gson'><l:description>Gson JSON library</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>