﻿<div>
  Если эта настройка включена, все
  <a href="lastSuccessfulBuild/fingerprint">сборки на которые есть ссылки</a>
  из этого проекта (через отпечаток) будут защищены от ротации (удаления
  информации о старых сборках).

  <p>
    Когда ваша задача зависит от других задач в Jenkins и вам по какой-то
    причине нужно поставить маркировку (tag) на сборочную директорию, обычно
    может понадобиться также промаркировать все задачи от которого зависит
    текущая. Проблема заключается в том, что ротация журналов может уже удалить
    записи о сборке, которую использует ваш проект (если в зависимостях довольно
    много проектов), и если такое произойдет то вы уже не сможете промаркировать
    зависимости надежным образом.
  </p>

  <p>
    Эта функция устраняет проблему "блокированием" тех сборок, от которых вы
    зависите, гарантируя таким образом, что вы всегда сможете промаркировать все
    дерево зависимостей.
  </p>
</div>
