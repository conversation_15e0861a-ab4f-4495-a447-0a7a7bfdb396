<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='asm-api' version='9.8-135.vb_2239d08ee90'><l:dependency name='ASM API Plugin' groupId='io.jenkins.plugins' artifactId='asm-api' version='9.8-135.vb_2239d08ee90' url='https://github.com/jenkinsci/asm-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/license/mit/'/></l:dependency><l:dependency name='asm' groupId='org.ow2.asm' artifactId='asm' version='9.8' url='http://asm.ow2.io/'><l:description>ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='asm-tree' groupId='org.ow2.asm' artifactId='asm-tree' version='9.8' url='http://asm.ow2.io/'><l:description>Tree API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='asm-analysis' groupId='org.ow2.asm' artifactId='asm-analysis' version='9.8' url='http://asm.ow2.io/'><l:description>Static code analysis API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='asm-commons' groupId='org.ow2.asm' artifactId='asm-commons' version='9.8' url='http://asm.ow2.io/'><l:description>Usefull class adapters based on ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='asm-util' groupId='org.ow2.asm' artifactId='asm-util' version='9.8' url='http://asm.ow2.io/'><l:description>Utilities for ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency></l:dependencies>