<div>
  Yap&#305;land&#305;rmalar&#305;n &#231;al&#305;&#351;t&#305;r&#305;laca&#287;&#305; JDK(lar&#305;) belirleyiniz. E&#287;er, herhangi biri se&#231;il<PERSON>z ise
  varsay&#305;lan JD<PERSON> kullan&#305;lacakt&#305;r (<tt>JAVA_HOME</tt>'un olmad&#305;&#287;&#305;, ve <tt>java</tt> komutunun 
  <tt>PATH</tt> i&#231;erisinde oldu&#287;u varsay&#305;lmaktad&#305;r.). E&#287;er birden fazla JDK se&#231;ilirse, konfig&#252;rasyon matrisi belirtilen
  t&#252;m JDK'lar&#305; i&#231;erecektir.
  <br>
  E&#287;er yap&#305;lmak istenen testin, farkl&#305; JD<PERSON>'lar &#252;zerinde &#231;al&#305;&#351;t&#305;r&#305;lmas&#305; isten<PERSON><PERSON><PERSON>, birden fazla de&#287;er se&#231;mek kullan&#305;&#351;l&#305; olacakt&#305;r, 
  <br>
  Yap&#305;land&#305;rma esnas&#305;nda, se&#231;ilen JDK de&#287;eri "jdK" ekseni olarak g&#246;r&#252;lecektir.
  Eksen de&#287;erlerine nas&#305;l eri&#351;ilece&#287;ine dair daha fazla bilgi i&#231;in, a&#351;a&#287;&#305;da "eksenler" ile ilgili yard&#305;m k&#305;sm&#305;na bak&#305;n&#305;z.
</div>