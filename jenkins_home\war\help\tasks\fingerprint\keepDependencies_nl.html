<div>
  <p>
    Indien U deze optie activeert, worden
    <a href="lastSuccessfulBuild/fingerprint">
      alle bouwpogingen van dit project, die (via de numerische vingerafdrukken)
      gerefereerd worden,
    </a>
    bijgehouden.
  </p>
  <p>
    Wanneer u af en toe uw werkplaats wenst te labelen en uw job hangt af van
    andere jobs op Jenkins, dan is het nuttig/nodig om uw afhankelijkheden
    evenzeer te labelen. Het probleem is dat de log opschoning hiermee kan
    interfereren, aangezien het mogelijk is dat de bouwpoging waar je op steunt
    ondertussen al opgeschoond is. In een dergelijke situatie is het niet langer
    mogelijk om consistent te labelen.
  </p>
  <p>
    Deze functionaliteit lost dit probleem op door de bouwpogingen waar u op
    steunt vast te leggen. Hierdoor kan een consistente situatie gegarandeerd
    worden, wat correct labelen mogelijk maakt.
  </p>
</div>
