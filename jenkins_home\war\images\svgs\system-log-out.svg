<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:export-ydpi="90.000000"
   inkscape:export-xdpi="90.000000"
   inkscape:export-filename="/home/<USER>/Desktop/wi-fi.png"
   width="48px"
   height="48px"
   id="svg11300"
   sodipodi:version="0.32"
   inkscape:version="0.43+devel"
   sodipodi:docbase="/home/<USER>/src/cvs/tango-icon-theme/scalable/actions"
   sodipodi:docname="system-log-out.svg">
  <defs
     id="defs3">
    <linearGradient
       id="linearGradient9896">
      <stop
         id="stop9898"
         offset="0"
         style="stop-color:#cecece;stop-opacity:1;" />
      <stop
         id="stop9900"
         offset="1.0000000"
         style="stop-color:#9e9e9e;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient9888"
       inkscape:collect="always">
      <stop
         id="stop9890"
         offset="0"
         style="stop-color:#ffffff;stop-opacity:1;" />
      <stop
         id="stop9892"
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       id="linearGradient9880"
       inkscape:collect="always">
      <stop
         id="stop9882"
         offset="0"
         style="stop-color:#525252;stop-opacity:1;" />
      <stop
         id="stop9884"
         offset="1"
         style="stop-color:#525252;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       id="linearGradient9868">
      <stop
         style="stop-color:#4e4e4e;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop9870" />
      <stop
         style="stop-color:#616161;stop-opacity:0.0000000;"
         offset="1.0000000"
         id="stop9872" />
    </linearGradient>
    <linearGradient
       id="linearGradient9854">
      <stop
         id="stop9856"
         offset="0.0000000"
         style="stop-color:#4e4e4e;stop-opacity:1.0000000;" />
      <stop
         id="stop9858"
         offset="1.0000000"
         style="stop-color:#ababab;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient9842"
       inkscape:collect="always">
      <stop
         id="stop9844"
         offset="0"
         style="stop-color:#727e0a;stop-opacity:1;" />
      <stop
         id="stop9846"
         offset="1"
         style="stop-color:#727e0a;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       id="linearGradient9830">
      <stop
         id="stop9832"
         offset="0.0000000"
         style="stop-color:#505050;stop-opacity:1.0000000;" />
      <stop
         id="stop9834"
         offset="1.0000000"
         style="stop-color:#181818;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient8662">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop8664" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop8666" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient8650">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop8652" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop8654" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient8650"
       id="radialGradient8656"
       cx="19.701141"
       cy="2.8969381"
       fx="19.701141"
       fy="2.8969381"
       r="17.171415"
       gradientTransform="matrix(1.253442,-2.296195e-16,1.747460e-16,0.953900,-15.47908,11.27663)"
       gradientUnits="userSpaceOnUse" />
    <radialGradient
       r="15.644737"
       fy="36.421127"
       fx="24.837126"
       cy="36.421127"
       cx="24.837126"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.536723,1.673575e-15,16.87306)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient9826"
       xlink:href="#linearGradient8662"
       inkscape:collect="always" />
    <radialGradient
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(2.017991,4.461116e-16,-5.888254e-16,2.64016,-144.5498,-62.03367)"
       r="16.321514"
       fy="40.545052"
       fx="93.780037"
       cy="40.545052"
       cx="93.780037"
       id="radialGradient9836"
       xlink:href="#linearGradient9830"
       inkscape:collect="always" />
    <linearGradient
       gradientTransform="matrix(1.025512,0,0,0.648342,-0.865496,15.63026)"
       gradientUnits="userSpaceOnUse"
       y2="28.112619"
       x2="30.935921"
       y1="43.757359"
       x1="30.935921"
       id="linearGradient9848"
       xlink:href="#linearGradient9842"
       inkscape:collect="always" />
    <linearGradient
       gradientUnits="userSpaceOnUse"
       y2="27.759069"
       x2="18.031221"
       y1="19.804117"
       x1="46.845825"
       id="linearGradient9864"
       xlink:href="#linearGradient9854"
       inkscape:collect="always" />
    <radialGradient
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(2.565823,5.084556e-18,-2.416266e-14,1.403262,-37.78323,-9.483303)"
       r="9.7227182"
       fy="7.1396070"
       fx="27.883883"
       cy="7.1396070"
       cx="27.883883"
       id="radialGradient9876"
       xlink:href="#linearGradient9868"
       inkscape:collect="always" />
    <linearGradient
       gradientTransform="translate(-1.116120,0.000000)"
       gradientUnits="userSpaceOnUse"
       y2="24.764584"
       x2="34.007416"
       y1="19.107729"
       x1="31.852951"
       id="linearGradient9886"
       xlink:href="#linearGradient9880"
       inkscape:collect="always" />
    <linearGradient
       gradientUnits="userSpaceOnUse"
       y2="43.449947"
       x2="19.755548"
       y1="13.663074"
       x1="8.7600641"
       id="linearGradient9894"
       xlink:href="#linearGradient9888"
       inkscape:collect="always" />
    <linearGradient
       gradientUnits="userSpaceOnUse"
       y2="18.064039"
       x2="33.710651"
       y1="21.511185"
       x1="31.078955"
       id="linearGradient9902"
       xlink:href="#linearGradient9896"
       inkscape:collect="always" />
  </defs>
  <sodipodi:namedview
     stroke="#a40000"
     fill="#727e0a"
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="0.25490196"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="1"
     inkscape:cx="20.295477"
     inkscape:cy="1.9717815"
     inkscape:current-layer="layer1"
     showgrid="true"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:showpageshadow="false"
     inkscape:window-width="1039"
     inkscape:window-height="938"
     inkscape:window-x="485"
     inkscape:window-y="137" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://jimmac.musichall.cz</dc:source>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:title>System Log Out</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>log out</rdf:li>
            <rdf:li>logout</rdf:li>
            <rdf:li>exit</rdf:li>
          </rdf:Bag>
        </dc:subject>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <rect
       ry="0.7071048"
       rx="0.70710522"
       y="2.5692098"
       x="13.501722"
       height="41.942028"
       width="31.99555"
       id="rect9828"
       style="opacity:1;color:#000000;fill:url(#radialGradient9836);fill-opacity:1;fill-rule:evenodd;stroke:#000000;stroke-width:1.00000048;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       inkscape:r_cx="true"
       inkscape:r_cy="true" />
    <rect
       y="31.736542"
       x="14"
       height="12.263458"
       width="30.999998"
       id="rect9840"
       style="opacity:1;color:#000000;fill:url(#linearGradient9848);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       inkscape:r_cx="true"
       inkscape:r_cy="true" />
    <path
       sodipodi:nodetypes="ccccc"
       id="path9852"
       d="M 14.037662,43.944859 L 13.998829,3.0545252 L 33.941125,3.0987194 L 33.985319,33.018175 L 14.037662,43.944859 z "
       style="overflow:visible;display:inline;visibility:visible;stroke-opacity:1.0000000;stroke-dashoffset:0.0000000;stroke-dasharray:none;stroke-miterlimit:10.000000;marker-end:none;marker-mid:none;marker-start:none;marker:none;stroke-linejoin:miter;stroke-linecap:butt;stroke-width:1.0000000;stroke:none;fill-rule:evenodd;fill-opacity:1.0;fill:url(#linearGradient9864);color:#000000;opacity:1.0000000" />
    <path
       style="opacity:0.42222222;color:#000000;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:block;overflow:visible"
       d="M 13.969801,43.944859 L 34.117903,33.062369 L 15.556349,41.989592 L 15.556349,3.0103306 L 13.985886,3.0103306 L 13.969801,43.944859 z "
       id="path1360"
       inkscape:r_cx="true"
       inkscape:r_cy="true"
       sodipodi:nodetypes="cccccc" />
    <path
       sodipodi:type="arc"
       style="opacity:0.29946521;color:#000000;fill:url(#radialGradient9826);fill-opacity:1.0000000;fill-rule:evenodd;stroke:none;stroke-width:1.0000000;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible"
       id="path8660"
       sodipodi:cx="24.837126"
       sodipodi:cy="36.421127"
       sodipodi:rx="15.644737"
       sodipodi:ry="8.3968935"
       d="M 40.481863 36.421127 A 15.644737 8.3968935 0 1 1  9.1923885,36.421127 A 15.644737 8.3968935 0 1 1  40.481863 36.421127 z"
       transform="matrix(0.778490,0.000000,0.000000,0.778490,-7.579815,1.598139)" />
    <path
       style="opacity:1.0000000;color:#000000;fill:#cc0000;fill-opacity:1.0000000;fill-rule:evenodd;stroke:#a40000;stroke-width:0.99999982;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible"
       d="M 1.7317981,17.593819 L 1.7317981,30.355364 L 9.6641034,30.355364 L 9.6641034,36.176147 L 21.887745,23.952503 L 9.5913424,11.656101 L 9.5913424,17.597067 L 1.7317981,17.593819 z "
       id="path8643"
       sodipodi:nodetypes="cccccccc" />
    <path
       style="opacity:0.50802141;color:#000000;fill:url(#radialGradient8656);fill-opacity:1.0000000;fill-rule:evenodd;stroke:none;stroke-width:1.0000000;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible"
       d="M 9.9240084,12.478043 L 9.9240084,17.865661 L 2.0746151,17.865661 L 2.0746151,24.531440 C 12.332521,20.703863 11.954992,27.773987 21.294280,23.946410 L 9.9240084,12.478043 z "
       id="path8645"
       sodipodi:nodetypes="cccccc" />
    <path
       sodipodi:nodetypes="cccccccc"
       id="path8658"
       d="M 2.7193259,18.399985 L 2.7193259,29.536029 L 10.553144,29.536029 L 10.553144,33.793979 L 20.404597,23.948406 L 10.488577,13.684714 L 10.488577,18.402867 L 2.7193259,18.399985 z "
       style="overflow:visible;display:inline;visibility:visible;stroke-opacity:1.0000000;stroke-dashoffset:0.0000000;stroke-dasharray:none;stroke-miterlimit:10.000000;marker-end:none;marker-mid:none;marker-start:none;marker:none;stroke-linejoin:miter;stroke-linecap:butt;stroke-width:1.0000000;stroke:url(#linearGradient9894);fill-rule:evenodd;fill-opacity:1.0000000;fill:none;color:#000000;opacity:0.48128340" />
    <path
       style="opacity:1;color:#000000;fill:url(#radialGradient9876);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       d="M 14.044811,43.757359 L 14,3.0545252 L 33.941125,3.0545252 L 33.761879,33.681088 L 14.044811,43.757359 z "
       id="path9866"
       sodipodi:nodetypes="ccccc"
       inkscape:r_cx="true"
       inkscape:r_cy="true" />
    <path
       sodipodi:nodetypes="cccsscc"
       id="path9878"
       d="M 29.643025,18.456195 L 31.565472,20.908971 L 30.107064,25.726136 C 30.107064,25.726136 30.372229,27.228738 31.145627,26.212272 C 31.919025,25.195806 34.118082,22.630218 33.730986,20.754291 C 33.443724,19.362175 32.648229,18.699263 32.648229,18.699263 L 29.643025,18.456195 z "
       style="opacity:1.0000000;color:#000000;fill:url(#linearGradient9886);fill-opacity:1.0000000;fill-rule:evenodd;stroke:none;stroke-width:1.0000000;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible" />
    <path
       sodipodi:nodetypes="csccscs"
       id="path9862"
       d="M 31.477084,17.351340 C 31.477084,17.351340 33.640354,18.353058 33.708889,19.229593 C 33.810670,20.531315 29.466248,24.665476 29.466248,24.665476 C 28.958015,25.284194 28.118326,24.731767 28.582365,24.135146 C 28.582365,24.135146 32.048969,20.017173 31.830637,19.693631 C 31.557026,19.288174 29.863996,18.655068 29.863996,18.655068 C 28.847530,17.903768 30.131617,16.349605 31.477084,17.351340 z "
       style="overflow:visible;display:inline;visibility:visible;stroke-opacity:1.0000000;stroke-dashoffset:0.0000000;stroke-dasharray:none;stroke-miterlimit:10.000000;marker-end:none;marker-mid:none;marker-start:none;marker:none;stroke-linejoin:miter;stroke-linecap:butt;stroke-width:1.0000000;stroke:none;fill-rule:evenodd;fill-opacity:1.0;fill:url(#linearGradient9902);color:#000000;opacity:1.0000000" />
  </g>
</svg>
