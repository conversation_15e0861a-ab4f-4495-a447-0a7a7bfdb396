#!groovy

import jenkins.model.*
import hudson.security.*
import hudson.security.csrf.DefaultCrumbIssuer
import hudson.markup.RawHtmlMarkupFormatter
import hudson.model.*
import jenkins.security.s2m.AdminWhitelistRule
import hudson.slaves.*
import hudson.slaves.EnvironmentVariablesNodeProperty.Entry

def instance = Jenkins.getInstance()

// Create admin user
def hudsonRealm = new HudsonPrivateSecurityRealm(false)
hudsonRealm.createAccount("admin", "admin123")
instance.setSecurityRealm(hudsonRealm)

// Set authorization strategy
def strategy = new FullControlOnceLoggedInAuthorizationStrategy()
strategy.setAllowAnonymousRead(false)
instance.setAuthorizationStrategy(strategy)

// Enable CSRF protection
instance.setCrumbIssuer(new DefaultCrumbIssuer(true))

// Set markup formatter
instance.setMarkupFormatter(new RawHtmlMarkupFormatter(false))

// Configure agent protocols
instance.getDescriptor("jenkins.CLI").get().setEnabled(false)
instance.getDescriptor("jenkins.security.s2m.MasterKillSwitchConfiguration").setMasterKillSwitch(false)

// Configure built-in node to ensure it's properly set as Linux
def node = instance.getNode("")
if (node != null) {
    // Set node properties to explicitly indicate this is a Linux node
    def envVars = new EnvironmentVariablesNodeProperty([
        new Entry("SHELL", "/bin/bash"),
        new Entry("OS", "linux")
    ])

    def nodeProperties = node.getNodeProperties()
    nodeProperties.clear()
    nodeProperties.add(envVars)

    println "Built-in node configured as Linux"
}

// Save configuration
instance.save()

println "Jenkins initial configuration completed!"
println "Admin user created with username: admin, password: admin123"
println "Built-in node configured for Linux/Unix shell commands"
println "Please change the default password after first login!"
