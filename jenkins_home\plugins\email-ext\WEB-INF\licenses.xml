<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='email-ext' version='1876.v28d8d38315b_d'><l:dependency name='Email Extension Plugin' groupId='org.jenkins-ci.plugins' artifactId='email-ext' version='1876.v28d8d38315b_d' url='https://github.com/jenkinsci/email-ext-plugin'><l:description>Allows to configure every aspect of email notifications: when an email is sent, who should receive it and what the email says</l:description><l:license name='The MIT License (MIT)' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Jericho HTML Parser' groupId='net.htmlparser.jericho' artifactId='jericho-html' version='3.4' url='http://jericho.htmlparser.net'><l:description>Jericho HTML Parser is a java library allowing analysis and manipulation of parts of an HTML document, including server-side tags, while reproducing verbatim any unrecognised or invalid HTML.</l:description><l:license name='GNU Lesser General Public License (LGPL)' url='http://www.gnu.org/licenses/lgpl.txt'/><l:license name='Apache License' url='http://www.apache.org/licenses/'/><l:license name='Eclipse Public License (EPL)' url='http://www.eclipse.org/legal/epl-v10.html'/></l:dependency><l:dependency name='jsoup Java HTML Parser' groupId='org.jsoup' artifactId='jsoup' version='1.18.3' url='https://jsoup.org/'><l:description>jsoup is a Java library that simplifies working with real-world HTML and XML. It offers an easy-to-use API for URL fetching, data parsing, extraction, and manipulation using DOM API methods, CSS, and xpath selectors. jsoup implements the WHATWG HTML5 specification, and parses HTML to the same DOM as modern browsers.</l:description><l:license name='The MIT License' url='https://jsoup.org/license'/></l:dependency><l:dependency name='commons-text API Plugin' groupId='io.jenkins.plugins' artifactId='commons-text-api' version='1.13.0-153.v91dcd89e2a_22' url='https://github.com/jenkinsci/commons-text-api-plugin'><l:description>Jenkins Api Plugin to provide org.apache.commons:commons-text:1.13.0.</l:description><l:license name='Apache-2.0' url='https://opensource.org/licenses/Apache-2.0'/></l:dependency><l:dependency name='Apache Commons Text' groupId='org.apache.commons' artifactId='commons-text' version='1.13.0' url='https://commons.apache.org/proper/commons-text'><l:description>Apache Commons Text is a set of utility functions and reusable components for the purpose of processing
    and manipulating text that should be of use in a Java environment.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Byte Buddy (without dependencies)' groupId='net.bytebuddy' artifactId='byte-buddy' version='1.15.11' url='https://bytebuddy.net/byte-buddy'><l:description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>