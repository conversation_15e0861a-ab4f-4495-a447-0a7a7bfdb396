<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='junit' version='1335.v6b_a_a_e18534e1'><l:dependency name='JUnit Plugin' groupId='org.jenkins-ci.plugins' artifactId='junit' version='1335.v6b_a_a_e18534e1' url='https://github.com/jenkinsci/junit-plugin'><l:description>Allows JUnit-format test results to be published.</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='parallel-collectors' groupId='com.pivovarit' artifactId='parallel-collectors' version='2.6.1' url='http://nexus.sonatype.org/oss-repository-hosting.html/parallel-collectors'><l:description>A set of utilities for parallel collection processing in Java</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='JBoss Marshalling River' groupId='org.jboss.marshalling' artifactId='jboss-marshalling-river' version='2.2.2.Final' url='http://www.jboss.org'><l:description>JBoss Marshalling River Implementation</l:description><l:license name='Apache License 2.0' url='http://repository.jboss.org/licenses/apache-2.0.txt'/></l:dependency><l:dependency name='JBoss Marshalling API' groupId='org.jboss.marshalling' artifactId='jboss-marshalling' version='2.2.2.Final' url='http://www.jboss.org'><l:description>JBoss Marshalling API</l:description><l:license name='Apache License 2.0' url='http://repository.jboss.org/licenses/apache-2.0.txt'/></l:dependency></l:dependencies>