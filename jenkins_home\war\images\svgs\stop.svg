<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:export-ydpi="90.000000"
   inkscape:export-xdpi="90.000000"
   inkscape:export-filename="/home/<USER>/Desktop/wi-fi.png"
   width="48px"
   height="48px"
   id="svg11300"
   sodipodi:version="0.32"
   inkscape:version="0.43"
   sodipodi:docbase="C:\Documents and Settings\kohsuke\Desktop"
   sodipodi:docname="emblem-unreadable.svg">
  <defs
     id="defs3">
    <linearGradient
       id="linearGradient2719">
      <stop
         style="stop-color:#cc0000;stop-opacity:1;"
         offset="0"
         id="stop2721" />
      <stop
         style="stop-color:#cc0000;stop-opacity:0;"
         offset="1"
         id="stop2723" />
    </linearGradient>
    <linearGradient
       id="linearGradient2711">
      <stop
         style="stop-color:#f34e4e;stop-opacity:1;"
         offset="0"
         id="stop2713" />
      <stop
         style="stop-color:#ef2929;stop-opacity:0;"
         offset="1"
         id="stop2715" />
    </linearGradient>
    <linearGradient
       id="linearGradient11327">
      <stop
         style="stop-color:#7d6400;stop-opacity:1;"
         offset="0"
         id="stop11329" />
      <stop
         style="stop-color:#be9700;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop11331" />
    </linearGradient>
    <linearGradient
       id="linearGradient2092">
      <stop
         id="stop2094"
         offset="0"
         style="stop-color:#fff7b0;stop-opacity:1;" />
      <stop
         style="stop-color:#ffec41;stop-opacity:1.0000000;"
         offset="0.20999999"
         id="stop2098" />
      <stop
         id="stop2293"
         offset="0.83999997"
         style="stop-color:#e2cc00;stop-opacity:1;" />
      <stop
         id="stop2100"
         offset="1"
         style="stop-color:#c3af00;stop-opacity:1;" />
    </linearGradient>
    <linearGradient
       id="linearGradient11335">
      <stop
         style="stop-color:#6f716d;stop-opacity:1;"
         offset="0"
         id="stop11337" />
      <stop
         style="stop-color:#9ea09c;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop11339" />
    </linearGradient>
    <linearGradient
       id="linearGradient10591">
      <stop
         style="stop-color:#cad0c6;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop10593" />
      <stop
         id="stop10599"
         offset="0.50000000"
         style="stop-color:#eaece9;stop-opacity:1.0000000;" />
      <stop
         style="stop-color:#c5cbc0;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop10595" />
    </linearGradient>
    <linearGradient
       id="linearGradient11520">
      <stop
         id="stop11522"
         offset="0.0000000"
         style="stop-color:#ffffff;stop-opacity:1.0000000;" />
      <stop
         id="stop11524"
         offset="1.0000000"
         style="stop-color:#dcdcdc;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient11508"
       inkscape:collect="always">
      <stop
         id="stop11510"
         offset="0"
         style="stop-color:#000000;stop-opacity:1;" />
      <stop
         id="stop11512"
         offset="1"
         style="stop-color:#000000;stop-opacity:0;" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient11508"
       id="radialGradient1348"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1,0,0,0.338462,-3.746128e-15,29.48178)"
       cx="30.203562"
       cy="44.565483"
       fx="30.203562"
       fy="44.565483"
       r="6.5659914" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient11520"
       id="radialGradient1366"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.995058,-1.651527e-32,0.000000,1.995058,-24.32488,-35.70087)"
       cx="24.445690"
       cy="35.878170"
       fx="24.445690"
       fy="35.878170"
       r="20.530962" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2719"
       id="radialGradient2725"
       cx="24.445681"
       cy="46.957848"
       fx="24.445681"
       fy="46.957848"
       r="16.554319"
       gradientTransform="matrix(2.670835,-1.66183e-17,1.650931e-17,2.653312,-40.84471,-77.78951)"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     stroke="#eeeeec"
     fill="#cc0000"
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="0.25490196"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="7.4642639"
     inkscape:cx="16.584846"
     inkscape:cy="28.954973"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:showpageshadow="false"
     inkscape:window-width="1002"
     inkscape:window-height="567"
     inkscape:window-x="191"
     inkscape:window-y="164" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://jimmac.musichall.cz</dc:source>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:title>Unreadable</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>emblem</rdf:li>
            <rdf:li>access</rdf:li>
            <rdf:li>denied</rdf:li>
            <rdf:li>unreadable</rdf:li>
          </rdf:Bag>
        </dc:subject>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <rect
       style="opacity:1;color:#000000;fill:url(#radialGradient2725);fill-opacity:1.0;fill-rule:evenodd;stroke:#cc0000;stroke-width:0.99999946;stroke-linecap:butt;stroke-linejoin:bevel;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       id="rect2707"
       width="33.108639"
       height="32.891403"
       x="7.8913612"
       y="7.1085968"
       rx="2.7440348"
       ry="2.7440348"
       inkscape:r_cx="true"
       inkscape:r_cy="true" />
    <g
       id="g2703"
       transform="matrix(0.717647,0,0,0.717647,6.882353,6.6)"
       inkscape:r_cx="true"
       inkscape:r_cy="true"
       style="stroke:#eeeeec">
      <path
         inkscape:r_cy="true"
         inkscape:r_cx="true"
         id="path2698"
         d="M 16.25,15.25 L 32.5,31.5"
         style="opacity:1;color:#000000;fill:#204a87;fill-opacity:1;fill-rule:nonzero;stroke:#eeeeec;stroke-width:6.96721315;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:block;overflow:visible" />
      <path
         style="opacity:1;color:#000000;fill:#204a87;fill-opacity:1;fill-rule:nonzero;stroke:#eeeeec;stroke-width:6.96721315;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:block;overflow:visible"
         d="M 32.5,15.25 L 16.25,31.5"
         id="path2700"
         inkscape:r_cx="true"
         inkscape:r_cy="true" />
    </g>
  </g>
</svg>
