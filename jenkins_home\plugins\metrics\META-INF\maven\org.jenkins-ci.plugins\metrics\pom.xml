<?xml version="1.0" encoding="UTF-8"?>
<!--
 ~ The MIT License
 ~
 ~ Copyright (c) 2014, CloudBees, Inc.
 ~
 ~ Permission is hereby granted, free of charge, to any person obtaining a copy
 ~ of this software and associated documentation files (the "Software"), to deal
 ~ in the Software without restriction, including without limitation the rights
 ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 ~ copies of the Software, and to permit persons to whom the Software is
 ~ furnished to do so, subject to the following conditions:
 ~
 ~ The above copyright notice and this permission notice shall be included in
 ~ all copies or substantial portions of the Software.
 ~
 ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 ~ THE SOFTWARE.
 -->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>metrics</artifactId>
  <version>4.2.32-476.v5042e1c1edd7</version>
  <packaging>hpi</packaging>
  <name>Metrics Plugin</name>
  <description>The Jenkins Plugins Parent POM Project</description>
  <url>https://github.com/jenkinsci/metrics-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>The MIT license</name>
      <url>https://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>stephenconnolly</id>
      <name>Stephen Connolly</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/metrics-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/metrics-plugin.git</developerConnection>
    <tag>5042e1c1edd7d2c4bc34d2f1a32d3f550ce22ec6</tag>
    <url>https://github.com/jenkinsci/metrics-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-core</artifactId>
      <version>4.2.32</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-servlet</artifactId>
      <version>4.2.32</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-healthchecks</artifactId>
      <version>4.2.32</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-jvm</artifactId>
      <version>4.2.32</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-jmx</artifactId>
      <version>4.2.32</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-json</artifactId>
      <version>4.2.32</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>ionicons-api</artifactId>
      <version>88.va_4187cb_eddf1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>jackson2-api</artifactId>
      <version>2.18.3-402.v74c4eb_f122b_2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>variant</artifactId>
      <version>70.va_d9f17f859e0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.main</groupId>
      <artifactId>jenkins-core</artifactId>
      <version>2.479.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.4</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.65</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
