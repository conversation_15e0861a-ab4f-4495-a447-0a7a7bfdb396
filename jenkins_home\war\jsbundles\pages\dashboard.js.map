{"version": 3, "file": "pages/dashboard.js", "mappings": ";;;;AAAA,SAASA,OAAOA,CAACC,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACjDC,SAAS,CAACL,OAAO,CAACC,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;AACrD;AAEA,SAASE,YAAYA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC5CH,SAAS,CAACC,YAAY,CAACC,SAAS,EAAEC,WAAW,CAAC;AAChD;AAEA,kDAAe;EAAER,OAAO;EAAEM;AAAa,CAAC;;ACRjC,SAASG,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EACnDF,QAAQ,CAACG,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAAC;EAChC,OAAOJ,QAAQ,CAACK,OAAO,CAACC,iBAAiB;AAC3C;AAEO,SAASC,IAAIA,CAACC,MAAM,EAAE;EAC3B,OAAOA,MAAM,CACVJ,IAAI,CAAC,CAAC,CACNK,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBC,WAAW,CAAC,CAAC;AAClB;;ACXgD;AACG;AAEnDC,aAAY,CAACtB,OAAO,CAAC,qBAAqB,EAAE,aAAa,EAAE,GAAG,EAAGuB,MAAM,IAAK;EAC1EA,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE,MAAM;IACrC,MAAMb,QAAQ,GAAGC,QAAQ,CAACa,aAAa,CAAC,uBAAuB,CAAC;IAChE,MAAMC,KAAK,GAAGf,QAAQ,CAACgB,YAAY,CAAC,YAAY,CAAC;IACjD,MAAMX,OAAO,GAAGP,qBAAqB,CACnC,OAAO,GAAGE,QAAQ,CAACG,SAAS,GAAG,QACjC,CAAC;IAEDc,MAAM,CAACC,KAAK,CAACb,OAAO,EAAE;MACpBc,QAAQ,EAAE,OAAO;MACjBJ,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC,C", "sources": ["webpack://jenkins-ui/./src/main/js/util/behavior-shim.js", "webpack://jenkins-ui/./src/main/js/util/dom.js", "webpack://jenkins-ui/./src/main/js/pages/dashboard/index.js"], "sourcesContent": ["function specify(selector, id, priority, behavior) {\n  Behaviour.specify(selector, id, priority, behavior);\n}\n\nfunction applySubtree(startNode, includeSelf) {\n  Behaviour.applySubtree(startNode, includeSelf);\n}\n\nexport default { specify, applySubtree };\n", "export function createElementFromHtml(html) {\n  const template = document.createElement(\"template\");\n  template.innerHTML = html.trim();\n  return template.content.firstElementChild;\n}\n\nexport function toId(string) {\n  return string\n    .trim()\n    .replace(/[\\W_]+/g, \"-\")\n    .toLowerCase();\n}\n", "import behaviorShim from \"@/util/behavior-shim\";\nimport { createElementFromHtml } from \"@/util/dom\";\n\nbehaviorShim.specify(\"#button-icon-legend\", \"icon-legend\", 999, (button) => {\n  button.addEventListener(\"click\", () => {\n    const template = document.querySelector(\"#template-icon-legend\");\n    const title = template.getAttribute(\"data-title\");\n    const content = createElementFromHtml(\n      \"<div>\" + template.innerHTML + \"</div>\",\n    );\n\n    dialog.modal(content, {\n      maxWidth: \"550px\",\n      title: title,\n    });\n  });\n});\n"], "names": ["specify", "selector", "id", "priority", "behavior", "Behaviour", "applySubtree", "startNode", "includeSelf", "createElementFromHtml", "html", "template", "document", "createElement", "innerHTML", "trim", "content", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "toId", "string", "replace", "toLowerCase", "behaviorShim", "button", "addEventListener", "querySelector", "title", "getAttribute", "dialog", "modal", "max<PERSON><PERSON><PERSON>"], "sourceRoot": ""}