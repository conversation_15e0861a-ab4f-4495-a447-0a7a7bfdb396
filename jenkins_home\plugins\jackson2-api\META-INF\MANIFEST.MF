Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Jackson 2 API Plugin
Specification-Version: 2.18
Implementation-Title: Jackson 2 API Plugin
Implementation-Version: 2.18.3-402.v74c4eb_f122b_2
Group-Id: org.jenkins-ci.plugins
Artifact-Id: jackson2-api
Short-Name: jackson2-api
Long-Name: Jackson 2 API Plugin
Url: https://github.com/jenkinsci/jackson2-api-plugin
Plugin-Version: 2.18.3-402.v74c4eb_f122b_2
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: javax-activation-api:1.2.0-7,jaxb:2.3.9-1,json-api:
 20250107-125.v28b_a_ffa_eb_f01,snakeyaml-api:2.3-123.v13484c65210a_
Plugin-Developers: <PERSON>:stephenconnolly:,<PERSON><PERSON>v:oleg_
 nenashev:,<PERSON>:jvz:
Support-Dynamic-Loading: true
Plugin-License-Name: The Apache Software License, Version 2.0
Plugin-License-Url: https://www.apache.org/licenses/LICENSE-2.0.txt
Plugin-License-Name-2: BSD 3-Clause ASM
Plugin-License-Url-2: https://gitlab.ow2.org/asm/asm/raw/ASM_9_7_1/LICEN
 SE.txt
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/jackson2-api-
 plugin.git
Plugin-ScmTag: 74c4ebf122b2d946da558923f79e60dd238149d4
Plugin-ScmUrl: https://github.com/jenkinsci/jackson2-api-plugin
Implementation-Build: 74c4ebf122b2d946da558923f79e60dd238149d4

