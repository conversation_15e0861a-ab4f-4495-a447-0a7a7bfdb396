<div>
  <!-- TODO -->
  このオプションを有効にすると、このプロジェクトのビルドが(指紋経由で)依存している
  <a href="lastSuccessfulBuild/fingerprint">すべてのビルド</a>
  が、 ログローテーションで削除されないようにします。

  <p>
    When your job depends on other jobs on <PERSON> and you occasionally need to
    tag your workspace, it's often convenient/necessary to also tag your
    dependencies on <PERSON>. The problem is that the log rotation could
    interfere with this, since the build your project is using might already be
    log rotated (if there have been a lot of builds in your dependency), and if
    that happens you won't be able to reliably tag your dependencies.
  </p>

  <p>
    This feature fixes that problem by "locking" those builds that you depend
    on, thereby guaranteeing that you can always tag your complete dependencies.
  </p>
</div>
