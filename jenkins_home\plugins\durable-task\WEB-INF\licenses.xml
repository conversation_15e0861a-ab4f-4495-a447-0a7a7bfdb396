<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='durable-task' version='587.v84b_877235b_45'><l:dependency name='Durable Task Plugin' groupId='org.jenkins-ci.plugins' artifactId='durable-task' version='587.v84b_877235b_45' url='https://github.com/jenkinsci/durable-task-plugin'><l:description>Library offering an extension point for processes which can run outside of Jenkins yet be monitored.</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Durable Task Library' groupId='io.jenkins.plugins' artifactId='lib-durable-task' version='48.v72b_86b_9ca_8e1' url='https://github.com/jenkinsci/lib-durable-task'><l:description>Generates and packages the binaries used by Durable Task Plugin</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>