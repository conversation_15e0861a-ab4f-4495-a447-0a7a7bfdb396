<div>
  ビルドのスケジューリング方法を選択します。

  <dl>
    <dt><b>このエージェントをできるだけ利用する</b></dt>
    <dd>
      これはデフォルトで通常の設定です。
      このモードでは、Jenkinsはこのエージェントを自由に利用します。このエージェントを利用して実行できるビルドがあればいつでも、
      Jenkinsはこのエージェントを利用します。
    </dd>

    <dt><b>このマシーンを特定ジョブ専用にする</b></dt>
    <dd>
      このモードでは、プロジェクトが"このビルドは指定のノード上でのみ実行"としてこのエージェントを指定している場合、
      このマシーンで1つだけプロジェクトをビルドします。
      エージェントをある種のジョブ専用にすることができます。
      例えば、Jenkinsで継続的に性能テストを動かすために、同時実行数を1として設定できます。
      そうすると、いつでも性能テストが1つだけ動き、他のエージェントでも実行できる他のビルドに実行を妨げられることはありません。
    </dd>
  </dl>
</div>
