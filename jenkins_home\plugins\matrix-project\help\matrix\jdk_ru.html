﻿<div>
  Выберите JDK с которым(и) будет выполняться сборка. Если ни одного не выбрано,
  будет использован JDK по-умолчанию (без явного <tt>JAVA_HOME</tt>, и команды <tt>java</tt> 
  не будет указано в <tt>PATH</tt>). Если несколько JDK выбрано,
  конфигурационная матрица будет содержать все выбранные JDK.
  <br>
  Выбор нескольких значений обычно полезен в случаях когда проект тестируется и вам нужно
  запускать тесты на нескольких разных версиях JDK.
  <br>
  Во время сборки выбранное значение JDK доступно как ось "jdk". 
  Смотрите в подсказку к опции "Оси (axes)" ниже для получения более подробной информации о том как
  получить значения по осям.
</div>