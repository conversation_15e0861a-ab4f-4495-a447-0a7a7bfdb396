<div>
  <PERSON><PERSON> le occasioni in cui Jenkins avvia e ferma quest'agente.

  <dl>
    <dt><b>Mantieni quest'agente in linea il più possibile</b></dt>
    <dd>
      In questa modalità Jenkins manterrà quest'agente in linea il più
      possibile.
      <p>
        Se l'agente va fuori linea, ad esempio per un problema di rete
        temporaneo, Jenkins tenterà periodicamente di riavviarlo.
      </p>
    </dd>

    <dt><b>Poni quest'agente in linea e non in linea a orari specifici</b></dt>
    <dd>
      In questa modalità Jenkins porrà quest'agente in linea agli orari
      specificati e lo farà rimanere in linea per un periodo di tempo
      specificato.
      <p>
        Se l'agente va fuori linea durante il periodo di tempo in cui dovrebbe
        essere in linea, Jenkins tenterà periodicamente di riavviarlo.
      </p>

      <p>
        Dopo che quest'agente è rimasto in linea per il numero di minuti
        specificato nel campo
        <i>Tempo di attività programmato</i>
        , sarà posto non in linea.
        <br />
        Se la casella
        <i><PERSON><PERSON><PERSON> in linea finché vi sono delle compilazioni in esecuzione</i>
        è selezionata e l'agente sta per essere posto fuori linea, Jenkins
        attenderà che eventuali compilazioni in corso siano state completate.
      </p>
    </dd>

    <dt>
      <b>
        Poni quest'agente in linea quando c'è richiesta e non in linea se non è
        attivo
      </b>
    </dt>
    <dd>
      In questa modalità Jenkins porrà quest'agente in linea se c'è richiesta,
      ossia se ci sono compilazioni in coda che soddisfano i seguenti criteri:
      <ul>
        <li>
          Sono rimaste in coda per un periodo di tempo pari almeno al
          <i>Ritardo "richiesta"</i>
          specificato
        </li>
        <li>
          Possono essere eseguite da quest'agente (ad esempio perché hanno
          un'espressione etichetta corrispondente)
        </li>
      </ul>

      Quest'agente sarà posto non in linea se:
      <ul>
        <li>Non ci sono compilazioni attive in esecuzione su quest'agente</li>
        <li>
          Quest'agente è rimasto inattivo per un periodo di tempo pari almeno al
          <i>Ritardo "inattivo"</i>
          specificato
        </li>
      </ul>
    </dd>
  </dl>
</div>
