<div>
    Newline-delimited set of rules in the form of +|-:repository_matching_keyword, for which to enable/disable Develocity Gradle plugin/Maven extension auto-injection.
    By default, all Git VCS repositories have auto-injection enabled. Disabled nodes have precedence over this filtering.

    This feature is currently in Beta and supports both Gradle and Maven builds (FreeStyle and Pipeline job configurations).
    Functionality can be changed in future releases.
</div>
