<div>
  Quand un agent est lancé par JNLP, l'agent de l'agent tente de se connecter à
  Jenkins par un port TCP spécifique, afin d'établir un canal de communication.
  Certains réseaux très sécurisés peuvent vous empècher d'effectuer cette
  connexion. Cela peut également être le cas quand Jenkins tourne derrière un
  répartiteur de charge (load balancer), un
  <a
    href="https://www.jenkins.io/doc/book/system-administration/reverse-proxy-configuration-apache/"
  >
    reverse proxy apache
  </a>
  dans une
  <a href="https://en.wikipedia.org/wiki/Demilitarized_zone_(computing)">
    zone démilitarisée (DMZ)
  </a>
  , etc.

  <p>
    Cette option de tunneling vous permet, dans ces situations, de rediriger la
    connexion sur un autre host ou un autre numéro de port. Le champ supporte
    les formats "
    <code>HOST:PORT</code>
    ", "
    <code>:PORT</code>
    " et "
    <code>HOST:</code>
    ". Dans le premier format, l'agent JNLP de l'agent se connectera sur le
    numéro de port TCP et le host spécifiés et supposera que vous avez configuré
    votre réseau de façon à ce que ce port fasse suivre la connexion sur le port
    TCP de l'agent JNLP de Jenkins.
  </p>

  <p>
    Dans les deux derniers formats, le nom de host et le numéro de port par
    défaut (c'est-à-dire le nom de host que Jenkins utilise et le numéro de port
    que Jenkins a ouvert) sont utilisés pour compléter les valeurs manquantes.
    En particulier, le format
    <code>HOST:</code>
    est utile si un reverse proxy HTTP est utilisé et que Jenkins tourne en fait
    sur un autre système.
  </p>
</div>
