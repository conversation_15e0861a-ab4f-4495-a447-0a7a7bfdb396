<div>
    <p>
        Enabling watching allows individual users to add their own triggers to jobs. The list of triggers they can
        add is restricted by the trigger implementer. For example, most script based triggers are not allowed for
        users to use as a watch. Users must have READ access to the job in order to watch it.
    </p>
    <p>
        Triggers are not stored in the job itself, but as a property on the user.
    </p>
</div>
