// Groovy script to reload pipeline job configuration
import jenkins.model.Jenkins
import org.jenkinsci.plugins.workflow.job.WorkflowJob

def jenkins = Jenkins.getInstance()
def jobName = "Pipeline"

// Get the job
def job = jenkins.getItem(jobName)
if (job != null) {
    println "Found job: ${jobName}"
    
    // Force reload the job configuration
    job.doReload()
    
    // Clear any cached pipeline definitions
    if (job.hasProperty('definition')) {
        job.definition = null
    }
    
    // Save the job
    job.save()
    
    println "Job ${jobName} configuration reloaded successfully"
} else {
    println "Job ${jobName} not found"
}

// Force Jenkins to reload all configurations
jenkins.reload()
println "Jenkins configuration reloaded"
