Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Git plugin
Specification-Version: 5.7
Implementation-Title: Git plugin
Implementation-Version: 5.7.0
Group-Id: org.jenkins-ci.plugins
Artifact-Id: git
Short-Name: git
Long-Name: Git plugin
Url: https://github.com/jenkinsci/git-plugin
Plugin-Version: 5.7.0
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: configuration-as-code:1903.v004d55388f30;resolution
 :=optional,workflow-scm-step:427.v4ca_6512e7df1,workflow-step-api:678.v
 3ee58b_469476,credentials-binding:687.v619cb_15e923f,credentials:1405.v
 b_cda_74a_f8974,git-client:6.1.0,mailer:489.vd4b_25144138f,matrix-proje
 ct:822.824.v14451b_c0fd42;resolution:=optional,parameterized-trigger:80
 6.vf6fff3e28c3e;resolution:=optional,promoted-builds:965.vcda_c6a_e0998
 f;resolution:=optional,scm-api:698.v8e3b_c788f0a_6,script-security:1369
 .v9b_98a_4e95b_2d,ssh-credentials:349.vb_8b_6b_9709f5b_,structs:338.v84
 **********,token-macro:400.v35420b_922dcb_;resolution:=optional
Plugin-Developers: 
Plugin-License-Name: MIT License
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/git-plugin
Plugin-ScmTag: git-5.7.0
Plugin-ScmUrl: https://github.com/jenkinsci/git-plugin
Implementation-Build: 184036c37732c4c87eb28a45de96025937dbfe0f

