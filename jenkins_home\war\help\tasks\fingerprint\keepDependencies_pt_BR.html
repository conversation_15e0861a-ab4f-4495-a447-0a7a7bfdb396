<div>
  Se esta op&#231;&#227;o estiver habilitada, todas
  <a href="lastSuccessfulBuild/fingerprint">
    as costru&#231;&#245;es que s&#227;o referenciadas
  </a>
  das constru&#231;&#245;es deste projet (via fingerprint) ser&#227;o protegidas
  da rota&#231;&#227;o de log.

  <p>
    Quando sua tarefa depende de outras tarefas no Jenkins e voc&#234;
    ocasionalmente precisa marcar com uma tag seu workspace, &#233;
    frequentemente conveniente/necess&#225;rio tamb&#233;m marcar com uma tag
    suas depend&#234;ncias no Jenkins. O problema &#233; que a rota&#231;&#227;o
    de log poderia interferir nisto, desde que a constru&#231;&#227;o que seu
    projeto est&#225; usando poderia j&#225; ter seu log rotacionado (se houve
    uma por&#231;&#227;o de constru&#231;&#245;es e sua depend&#234;ncia), e se
    isto acontecer voc&#234; n&#227;o ser&#225; capaz de confiavelmente marcar
    com uma tag suas depend&#234;ncias.
  </p>

  <p>
    Esta caracter&#237;stica corrige este problema "travando" aquelas
    constru&#231;&#245;es que voc&#234; depende, portanto garantindo que
    voc&#234; possa sempre marcar suas depend&#234;ncias completas.
  </p>
</div>
