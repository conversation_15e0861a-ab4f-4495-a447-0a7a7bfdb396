<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="48px"
   height="48px"
   id="svg1306"
   sodipodi:version="0.32"
   inkscape:version="0.43+devel"
   sodipodi:docbase="/home/<USER>/cvs/freedesktop.org/tango-icon-theme/scalable/apps"
   sodipodi:docname="utilities-terminal.svg"
   inkscape:export-filename="/home/<USER>/projekt/bild/tango/terminal4.png"
   inkscape:export-xdpi="240.00000"
   inkscape:export-ydpi="240.00000"
   inkscape:output_extension="org.inkscape.output.svg.inkscape">
  <defs
     id="defs1308">
    <linearGradient
       inkscape:collect="always"
       id="linearGradient6447">
      <stop
         style="stop-color:#777973;stop-opacity:1;"
         offset="0"
         id="stop6449" />
      <stop
         style="stop-color:#777973;stop-opacity:0;"
         offset="1"
         id="stop6451" />
    </linearGradient>
    <linearGradient
       id="linearGradient4254">
      <stop
         style="stop-color:#616161;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop4256" />
      <stop
         style="stop-color:#a0a0a0;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop4258" />
    </linearGradient>
    <linearGradient
       id="linearGradient5176">
      <stop
         id="stop5178"
         offset="0.0000000"
         style="stop-color:#a2a59c;stop-opacity:1.0000000;" />
      <stop
         id="stop5180"
         offset="1.0000000"
         style="stop-color:#535750;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient2667">
      <stop
         id="stop2669"
         offset="0.0000000"
         style="stop-color:#ffffff;stop-opacity:1.0000000;" />
      <stop
         id="stop2671"
         offset="1.0000000"
         style="stop-color:#fcfcff;stop-opacity:0.0000000;" />
    </linearGradient>
    <linearGradient
       gradientUnits="userSpaceOnUse"
       y2="26.729263"
       x2="17.199417"
       y1="1.6537577"
       x1="11.492236"
       gradientTransform="matrix(1.236157,0.000000,0.000000,0.896051,-1.081820,2.830699)"
       id="linearGradient2673"
       xlink:href="#linearGradient2667"
       inkscape:collect="always" />
    <linearGradient
       inkscape:collect="always"
       id="linearGradient2238">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop2240" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop2242" />
    </linearGradient>
    <linearGradient
       id="linearGradient2224">
      <stop
         style="stop-color:#32342f;stop-opacity:0.54639173;"
         offset="0.0000000"
         id="stop2226" />
      <stop
         style="stop-color:#32342f;stop-opacity:0;"
         offset="1"
         id="stop2228" />
    </linearGradient>
    <linearGradient
       id="linearGradient2214">
      <stop
         style="stop-color:#a9aaa7;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop2216" />
      <stop
         style="stop-color:#676964;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop2218" />
    </linearGradient>
    <linearGradient
       id="linearGradient2206">
      <stop
         style="stop-color:#777973;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop2208" />
      <stop
         style="stop-color:#cbccca;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop2210" />
    </linearGradient>
    <linearGradient
       id="linearGradient2198">
      <stop
         style="stop-color:#748f48;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop2200" />
      <stop
         style="stop-color:#1f2816;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop2202" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2198"
       id="linearGradient2204"
       x1="23.118565"
       y1="9.5830288"
       x2="22.440805"
       y2="34.225887"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.950085,0.000000,0.000000,0.965659,1.243978,0.255342)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2206"
       id="linearGradient2212"
       x1="29.870447"
       y1="32.285740"
       x2="24.841814"
       y2="14.157946"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.957412,0.000000,0.000000,0.952331,1.022766,0.133307)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5176"
       id="linearGradient2220"
       x1="8.6529236"
       y1="9.5865316"
       x2="21.305075"
       y2="32.497993"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.957412,0.000000,0.000000,0.952331,1.022766,0.133307)" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2224"
       id="radialGradient2230"
       cx="24.041630"
       cy="42.242130"
       fx="24.041630"
       fy="42.242130"
       r="17.576654"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.304598,-1.841788e-16,29.37527)"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2238"
       id="linearGradient2244"
       x1="20.338758"
       y1="19.636894"
       x2="48.845253"
       y2="49.730762"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.953506,0.000000,0.000000,0.947873,1.141528,1.205591)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4254"
       id="linearGradient4260"
       x1="11.048059"
       y1="9.1463490"
       x2="26.178129"
       y2="30.343304"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.997583,0.000000,0.000000,0.989941,0.104141,7.028871e-2)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2214"
       id="linearGradient5719"
       x1="40.253334"
       y1="42.318577"
       x2="36.451904"
       y2="37.999615"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.744756,0.000000,9.569132)" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient6447"
       id="radialGradient6453"
       cx="37.495606"
       cy="39.510023"
       fx="37.495606"
       fy="39.510023"
       r="2.5100370"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.737790,0.000000,9.844321)"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="0.19607843"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="1"
     inkscape:cx="33.312823"
     inkscape:cy="22.847042"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:window-width="926"
     inkscape:window-height="975"
     inkscape:window-x="397"
     inkscape:window-y="25"
     showguides="true"
     inkscape:guide-bbox="true"
     inkscape:showpageshadow="false" />
  <metadata
     id="metadata1311">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Terminal</dc:title>
        <dc:date>2005-10-15</dc:date>
        <dc:creator>
          <cc:Agent>
            <dc:title>Andreas Nilsson</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>terminal</rdf:li>
            <rdf:li>emulator</rdf:li>
            <rdf:li>term</rdf:li>
            <rdf:li>command line</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:contributor>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:contributor>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <path
       sodipodi:type="arc"
       style="opacity:1;fill:url(#radialGradient2230);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1.07686412;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
       id="path2222"
       sodipodi:cx="24.04163"
       sodipodi:cy="42.24213"
       sodipodi:rx="17.576654"
       sodipodi:ry="5.3538084"
       d="M 41.618284 42.24213 A 17.576654 5.3538084 0 1 1  6.4649754,42.24213 A 17.576654 5.3538084 0 1 1  41.618284 42.24213 z"
       transform="matrix(1.126713,0.000000,0.000000,0.856184,-2.891865,5.686653)" />
    <rect
       style="opacity:1.0000000;fill:url(#linearGradient2212);fill-opacity:1.0000000;fill-rule:evenodd;stroke:url(#linearGradient2220);stroke-width:0.99999946;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4.0000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000"
       id="rect1316"
       width="44.996037"
       height="38.998734"
       x="1.5026338"
       y="3.5015533"
       rx="4.8517075"
       ry="4.8517079" />
    <rect
       style="opacity:1.0000000;fill:url(#linearGradient2204);fill-opacity:1.0000000;fill-rule:evenodd;stroke:url(#linearGradient4260);stroke-width:0.99495775;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4.0000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000"
       id="rect1314"
       width="37.088005"
       height="29.022322"
       x="5.4962788"
       y="7.4827089"
       rx="1.6452150"
       ry="1.6452144" />
    <g
       id="g2286"
       style="opacity:0.25568182">
      <path
         id="path1345"
         d="M 8.0152033,11.500361 L 39.994145,11.500361"
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <path
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="M 8.0152033,13.500361 L 39.994145,13.500361"
         id="path2264" />
      <path
         id="path2266"
         d="M 8.0152033,15.500361 L 39.994145,15.500361"
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <path
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="M 8.0152033,17.500361 L 39.994145,17.500361"
         id="path2268" />
      <path
         id="path2270"
         d="M 8.0152033,19.500361 L 39.994145,19.500361"
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <path
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="M 8.0152033,21.500361 L 39.994145,21.500361"
         id="path2272" />
      <path
         id="path2274"
         d="M 8.0152033,23.500361 L 39.994145,23.500361"
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <path
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="M 8.0152033,25.500361 L 39.994145,25.500361"
         id="path2276" />
      <path
         id="path2278"
         d="M 8.0152033,27.500361 L 39.994145,27.500361"
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <path
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="M 8.0152033,29.500361 L 39.994145,29.500361"
         id="path2280" />
      <path
         id="path2282"
         d="M 8.0152033,31.500361 L 39.994145,31.500361"
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <path
         style="fill:none;fill-opacity:0.75;fill-rule:evenodd;stroke:#181f10;stroke-width:1.00072134;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="M 8.0152033,33.500361 L 39.994145,33.500361"
         id="path2284" />
    </g>
    <rect
       style="opacity:0.76373626;fill:none;fill-opacity:1;fill-rule:evenodd;stroke:url(#linearGradient2244);stroke-width:0.99999946;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
       id="rect2232"
       width="42.945141"
       height="37.000587"
       x="2.5542557"
       y="4.5007114"
       rx="3.7910469"
       ry="3.7910469" />
    <path
       style="font-size:18.585011px;font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;text-align:start;line-height:125.00000%;writing-mode:lr-tb;text-anchor:start;fill:#ffffff;fill-opacity:1.0000000;stroke:#6ed66e;stroke-width:1.0000000pt;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:0.27868852;font-family:Bitstream Vera Sans Mono"
       d="M 11.625000,20.679392 L 11.625000,17.625000 L 20.609828,21.685794 L 20.609828,23.541713 L 11.625000,27.629147 L 11.625000,24.583829 L 18.589396,22.729971 L 11.625000,20.679392 z M 30.517635,30.705752 L 30.517635,32.679948 L 19.614229,32.679948 L 19.614229,30.705752 L 30.517635,30.705752"
       id="text1340"
       sodipodi:nodetypes="ccccccccccccc" />
    <path
       sodipodi:nodetypes="ccccccc"
       style="opacity:0.53142856;fill:url(#linearGradient2673);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.25pt;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="M 7.625388,8 C 7.102102,8 6.05153,8.190188 6.05153,9.0259761 L 6.16958,25.542519 C 23.841567,24.579133 20.294433,17.286426 42,13.633318 L 41.937264,9.2913791 C 41.859002,8.1662868 41.397947,8.0594548 40.327115,8.066071 L 7.625388,8 z "
       id="path2443" />
    <rect
       style="opacity:0.71428573;fill:none;fill-opacity:1.0000000;fill-rule:evenodd;stroke:#000000;stroke-width:1.9999992;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4.0000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000"
       id="rect1340"
       width="34.026031"
       height="26.057468"
       x="6.9894562"
       y="8.9805145"
       rx="0.11773217"
       ry="0.11773217" />
    <rect
       style="opacity:1;fill:url(#radialGradient6453);fill-opacity:1;fill-rule:evenodd;stroke:url(#linearGradient5719);stroke-width:1.00000119;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
       id="rect5025"
       width="4.0200734"
       height="2.9590063"
       x="35.485569"
       y="37.514935"
       rx="0.35819405"
       ry="0.56022596" />
    <rect
       style="opacity:1;fill:#93d94c;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
       id="rect6458"
       width="2"
       height="2"
       x="32"
       y="38"
       rx="0.56022543"
       ry="0.56022543" />
    <path
       sodipodi:type="arc"
       style="opacity:1;color:#000000;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       id="path2300"
       sodipodi:cx="28.3125"
       sodipodi:cy="38.75"
       sodipodi:rx="0.5625"
       sodipodi:ry="0.5625"
       d="M 28.875 38.75 A 0.5625 0.5625 0 1 1  27.75,38.75 A 0.5625 0.5625 0 1 1  28.875 38.75 z"
       transform="translate(4.375000,-6.250000e-2)" />
  </g>
</svg>
