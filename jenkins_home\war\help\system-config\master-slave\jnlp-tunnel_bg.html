<div>
  Когато подчинен компютър е стартиран чрез JNLP, агентът се опитва да се свърже
  към конкретен порт по TCP към Jenkins, за да създаде комуникационен канал.
  Някои мрежи с повишено ниво на сигурност могат да предотвратят това. Това може
  да се случи също и когато Jenkins е зад машина, разпределяща натоварването,
  <a
    href="https://www.jenkins.io/doc/book/system-administration/reverse-proxy-configuration-apache/"
  >
    обратен сървър посредник
  </a>
  , в
  <a href="https://en.wikipedia.org/wiki/Demilitarized_zone_(computing)">
    демилитаризираната зона
  </a>
  ( и т.н.

  <p>
    Чрез възможността за тунел може да пренасочите тази връзка през към друг
    компютър или порт и това е полезно в случаите указани по-горе. Полето приема
    „
    <code>ХОСТ:ПОРТ</code>
    “, „
    <code>:ПОРТ</code>
    “ или „
    <code>ХОСТ:</code>
    “. При първия формат подчиненият компютър трябва да се свърже към този порт
    по TCP на зададения хост. Приема се, че сте настроили мрежата си, така че
    портът да бъде пренасочен към порта на Jenkins за JNLP.
  </p>

  <p>
    При втория и третия варианти пропуснатите стойности се наследяват от
    стандартните (хостът, на който Jenkins се изпълнява, и портът по TCP, който
    Jenkins е отворил). Специално форматът
    <code>ХОСТ:</code>
    е особено полезен, когато Jenkins работи на друга машина и е зад обратен
    сървър посредник по HTTP.
  </p>
</div>
