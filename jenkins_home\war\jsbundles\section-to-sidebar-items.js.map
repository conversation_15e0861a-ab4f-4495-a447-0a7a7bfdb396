{"version": 3, "file": "section-to-sidebar-items.js", "mappings": ";;;;AAAO,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EACnDF,QAAQ,CAACG,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAAC;EAChC,OAAOJ,QAAQ,CAACK,OAAO,CAACC,iBAAiB;AAC3C;AAEO,SAASC,IAAIA,CAACC,MAAM,EAAE;EAC3B,OAAOA,MAAM,CACVJ,IAAI,CAAC,CAAC,CACNK,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBC,WAAW,CAAC,CAAC;AAClB;;ACXyD;AAEzD,MAAMC,eAAe,GACnB,qKAAqK;AACvK,MAAMC,YAAY,GAAG,suCAAsuC;AAE3vCC,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,YAAY;EAC1C,MAAMC,YAAY,GAAGd,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC;EACrD,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,gBAAgB,CAACP,eAAe,CAAC;;EAEjE;EACAM,cAAc,CAACE,OAAO,CAAC,UAAUC,MAAM,EAAEC,CAAC,EAAE;IAC1C,MAAMC,QAAQ,GAAGf,IAAI,CAACa,MAAM,CAACG,WAAW,CAAC;IACzCH,MAAM,CAACI,EAAE,GAAGF,QAAQ;IACpB,MAAMG,IAAI,GAAGL,MAAM,CAACJ,aAAa,CAAC,KAAK,CAAC,GACpCI,MAAM,CAACJ,aAAa,CAAC,KAAK,CAAC,CAACU,SAAS,GACrCd,YAAY;IAChB,MAAMe,IAAI,GAAG7B,qBAAqB,CAAC;AACvC;AACA;AACA,0CAA0CwB,QAAQ;AAClD;AACA,0BAA0BG,IAAI;AAC9B;AACA;AACA,0BAA0BL,MAAM,CAACG,WAAW;AAC5C;AACA;AACA;AACA;AACA,KAAK,CAAC;IACFI,IAAI,CAACb,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACnC,MAAMc,gBAAgB,GAAG3B,QAAQ,CAAC4B,cAAc,CAC9CF,IAAI,CAACX,aAAa,CAAC,YAAY,CAAC,CAACc,OAAO,CAACC,SAC3C,CAAC;MAED,MAAMC,kBAAkB,GACtBJ,gBAAgB,CAACK,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGrB,MAAM,CAACsB,OAAO,GAAG,EAAE;MACpEtB,MAAM,CAACuB,QAAQ,CAAC;QACdF,GAAG,EAAEb,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGW,kBAAkB;QACrCK,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFtB,YAAY,CAACuB,qBAAqB,CAAC,WAAW,EAAEX,IAAI,CAAC;EACvD,CAAC,CAAC;;EAEF;EACA;EACA;EACA1B,QAAQ,CACLiB,gBAAgB,CAAC,gDAAgD,CAAC,CAClEC,OAAO,CAAEoB,KAAK,IAAK;IAClBA,KAAK,CAACC,aAAa,CAACC,MAAM,CAAC,CAAC;EAC9B,CAAC,CAAC;EAEJxC,QAAQ,CAACa,gBAAgB,CAAC,QAAQ,EAAE,MAAM4B,QAAQ,CAAC,CAAC,CAAC;EACrDA,QAAQ,CAAC,CAAC;AACZ,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAASA,QAAQA,CAAA,EAAG;EAClB,MAAMP,OAAO,GAAGQ,IAAI,CAACC,GAAG,CAAC/B,MAAM,CAACsB,OAAO,EAAE,CAAC,CAAC;EAC3C,MAAMlB,cAAc,GAAGhB,QAAQ,CAACiB,gBAAgB,CAACP,eAAe,CAAC;EAEjE,IAAIkC,eAAe,GAAG,IAAI;;EAE1B;EACA5B,cAAc,CAACE,OAAO,CAAC,UAAU2B,OAAO,EAAEzB,CAAC,EAAE;IAC3C,MAAM0B,eAAe,GACnB1B,CAAC,KAAK,CAAC,GACHpB,QAAQ,CAACiB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAChDD,cAAc,CAAC0B,IAAI,CAACC,GAAG,CAACvB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC2B,UAAU;IACnD,MAAMC,mBAAmB,GACvB5B,CAAC,KAAK,CAAC,GACH,CAAC,GACDyB,OAAO,CAACE,UAAU,CAACf,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAC9CrB,MAAM,CAACsB,OAAO,GACdY,eAAe,CAACG,YAAY,GAAG,CAAC;IAEtC,IAAIf,OAAO,IAAIc,mBAAmB,EAAE;MAClCJ,eAAe,GAAGC,OAAO;IAC3B;EACF,CAAC,CAAC;EAEF7C,QAAQ,CAACiB,gBAAgB,CAAC,oBAAoB,CAAC,CAACC,OAAO,CAAC,UAAUgC,QAAQ,EAAE;IAC1EA,QAAQ,CAACC,SAAS,CAACX,MAAM,CAAC,mBAAmB,CAAC;EAChD,CAAC,CAAC;EAEFxC,QAAQ,CACLe,aAAa,CAAC,8BAA8B,GAAG6B,eAAe,CAACrB,EAAE,GAAG,IAAI,CAAC,CACzE4B,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;AACvC,C", "sources": ["webpack://jenkins-ui/./src/main/js/util/dom.js", "webpack://jenkins-ui/./src/main/js/section-to-sidebar-items.js"], "sourcesContent": ["export function createElementFromHtml(html) {\n  const template = document.createElement(\"template\");\n  template.innerHTML = html.trim();\n  return template.content.firstElementChild;\n}\n\nexport function toId(string) {\n  return string\n    .trim()\n    .replace(/[\\W_]+/g, \"-\")\n    .toLowerCase();\n}\n", "import { createElementFromHtml, toId } from \"./util/dom\";\n\nconst HEADER_SELECTOR =\n  \".config-table .jenkins-app-bar h2, .config-table > .jenkins-section > .jenkins-section__title, .config-table > section > .jenkins-section > .jenkins-section__title\";\nconst DEFAULT_ICON = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path d=\"M262.29 192.31a64 64 0 1057.4 57.4 64.13 64.13 0 00-57.4-57.4zM416.39 256a154.34 154.34 0 01-1.53 20.79l45.21 35.46a10.81 10.81 0 012.45 13.75l-42.77 74a10.81 10.81 0 01-13.14 4.59l-44.9-18.08a16.11 16.11 0 00-15.17 1.75A164.48 164.48 0 01325 400.8a15.94 15.94 0 00-8.82 12.14l-6.73 47.89a11.08 11.08 0 01-10.68 9.17h-85.54a11.11 11.11 0 01-10.69-8.87l-6.72-47.82a16.07 16.07 0 00-9-12.22 155.3 155.3 0 01-21.46-12.57 16 16 0 00-15.11-1.71l-44.89 18.07a10.81 10.81 0 01-13.14-4.58l-42.77-74a10.8 10.8 0 012.45-13.75l38.21-30a16.05 16.05 0 006-14.08c-.36-4.17-.58-8.33-.58-12.5s.21-8.27.58-12.35a16 16 0 00-6.07-13.94l-38.19-30A10.81 10.81 0 0149.48 186l42.77-74a10.81 10.81 0 0113.14-4.59l44.9 18.08a16.11 16.11 0 0015.17-1.75A164.48 164.48 0 01187 111.2a15.94 15.94 0 008.82-12.14l6.73-47.89A11.08 11.08 0 01213.23 42h85.54a11.11 11.11 0 0110.69 8.87l6.72 47.82a16.07 16.07 0 009 12.22 155.3 155.3 0 0121.46 12.57 16 16 0 0015.11 1.71l44.89-18.07a10.81 10.81 0 0113.14 4.58l42.77 74a10.8 10.8 0 01-2.45 13.75l-38.21 30a16.05 16.05 0 00-6.05 14.08c.33 4.14.55 8.3.55 12.47z\" fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"32\"/></svg>`;\n\nwindow.addEventListener(\"load\", function () {\n  const sidebarItems = document.querySelector(\"#tasks\");\n  const sectionHeaders = document.querySelectorAll(HEADER_SELECTOR);\n\n  // Create the sidebar items\n  sectionHeaders.forEach(function (header, i) {\n    const headerId = toId(header.textContent);\n    header.id = headerId;\n    const icon = header.querySelector(\"svg\")\n      ? header.querySelector(\"svg\").outerHTML\n      : DEFAULT_ICON;\n    const item = createElementFromHtml(`\n        <div class=\"task\">\n            <span class=\"task-link-wrapper\">\n                <button data-section-id=${headerId} class=\"task-link\">\n                    <span class=\"task-icon-link\">\n                        ${icon}\n                    </span>\n                    <span class=\"task-link-text\">\n                        ${header.textContent}\n                    </span>\n                </button>\n            </span>\n        </div>\n    `);\n    item.addEventListener(\"click\", () => {\n      const headerToScrollTo = document.getElementById(\n        item.querySelector(\".task-link\").dataset.sectionId,\n      );\n\n      const sectionTopPosition =\n        headerToScrollTo.getBoundingClientRect().top + window.scrollY - 70;\n      window.scrollTo({\n        top: i === 0 ? 0 : sectionTopPosition,\n        behavior: \"smooth\",\n      });\n    });\n\n    sidebarItems.insertAdjacentElement(\"beforeend\", item);\n  });\n\n  // TODO - Remove when Matrix-Project plugin has been updated to only have one enable/disable project toggle\n  // Having multiple toggles on the same page for the same field corrupts submission for that field, so\n  // remove all but the first\n  document\n    .querySelectorAll(\".jenkins-form-item + span input[name='enable']\")\n    .forEach((input) => {\n      input.parentElement.remove();\n    });\n\n  document.addEventListener(\"scroll\", () => onScroll());\n  onScroll();\n});\n\n/**\n * Change the selected item depending on the user's vertical scroll position\n */\nfunction onScroll() {\n  const scrollY = Math.max(window.scrollY, 0);\n  const sectionHeaders = document.querySelectorAll(HEADER_SELECTOR);\n\n  let selectedSection = null;\n\n  // Calculate the top and height of each section to know when to switch selected sidebar item\n  sectionHeaders.forEach(function (section, i) {\n    const previousSection =\n      i === 1\n        ? document.querySelectorAll(\".jenkins-section\")[0]\n        : sectionHeaders[Math.max(i - 1, 0)].parentNode;\n    const viewportEntryOffset =\n      i === 0\n        ? 0\n        : section.parentNode.getBoundingClientRect().top +\n          window.scrollY -\n          previousSection.offsetHeight / 2;\n\n    if (scrollY >= viewportEntryOffset) {\n      selectedSection = section;\n    }\n  });\n\n  document.querySelectorAll(\".task-link--active\").forEach(function (selected) {\n    selected.classList.remove(\"task-link--active\");\n  });\n\n  document\n    .querySelector(\".task-link[data-section-id='\" + selectedSection.id + \"']\")\n    .classList.add(\"task-link--active\");\n}\n"], "names": ["createElementFromHtml", "html", "template", "document", "createElement", "innerHTML", "trim", "content", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "toId", "string", "replace", "toLowerCase", "HEADER_SELECTOR", "DEFAULT_ICON", "window", "addEventListener", "sidebarItems", "querySelector", "sectionHeaders", "querySelectorAll", "for<PERSON>ach", "header", "i", "headerId", "textContent", "id", "icon", "outerHTML", "item", "headerToScrollTo", "getElementById", "dataset", "sectionId", "sectionTopPosition", "getBoundingClientRect", "top", "scrollY", "scrollTo", "behavior", "insertAdjacentElement", "input", "parentElement", "remove", "onScroll", "Math", "max", "selectedSection", "section", "previousSection", "parentNode", "viewportEntryOffset", "offsetHeight", "selected", "classList", "add"], "sourceRoot": ""}