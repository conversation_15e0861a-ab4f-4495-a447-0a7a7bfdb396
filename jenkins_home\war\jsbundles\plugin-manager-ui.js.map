{"version": 3, "file": "plugin-manager-ui.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACuB;AACQ;AACK;AAEpC,IAAIG,KAAK,GAAG,KAAK;AACjB,IAAIC,OAAO,GAAG,CAAC,CAAC;;AAEhB;AACAA,OAAO,CAACC,OAAO,GAAG,YAAY;EAC5B,IAAIC,CAAC,GAAGN,gBAAC,CAAC,MAAM,CAAC,CAACO,IAAI,CAAC,cAAc,CAAC;EACtC,IAAI,CAACD,CAAC,EAAE;IACNA,CAAC,GAAG,EAAE;EACR;EACA,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACAF,OAAO,CAACI,IAAI,GAAG,UAAUC,GAAG,EAAE;EAC5BR,uBAAY,CAAC,CAAC,CAACU,QAAQ,CAACC,OAAO,CAACR,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGI,GAAG,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACAL,OAAO,CAACS,GAAG,GAAG,UAAUJ,GAAG,EAAEK,OAAO,EAAEC,OAAO,EAAE;EAC7C,IAAIZ,KAAK,EAAE;IACTa,OAAO,CAACC,GAAG,CAAC,OAAO,GAAGR,GAAG,CAAC;EAC5B;EACA,IAAIS,IAAI,GAAG;IACTT,GAAG,EAAEL,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGI,GAAG;IAC5BU,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,MAAM;IAChBP,OAAO,EAAEA;EACX,CAAC;EACD,IAAIC,OAAO,YAAYO,MAAM,EAAE;IAC7BtB,uBAAQ,CAACkB,IAAI,EAAEH,OAAO,CAAC;EACzB;EACAf,qBAAM,CAACkB,IAAI,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACA;AACAd,OAAO,CAACqB,IAAI,GAAG,UAAUhB,GAAG,EAAEiB,IAAI,EAAEZ,OAAO,EAAEC,OAAO,EAAE;EACpD,IAAIZ,KAAK,EAAE;IACTa,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAGR,GAAG,CAAC;EAC7B;;EAEA;EACA,IAAIkB,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,GAAG,GAAG3B,uBAAY,CAAC,CAAC;EACxB,IAAI4B,KAAK;EACT,IAAI,OAAO,IAAId,OAAO,EAAE;IACtBc,KAAK,GAAGd,OAAO,CAACc,KAAK;EACvB,CAAC,MAAM,IAAI,OAAO,IAAID,GAAG,EAAE;IACzBC,KAAK,GAAGD,GAAG,CAACC,KAAK;EACnB;EAEA,IAAIA,KAAK,EAAE;IACTF,OAAO,CAACE,KAAK,CAACC,SAAS,CAAC,GAAGD,KAAK,CAACE,KAAK;EACxC;EAEA,IAAIC,QAAQ,GAAGN,IAAI;EACnB,IAAIM,QAAQ,YAAYV,MAAM,EAAE;IAC9B,IAAIO,KAAK,EAAE;MACTG,QAAQ,GAAGhC,uBAAQ,CAAC,CAAC,CAAC,EAAEgC,QAAQ,CAAC;MACjCA,QAAQ,CAACH,KAAK,CAACC,SAAS,CAAC,GAAGD,KAAK,CAACE,KAAK;IACzC;IACAC,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC;EACrC;EAEA,IAAId,IAAI,GAAG;IACTT,GAAG,EAAEL,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGI,GAAG;IAC5BU,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,MAAM;IAChBK,IAAI,EAAEM,QAAQ;IACdG,WAAW,EAAE,kBAAkB;IAC/BrB,OAAO,EAAEA,OAAO;IAChBa,OAAO,EAAEA;EACX,CAAC;EACD,IAAIZ,OAAO,YAAYO,MAAM,EAAE;IAC7BtB,uBAAQ,CAACkB,IAAI,EAAEH,OAAO,CAAC;EACzB;EACAf,qBAAM,CAACkB,IAAI,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACAd,OAAO,CAACgC,cAAc,GAAG,YAAY;EACnC,OAAOlC,mBAAU;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACAE,OAAO,CAACiC,gBAAgB,GAAG,UAAUC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACjEpC,OAAO,CAACS,GAAG,CAAC,gCAAgC,GAAGyB,UAAU,EAAE,UAAUG,GAAG,EAAE;IACxE,IAAIA,GAAG,CAACC,MAAM,KAAK,IAAI,EAAE;MACvB,IAAIF,OAAO,EAAE;QACXA,OAAO,CAACC,GAAG,CAACE,OAAO,CAAC;MACtB;MACA,MAAM,oCAAoC,GAAGF,GAAG,CAACE,OAAO;IAC1D;IAEA,IAAIC,YAAY,GAAGH,GAAG,CAACf,IAAI;IAE3B,IAAI,WAAW,KAAK,OAAOmB,KAAK,EAAE;MAChCD,YAAY,GAAG,IAAIC,KAAK,CAACD,YAAY,EAAE;QACrC/B,GAAG,EAAE,SAAAA,CAAUiC,MAAM,EAAEC,QAAQ,EAAE;UAC/B,IAAIA,QAAQ,IAAID,MAAM,EAAE;YACtB,OAAOA,MAAM,CAACC,QAAQ,CAAC;UACzB;UACA,IAAI5C,KAAK,EAAE;YACTa,OAAO,CAACC,GAAG,CAAC,GAAG,GAAG8B,QAAQ,GAAG,oCAAoC,CAAC;UACpE;UACA,OAAOA,QAAQ;QACjB;MACF,CAAC,CAAC;IACJ;IAEAR,OAAO,CAACK,YAAY,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACAxC,OAAO,CAAC4C,gBAAgB,GAAG,UAAUC,MAAM,EAAEV,OAAO,EAAE;EACpD;EACA,IAAIS,gBAAgB,GAAG,SAAAA,CAAA,EAAY;IACjC5C,OAAO,CAACS,GAAG,CACT,wCAAwC,GAAGoC,MAAM,EACjD,UAAUC,QAAQ,EAAE;MAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;QAC5BH,OAAO,CAAC,KAAK,EAAE,IAAI,EAAEW,QAAQ,CAACP,OAAO,CAAC;MACxC;;MAEA;MACA;MACA,IAAIQ,iBAAiB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;MAC7D,IACEA,iBAAiB,CAACC,OAAO,CAACF,QAAQ,CAACxB,IAAI,CAAC2B,UAAU,CAAC,IAAI,CAAC,IACxDF,iBAAiB,CAACC,OAAO,CAACF,QAAQ,CAACxB,IAAI,CAAC4B,QAAQ,CAAC,IAAI,CAAC,EACtD;QACAC,UAAU,CAACP,gBAAgB,EAAE,GAAG,CAAC;MACnC,CAAC,MAAM;QACL;QACA;QACA,IACEE,QAAQ,CAACR,MAAM,KAAK,IAAI,IACxBQ,QAAQ,CAACxB,IAAI,CAAC2B,UAAU,KAAK,IAAI,IAChCH,QAAQ,CAACxB,IAAI,CAAC4B,QAAQ,KAAK,IAAI,IAC9BJ,QAAQ,CAACxB,IAAI,CAAC4B,QAAQ,KAAK,SAAU,EACvC;UACA;UACAf,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;QACvB,CAAC,MAAM;UACLA,OAAO,CAAC,IAAI,CAAC;QACf;MACF;IACF,CAAC,EACD;MACEiB,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAC7C,IAAIF,GAAG,CAACf,MAAM,KAAK,GAAG,EAAE;UACtBtC,OAAO,CAACI,IAAI,CAAC,QAAQ,CAAC;QACxB,CAAC,MAAM;UACL+B,OAAO,CAACqB,IAAI,CAAC;YAAEC,OAAO,EAAE,IAAI;YAAEC,YAAY,EAAEH;UAAY,CAAC,CAAC;QAC5D;MACF;IACF,CACF,CAAC;EACH,CAAC;EACDX,gBAAgB,CAAC,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA5C,OAAO,CAACM,SAAS,GAAG,UAAUqD,KAAK,EAAE;EACnCA,KAAK,GAAG/D,gBAAC,CAAC+D,KAAK,CAAC;EAChB,IAAInC,GAAG,GAAG3B,uBAAY,CAAC,CAAC;EACxBD,gBAAC,CAACgE,GAAG,CAACC,QAAQ,CAAC,CACZC,IAAI,CAAC,QAAQ,CAAC,CACdC,IAAI,CAAC,YAAY;IAChB,IAAIC,WAAW,GAAG,IAAI,CAACC,aAAa;IACpC,IAAIC,EAAE,GAAGtE,gBAAC,CAAC,IAAI,CAAC,CAACuE,QAAQ,CAAC,CAAC,CAACL,IAAI,CAAC,MAAM,CAAC;IACxCI,EAAE,CAACH,IAAI,CAAC,YAAY;MAClB,IAAIJ,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACrBnC,GAAG,GAAGwC,WAAW;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACJ,OAAOxC,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACAxB,OAAO,CAACoE,aAAa,GAAG,UAAUT,KAAK,EAAE;EACvCA,KAAK,GAAG/D,gBAAC,CAAC+D,KAAK,CAAC;EAChB,IAAInC,GAAG,GAAGxB,OAAO,CAACM,SAAS,CAACqD,KAAK,CAAC;EAClC,IAAIU,IAAI,GAAGV,KAAK,CAAC,CAAC,CAAC;EACnB,IAAInC,GAAG,CAAC8C,aAAa,CAACD,IAAI,CAAC,EAAE;IAC3B,OACEV,KAAK,CAACY,SAAS,CAAC,CAAC,GACjB,GAAG,GACH3E,sBAAO,CAAC;MACN,YAAY,EAAE,EAAE;MAChB6E,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEf,KAAK,CAACG,IAAI,CAAC,kBAAkB,CAAC,CAACa,GAAG,CAAC;IAC3C,CAAC,CAAC;EAEN;EACA,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA3E,OAAO,CAAC4E,YAAY,GAAG,UAAUjB,KAAK,EAAE;EACtCA,KAAK,GAAG/D,gBAAC,CAAC+D,KAAK,CAAC;EAChB,IAAInC,GAAG,GAAGxB,OAAO,CAACM,SAAS,CAACqD,KAAK,CAAC;EAClC,OAAOnC,GAAG,CAACC,KAAK;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACAzB,OAAO,CAAC6E,WAAW,GAAG,UAAUxE,GAAG,EAAEsD,KAAK,EAAEjD,OAAO,EAAEC,OAAO,EAAE;EAC5DgD,KAAK,GAAG/D,gBAAC,CAAC+D,KAAK,CAAC;EAChB,IAAImB,QAAQ,GAAG9E,OAAO,CAACoE,aAAa,CAACT,KAAK,CAAC;EAC3C,IAAIlC,KAAK,GAAGzB,OAAO,CAAC4E,YAAY,CAACjB,KAAK,CAAC;EACvC3D,OAAO,CAACqB,IAAI,CACVhB,GAAG,EACHyE,QAAQ,EACRpE,OAAO,EACPd,uBAAQ,CACN;IACEmF,WAAW,EAAE,KAAK;IAClBhD,WAAW,EAAE,mCAAmC;IAChDN,KAAK,EAAEA;EACT,CAAC,EACDd,OACF,CACF,CAAC;AACH,CAAC;AAED,iDAAeX,OAAO;;ACnQtB;AACA;AACA;AACsC;;AAEtC;AACA,IAAIgF,OAAO;AAEX,IAAIC,aAAa,GAAG,CAAC,CAAC;AAEtBA,aAAa,CAACC,iBAAiB,GAAG,UAAU/C,OAAO,EAAE;EACnDnC,YAAO,CAACS,GAAG,CACT,iCAAiC,EACjC,UAAUqC,QAAQ,EAAE;IAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;MAC5BH,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEnC,IAAI,EAAEwB,QAAQ,CAACxB;MAAK,CAAC,CAAC;MACpD;IACF;IAEAa,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC;EACjD,CAAC,EACD;IACE6D,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEH;MAAY,CAAC,CAAC;IAC5D;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA0B,aAAa,CAACI,IAAI,GAAG,UAAUlD,OAAO,EAAE;EACtC8C,aAAa,CAACC,iBAAiB,CAAC,UAAUI,uBAAuB,EAAE;IACjEN,OAAO,GAAG,CAAC,CAAC;IACZA,OAAO,CAACO,KAAK,GAAG,EAAE;IAClBP,OAAO,CAACQ,kBAAkB,GAAG,EAAE;IAC/BR,OAAO,CAACS,gBAAgB,GAAGH,uBAAuB;IAClD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,uBAAuB,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACvD,IAAIE,cAAc,GAAGN,uBAAuB,CAACI,CAAC,CAAC;MAC/C,IAAIG,eAAe,GAAGD,cAAc,CAACZ,OAAO;MAC5C,KAAK,IAAIc,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,eAAe,CAACF,MAAM,EAAEG,EAAE,EAAE,EAAE;QAClD,IAAIC,MAAM,GAAGF,eAAe,CAACC,EAAE,CAAC;QAChC,IAAIE,UAAU,GAAGD,MAAM,CAACE,IAAI;QAC5B,IAAIjB,OAAO,CAACO,KAAK,CAACvC,OAAO,CAACgD,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;UAC5ChB,OAAO,CAACO,KAAK,CAACW,IAAI,CAACF,UAAU,CAAC;UAC9B,IAAID,MAAM,CAACI,SAAS,EAAE;YACpBnB,OAAO,CAACQ,kBAAkB,CAACU,IAAI,CAACF,UAAU,CAAC;UAC7C,CAAC,MAAM,IAAIJ,cAAc,CAACQ,QAAQ,KAAK,WAAW,EAAE;YAClD,IAAIC,QAAQ,GACVC,MAAM,CAACC,SAAS,CAACC,YAAY,IAAIF,MAAM,CAACC,SAAS,CAACF,QAAQ;YAC5D,IAAII,IAAI,GAAGJ,QAAQ,CAACK,iBAAiB,CAAC,CAAC;YACvC,IAAIV,UAAU,KAAK,eAAe,GAAGS,IAAI,EAAE;cACzCzB,OAAO,CAACQ,kBAAkB,CAACU,IAAI,CAACF,UAAU,CAAC;YAC7C;UACF;QACF;MACF;IACF;IACA7D,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,IAAIiD,+BAA+B,GAAG,EAAE,GAAG,IAAI;;AAE/C;AACA;AACA;AACA;AACAH,aAAa,CAACD,OAAO,GAAG,YAAY;EAClC,OAAOA,OAAO,CAACS,gBAAgB;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACAR,aAAa,CAAC0B,WAAW,GAAG,YAAY;EACtC,OAAO3B,OAAO,CAACO,KAAK;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAN,aAAa,CAAC2B,sBAAsB,GAAG,YAAY;EACjD,OAAO5B,OAAO,CAACQ,kBAAkB,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA5B,aAAa,CAAC6B,cAAc,GAAG,UAAU9B,OAAO,EAAE7C,OAAO,EAAE;EACzDnC,YAAO,CAACqB,IAAI,CACV,+BAA+B,EAC/B;IAAE0F,WAAW,EAAE,IAAI;IAAE/B,OAAO,EAAEA;EAAQ,CAAC,EACvC,UAAUlC,QAAQ,EAAE;IAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;MAC5BH,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEZ,QAAQ,CAACP;MAAQ,CAAC,CAAC;MAC/D;IACF;IAEAJ,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC0F,aAAa,CAAC;EAC/D,CAAC,EACD;IACE7B,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEH;MAAY,CAAC,CAAC;IAC5D;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA0B,aAAa,CAACgC,aAAa,GAAG,UAAU9E,OAAO,EAAE6E,aAAa,EAAE;EAC9D,IAAI3G,GAAG,GAAG,6BAA6B;EACvC,IAAI2G,aAAa,KAAKE,SAAS,EAAE;IAC/B7G,GAAG,IAAI,iBAAiB,GAAG2G,aAAa;EAC1C;EACAhH,YAAO,CAACS,GAAG,CACTJ,GAAG,EACH,UAAUyC,QAAQ,EAAE;IAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;MAC5BH,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEZ,QAAQ,CAACP;MAAQ,CAAC,CAAC;MAC/D;IACF;IAEAJ,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC;EACjD,CAAC,EACD;IACE6D,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEH;MAAY,CAAC,CAAC;IAC5D;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA0B,aAAa,CAACQ,gBAAgB,GAAG,UAAUtD,OAAO,EAAE;EAClDnC,YAAO,CAACS,GAAG,CACT,wBAAwB,EACxB,UAAUqC,QAAQ,EAAE;IAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;MAC5BH,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEZ,QAAQ,CAACP;MAAQ,CAAC,CAAC;MAC/D;IACF;IAEAJ,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC;EACjD,CAAC,EACD;IACE6D,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEH;MAAY,CAAC,CAAC;IAC5D;EACF,CACF,CAAC;AACH,CAAC;AAED0B,aAAa,CAACkC,sBAAsB,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAElF,OAAO,EAAE;EACtEnC,YAAO,CAACS,GAAG,CACT,qCAAqC,GAAG2G,KAAK,GAAG,SAAS,GAAGC,KAAK,EACjE,UAAUvE,QAAQ,EAAE;IAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;MAC5BH,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEZ,QAAQ,CAACP;MAAQ,CAAC,CAAC;MAC/D;IACF;IAEAJ,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC;EACjD,CAAC,EACD;IACE6D,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEH;MAAY,CAAC,CAAC;IAC5D;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA0B,aAAa,CAACqC,uBAAuB,GAAG,UAAUnF,OAAO,EAAE6E,aAAa,EAAE;EACxE,IAAI3G,GAAG,GAAG,uCAAuC;EACjD,IAAI2G,aAAa,KAAKE,SAAS,EAAE;IAC/B7G,GAAG,IAAI,iBAAiB,GAAG2G,aAAa;EAC1C;EACAhH,YAAO,CAACS,GAAG,CACTJ,GAAG,EACH,UAAUyC,QAAQ,EAAE;IAClB,IAAIA,QAAQ,CAACR,MAAM,KAAK,IAAI,EAAE;MAC5BH,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEZ,QAAQ,CAACP;MAAQ,CAAC,CAAC;MAC/D;IACF;IAEAJ,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC;EACjD,CAAC,EACD;IACE6D,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,YAAY,EAAEH;MAAY,CAAC,CAAC;IAC5D;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA0B,aAAa,CAACsC,eAAe,GAAG,UAAUpF,OAAO,EAAE;EACjDnC,YAAO,CAACqB,IAAI,CACV,8BAA8B,EAC9B,CAAC,CAAC,EACF,YAAY;IACVc,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;EAClC,CAAC,EACD;IACE0B,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAElB,OAAO,EAAEgB;MAAY,CAAC,CAAC;IACvD;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA0B,aAAa,CAACuC,gBAAgB,GAAG,UAAUrF,OAAO,EAAE;EAClDnC,YAAO,CAACS,GAAG,CACT,4BAA4B,EAC5B,UAAUqC,QAAQ,EAAE;IAClBX,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,EAAEX,QAAQ,CAACxB,IAAI,CAAC;EACjD,CAAC,EACD;IACE6D,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAElB,OAAO,EAAEgB;MAAY,CAAC,CAAC;IACvD;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA0B,aAAa,CAACwC,kBAAkB,GAAG,UAAUtF,OAAO,EAAE;EACpDnC,YAAO,CAACqB,IAAI,CACV,mCAAmC,EACnC,CAAC,CAAC,EACF,YAAY;IACVc,OAAO,CAAC,CAAC;EACX,CAAC,EACD;IACEgD,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAElB,OAAO,EAAEgB;MAAY,CAAC,CAAC;IACvD;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA0B,aAAa,CAACyC,cAAc,GAAG,UAAUvF,OAAO,EAAE;EAChDnC,YAAO,CAACqB,IAAI,CACV,2BAA2B,EAC3B,CAAC,CAAC,EACF,YAAY;IACVc,OAAO,CAACqB,IAAI,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;EAClC,CAAC,EACD;IACE0B,OAAO,EAAEC,+BAA+B;IACxChC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;MAC7CpB,OAAO,CAACqB,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAElB,OAAO,EAAEgB;MAAY,CAAC,CAAC;IACvD;EACF,CACF,CAAC;AACH,CAAC;AAED,sDAAe0B,aAAa;;AC1SW;AAEuC;AAC9B;AAEhD,SAAS4C,WAAWA,CAACC,WAAW,EAAE;EAChC;EACA7C,iBAAa,CAACkC,sBAAsB,CAClCW,WAAW,CAACC,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,EAChC,EAAE,EACF,UAAUhD,OAAO,EAAE;IACjB,IAAIiD,YAAY,GAAGpE,QAAQ,CAACqE,cAAc,CAAC,SAAS,CAAC;IACrD,IAAIC,KAAK,GAAGF,YAAY,CAACG,aAAa,CAAC,OAAO,CAAC;IAC/C,IAAIC,KAAK,GAAGJ,YAAY,CAACK,OAAO,CAACC,QAAQ,KAAK,MAAM;IACpD,IAAIC,eAAe,GAAG,EAAE;IAExB,IAAIC,WAAW,GAAG5E,QAAQ,CAACqE,cAAc,CAAC,YAAY,CAAC;IACvDO,WAAW,CAACC,aAAa,CAACC,SAAS,CAACC,MAAM,CAAC,yBAAyB,CAAC;IAErE,SAASC,eAAeA,CAAA,EAAG;MACzB,IAAI,CAACR,KAAK,EAAE;QACVF,KAAK,CAACW,SAAS,GAAG,EAAE;MACtB,CAAC,MAAM;QACL,IAAIC,IAAI,GAAGZ,KAAK,CAACa,gBAAgB,CAAC,IAAI,CAAC;QACvC,IAAID,IAAI,EAAE;UACRP,eAAe,GAAG,EAAE;UACpBO,IAAI,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;YAC1B,IAAIC,KAAK,GAAGD,GAAG,CAACd,aAAa,CAAC,OAAO,CAAC;YACtC,IAAIe,KAAK,CAACC,OAAO,KAAK,IAAI,EAAE;cAC1B,IAAIpD,UAAU,GAAGmD,KAAK,CAAClD,IAAI,CAACoD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cACzCb,eAAe,CAACtC,IAAI,CAACF,UAAU,CAAC;YAClC,CAAC,MAAM;cACLkD,GAAG,CAACN,MAAM,CAAC,CAAC;YACd;UACF,CAAC,CAAC;QACJ;MACF;IACF;IAEAC,eAAe,CAAC,CAAC;IACjB,IAAIE,IAAI,GAAGnB,mBAAsB,CAAC;MAChC5C,OAAO,EAAEA,OAAO,CAACsE,MAAM,CACpBvD,MAAM,IAAKyC,eAAe,CAACxF,OAAO,CAAC+C,MAAM,CAACE,IAAI,CAAC,KAAK,CAAC,CACxD,CAAC;MACDoC;IACF,CAAC,CAAC;IAEFF,KAAK,CAACoB,kBAAkB,CAAC,WAAW,EAAER,IAAI,CAAC;IAE3CS,wBAAwB,CAAC,CAAC;EAC5B,CACF,CAAC;AACH;AAEA,IAAIC,YAAY,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAC9B7B,WAAW,CAAC6B,CAAC,CAAChH,MAAM,CAACf,KAAK,CAAC;AAC7B,CAAC;AAED,IAAIgI,eAAe,GAAGhC,kBAAQ,CAAC8B,YAAY,EAAE,GAAG,CAAC;AAEjD5F,QAAQ,CAAC+F,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACxD,IAAInB,WAAW,GAAG5E,QAAQ,CAACqE,cAAc,CAAC,YAAY,CAAC;EACvDO,WAAW,CAACmB,gBAAgB,CAAC,OAAO,EAAE,UAAUF,CAAC,EAAE;IACjDC,eAAe,CAACD,CAAC,CAAC;IAClBjB,WAAW,CAACC,aAAa,CAACC,SAAS,CAACkB,GAAG,CAAC,yBAAyB,CAAC;EACpE,CAAC,CAAC;EAEFhC,WAAW,CAACY,WAAW,CAAC9G,KAAK,CAAC;EAE9BwB,UAAU,CAAC,YAAY;IACrB2G,oBAAoB,CAACtG,IAAI,CAAC,CAAC;EAC7B,CAAC,EAAE,GAAG,CAAC;;EAEP;EACA,MAAMuG,iBAAiB,GAAGlG,QAAQ,CAACuE,aAAa,CAAC,sBAAsB,CAAC;EACxE,IAAI2B,iBAAiB,EAAE;IACrBC,eAAe,CAACC,IAAI,CAClBF,iBAAiB,CAACG,OAAO,CAACC,WAAW,EACrCH,eAAe,CAACI,KAClB,CAAC;EACH;AACF,CAAC,CAAC;AAEF,SAASZ,wBAAwBA,CAAA,EAAG;EAClC;EACA,MAAMa,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OACExG,QAAQ,CAACmF,gBAAgB,CAAC,gCAAgC,CAAC,CAACrD,MAAM,GAAG,CAAC;EAE1E,CAAC;EACD,MAAM2E,aAAa,GAAGzG,QAAQ,CAACuE,aAAa,CAAC,iBAAiB,CAAC;EAC/D,MAAMmC,yBAAyB,GAAG1G,QAAQ,CAACuE,aAAa,CACtD,+BACF,CAAC;EACD,IAAI,CAACiC,qBAAqB,CAAC,CAAC,EAAE;IAC5BC,aAAa,CAACE,QAAQ,GAAG,IAAI;IAC7BD,yBAAyB,CAACC,QAAQ,GAAG,IAAI;EAC3C;EACA,MAAMC,UAAU,GAAG5G,QAAQ,CAACmF,gBAAgB,CAAC,wBAAwB,CAAC;EACtEyB,UAAU,CAACxB,OAAO,CAAEyB,QAAQ,IAAK;IAC/BA,QAAQ,CAACd,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACvCzG,UAAU,CAAC,MAAM;QACfmH,aAAa,CAACE,QAAQ,GAAG,CAACH,qBAAqB,CAAC,CAAC;QACjDE,yBAAyB,CAACC,QAAQ,GAAG,CAACH,qBAAqB,CAAC,CAAC;MAC/D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;;;;;;AC3GA,iBAAiB,mBAAO,CAAC,IAAmD;AAC5E,0BAA0B;AAC1B,iEAAiE;AACjE,yIAAyI;AACzI;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gJAAgJ,qBAAqB,gFAAgF,SAAS,oBAAoB,QAAQ,wBAAwB;AAClT;AACA;AACA;AACA;AACA,wLAAwL;AACxL;AACA;AACA,2HAA2H,qBAAqB,gFAAgF,SAAS,sBAAsB,QAAQ,wBAAwB;AAC/R,wHAAwH,qBAAqB,gFAAgF,SAAS,sBAAsB,QAAQ,wBAAwB;AAC5R,kIAAkI,qBAAqB,gFAAgF,SAAS,sBAAsB,QAAQ,wBAAwB;AACtS,2IAA2I,qBAAqB,iFAAiF,SAAS,sBAAsB,QAAQ,wBAAwB;AAChT,2HAA2H,qBAAqB,iFAAiF,SAAS,sBAAsB,QAAQ,wBAAwB;AAChS,wHAAwH,qBAAqB,iFAAiF,SAAS,sBAAsB,QAAQ,wBAAwB;AAC7R;AACA,iIAAiI,qBAAqB,iGAAiG,SAAS,qBAAqB,QAAQ,wBAAwB;AACrT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2GAA2G,mEAAmE,uBAAuB,gFAAgF,SAAS,sBAAsB,QAAQ,wBAAwB;AACpV;AACA,CAAC;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,2GAA2G,qJAAqJ,uBAAuB,iFAAiF,SAAS,sBAAsB,QAAQ,wBAAwB;AACva;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;;AAEA,8GAA8G,gEAAgE,uBAAuB,gFAAgF,SAAS,oBAAoB,QAAQ,uBAAuB;AACjV,CAAC,gBAAgB;;;;;;UCxKjB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA;WACA;WACA,kBAAkB,qBAAqB;WACvC,oHAAoH,iDAAiD;WACrK;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC7BA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD,8CAA8C;;;;;WCA9C;;;;;WCAA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,8CAA8C;;WAE9C;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,iCAAiC,mCAAmC;WACpE;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;WClDA;;;;;UEAA;UACA;UACA;UACA,8EAA8E,kCAAkC;UAChH", "sources": ["webpack://jenkins-ui/./src/main/js/util/jenkins.js", "webpack://jenkins-ui/./src/main/js/api/pluginManager.js", "webpack://jenkins-ui/./src/main/js/plugin-manager-ui.js", "webpack://jenkins-ui/./src/main/js/templates/plugin-manager/available.hbs", "webpack://jenkins-ui/webpack/bootstrap", "webpack://jenkins-ui/webpack/runtime/chunk loaded", "webpack://jenkins-ui/webpack/runtime/compat get default export", "webpack://jenkins-ui/webpack/runtime/define property getters", "webpack://jenkins-ui/webpack/runtime/global", "webpack://jenkins-ui/webpack/runtime/hasOwnProperty shorthand", "webpack://jenkins-ui/webpack/runtime/runtimeId", "webpack://jenkins-ui/webpack/runtime/jsonp chunk loading", "webpack://jenkins-ui/webpack/runtime/nonce", "webpack://jenkins-ui/webpack/before-startup", "webpack://jenkins-ui/webpack/startup", "webpack://jenkins-ui/webpack/after-startup"], "sourcesContent": ["/**\n * Jenkins JS Modules common utility functions\n */\nimport $ from \"jquery\";\nimport wh from \"window-handle\";\nimport Handlebars from \"handlebars\";\n\nvar debug = false;\nvar jenkins = {};\n\n// gets the base Jenkins URL including context path\njenkins.baseUrl = function () {\n  var u = $(\"head\").attr(\"data-rooturl\");\n  if (!u) {\n    u = \"\";\n  }\n  return u;\n};\n\n/**\n * redirect\n */\njenkins.goTo = function (url) {\n  wh.getWindow().location.replace(jenkins.baseUrl() + url);\n};\n\n/**\n * Jenkins AJAX GET callback.\n * If last parameter is an object, will be extended to jQuery options (e.g. pass { error: function() ... } to handle errors)\n */\njenkins.get = function (url, success, options) {\n  if (debug) {\n    console.log(\"get: \" + url);\n  }\n  var args = {\n    url: jenkins.baseUrl() + url,\n    type: \"GET\",\n    cache: false,\n    dataType: \"json\",\n    success: success,\n  };\n  if (options instanceof Object) {\n    $.extend(args, options);\n  }\n  $.ajax(args);\n};\n\n/**\n * Jenkins AJAX POST callback, formats data as a JSON object post\n * If last parameter is an object, will be extended to jQuery options (e.g. pass { error: function() ... } to handle errors)\n */\njenkins.post = function (url, data, success, options) {\n  if (debug) {\n    console.log(\"post: \" + url);\n  }\n\n  // handle crumbs\n  var headers = {};\n  var wnd = wh.getWindow();\n  var crumb;\n  if (\"crumb\" in options) {\n    crumb = options.crumb;\n  } else if (\"crumb\" in wnd) {\n    crumb = wnd.crumb;\n  }\n\n  if (crumb) {\n    headers[crumb.fieldName] = crumb.value;\n  }\n\n  var formBody = data;\n  if (formBody instanceof Object) {\n    if (crumb) {\n      formBody = $.extend({}, formBody);\n      formBody[crumb.fieldName] = crumb.value;\n    }\n    formBody = JSON.stringify(formBody);\n  }\n\n  var args = {\n    url: jenkins.baseUrl() + url,\n    type: \"POST\",\n    cache: false,\n    dataType: \"json\",\n    data: formBody,\n    contentType: \"application/json\",\n    success: success,\n    headers: headers,\n  };\n  if (options instanceof Object) {\n    $.extend(args, options);\n  }\n  $.ajax(args);\n};\n\n/**\n *  handlebars setup, done for backwards compatibility because some plugins depend on it\n */\njenkins.initHandlebars = function () {\n  return Handlebars;\n};\n\n/**\n * Load translations for the given bundle ID, provide the message object to the handler.\n * Optional error handler as the last argument.\n */\njenkins.loadTranslations = function (bundleName, handler, onError) {\n  jenkins.get(\"/i18n/resourceBundle?baseName=\" + bundleName, function (res) {\n    if (res.status !== \"ok\") {\n      if (onError) {\n        onError(res.message);\n      }\n      throw \"Unable to load localization data: \" + res.message;\n    }\n\n    var translations = res.data;\n\n    if (\"undefined\" !== typeof Proxy) {\n      translations = new Proxy(translations, {\n        get: function (target, property) {\n          if (property in target) {\n            return target[property];\n          }\n          if (debug) {\n            console.log('\"' + property + '\" not found in translation bundle.');\n          }\n          return property;\n        },\n      });\n    }\n\n    handler(translations);\n  });\n};\n\n/**\n * Runs a connectivity test, calls handler with a boolean whether there is sufficient connectivity to the internet\n */\njenkins.testConnectivity = function (siteId, handler) {\n  // check the connectivity api\n  var testConnectivity = function () {\n    jenkins.get(\n      \"/updateCenter/connectionStatus?siteId=\" + siteId,\n      function (response) {\n        if (response.status !== \"ok\") {\n          handler(false, true, response.message);\n        }\n\n        // Define statuses, which need additional check iteration via async job on the Jenkins master\n        // Statuses like \"OK\" or \"SKIPPED\" are considered as fine.\n        var uncheckedStatuses = [\"PRECHECK\", \"CHECKING\", \"UNCHECKED\"];\n        if (\n          uncheckedStatuses.indexOf(response.data.updatesite) >= 0 ||\n          uncheckedStatuses.indexOf(response.data.internet) >= 0\n        ) {\n          setTimeout(testConnectivity, 100);\n        } else {\n          // Update site should be always reachable, but we do not require the internet connection\n          // if it's explicitly skipped by the update center\n          if (\n            response.status !== \"ok\" ||\n            response.data.updatesite !== \"OK\" ||\n            (response.data.internet !== \"OK\" &&\n              response.data.internet !== \"SKIPPED\")\n          ) {\n            // no connectivity, but not fatal\n            handler(false, false);\n          } else {\n            handler(true);\n          }\n        }\n      },\n      {\n        error: function (xhr, textStatus, errorThrown) {\n          if (xhr.status === 403) {\n            jenkins.goTo(\"/login\");\n          } else {\n            handler.call({ isError: true, errorMessage: errorThrown });\n          }\n        },\n      },\n    );\n  };\n  testConnectivity();\n};\n\n/**\n * gets the window containing a form, taking in to account top-level iframes\n */\njenkins.getWindow = function ($form) {\n  $form = $($form);\n  var wnd = wh.getWindow();\n  $(top.document)\n    .find(\"iframe\")\n    .each(function () {\n      var windowFrame = this.contentWindow;\n      var $f = $(this).contents().find(\"form\");\n      $f.each(function () {\n        if ($form[0] === this) {\n          wnd = windowFrame;\n        }\n      });\n    });\n  return wnd;\n};\n\n/**\n * Builds a stapler form post\n */\njenkins.buildFormPost = function ($form) {\n  $form = $($form);\n  var wnd = jenkins.getWindow($form);\n  var form = $form[0];\n  if (wnd.buildFormTree(form)) {\n    return (\n      $form.serialize() +\n      \"&\" +\n      $.param({\n        \"core:apply\": \"\",\n        Submit: \"Save\",\n        json: $form.find(\"input[name=json]\").val(),\n      })\n    );\n  }\n  return \"\";\n};\n\n/**\n * Gets the crumb, if crumbs are enabled\n */\njenkins.getFormCrumb = function ($form) {\n  $form = $($form);\n  var wnd = jenkins.getWindow($form);\n  return wnd.crumb;\n};\n\n/**\n * Jenkins Stapler JSON POST callback\n * If last parameter is an object, will be extended to jQuery options (e.g. pass { error: function() ... } to handle errors)\n */\njenkins.staplerPost = function (url, $form, success, options) {\n  $form = $($form);\n  var postBody = jenkins.buildFormPost($form);\n  var crumb = jenkins.getFormCrumb($form);\n  jenkins.post(\n    url,\n    postBody,\n    success,\n    $.extend(\n      {\n        processData: false,\n        contentType: \"application/x-www-form-urlencoded\",\n        crumb: crumb,\n      },\n      options,\n    ),\n  );\n};\n\nexport default jenkins;\n", "/**\n * Provides a wrapper to interact with the plugin manager & update center\n */\nimport jenkins from \"../util/jenkins\";\n\n//Get plugin info (plugins + recommended plugin list) from update centers.\nvar plugins;\n\nvar pluginManager = {};\n\npluginManager.initialPluginList = function (handler) {\n  jenkins.get(\n    \"/setupWizard/platformPluginList\",\n    function (response) {\n      if (response.status !== \"ok\") {\n        handler.call({ isError: true, data: response.data });\n        return;\n      }\n\n      handler.call({ isError: false }, response.data);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, errorMessage: errorThrown });\n      },\n    },\n  );\n};\n\n// Call this to initialize the plugin list\npluginManager.init = function (handler) {\n  pluginManager.initialPluginList(function (initialPluginCategories) {\n    plugins = {};\n    plugins.names = [];\n    plugins.recommendedPlugins = [];\n    plugins.availablePlugins = initialPluginCategories;\n    for (var i = 0; i < initialPluginCategories.length; i++) {\n      var pluginCategory = initialPluginCategories[i];\n      var categoryPlugins = pluginCategory.plugins;\n      for (var ii = 0; ii < categoryPlugins.length; ii++) {\n        var plugin = categoryPlugins[ii];\n        var pluginName = plugin.name;\n        if (plugins.names.indexOf(pluginName) === -1) {\n          plugins.names.push(pluginName);\n          if (plugin.suggested) {\n            plugins.recommendedPlugins.push(pluginName);\n          } else if (pluginCategory.category === \"Languages\") {\n            var language =\n              window.navigator.userLanguage || window.navigator.language;\n            var code = language.toLocaleLowerCase();\n            if (pluginName === \"localization-\" + code) {\n              plugins.recommendedPlugins.push(pluginName);\n            }\n          }\n        }\n      }\n    }\n    handler();\n  });\n};\n\n// default 10 seconds for AJAX responses to return before triggering an error condition\nvar pluginManagerErrorTimeoutMillis = 10 * 1000;\n\n/**\n * Get the curated list of plugins to be offered in the wizard.\n * @returns The curated list of plugins to be offered in the wizard.\n */\npluginManager.plugins = function () {\n  return plugins.availablePlugins;\n};\n\n/**\n * Get the curated list of plugins to be offered in the wizard by name only.\n * @returns The curated list of plugins to be offered in the wizard by name only.\n */\npluginManager.pluginNames = function () {\n  return plugins.names;\n};\n\n/**\n * Get the subset of plugins (subset of the plugin list) that are recommended by default.\n * <p>\n * The user can easily change this selection.\n * @returns The subset of plugins (subset of the plugin list) that are recommended by default.\n */\npluginManager.recommendedPluginNames = function () {\n  return plugins.recommendedPlugins.slice(); // copy this\n};\n\n/**\n * Call this function to install plugins, will pass a correlationId to the complete callback which\n * may be used to restrict further calls getting plugin lists. Note: do not use the correlation id.\n * If handler is called with this.isError, there will be a corresponding this.errorMessage indicating\n * the failure reason\n */\npluginManager.installPlugins = function (plugins, handler) {\n  jenkins.post(\n    \"/pluginManager/installPlugins\",\n    { dynamicLoad: true, plugins: plugins },\n    function (response) {\n      if (response.status !== \"ok\") {\n        handler.call({ isError: true, errorMessage: response.message });\n        return;\n      }\n\n      handler.call({ isError: false }, response.data.correlationId);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, errorMessage: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Accepts 1 or 2 arguments, if argument 2 is not provided all installing plugins will be passed\n * to the handler function. If argument 2 is non-null, it will be treated as a correlationId, which\n * must be retrieved from a prior installPlugins call.\n */\npluginManager.installStatus = function (handler, correlationId) {\n  var url = \"/updateCenter/installStatus\";\n  if (correlationId !== undefined) {\n    url += \"?correlationId=\" + correlationId;\n  }\n  jenkins.get(\n    url,\n    function (response) {\n      if (response.status !== \"ok\") {\n        handler.call({ isError: true, errorMessage: response.message });\n        return;\n      }\n\n      handler.call({ isError: false }, response.data);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, errorMessage: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Provides a list of the available plugins, some useful properties is:\n * [\n * \t{ name, title, excerpt, dependencies[], ... },\n *  ...\n * ]\n */\npluginManager.availablePlugins = function (handler) {\n  jenkins.get(\n    \"/pluginManager/plugins\",\n    function (response) {\n      if (response.status !== \"ok\") {\n        handler.call({ isError: true, errorMessage: response.message });\n        return;\n      }\n\n      handler.call({ isError: false }, response.data);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, errorMessage: errorThrown });\n      },\n    },\n  );\n};\n\npluginManager.availablePluginsSearch = function (query, limit, handler) {\n  jenkins.get(\n    \"/pluginManager/pluginsSearch?query=\" + query + \"&limit=\" + limit,\n    function (response) {\n      if (response.status !== \"ok\") {\n        handler.call({ isError: true, errorMessage: response.message });\n        return;\n      }\n\n      handler.call({ isError: false }, response.data);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, errorMessage: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Accepts 1 or 2 arguments, if argument 2 is not provided all installing plugins will be passed\n * to the handler function. If argument 2 is non-null, it will be treated as a correlationId, which\n * must be retrieved from a prior installPlugins call.\n */\npluginManager.incompleteInstallStatus = function (handler, correlationId) {\n  var url = \"/updateCenter/incompleteInstallStatus\";\n  if (correlationId !== undefined) {\n    url += \"?correlationId=\" + correlationId;\n  }\n  jenkins.get(\n    url,\n    function (response) {\n      if (response.status !== \"ok\") {\n        handler.call({ isError: true, errorMessage: response.message });\n        return;\n      }\n\n      handler.call({ isError: false }, response.data);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, errorMessage: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Call this to complete the installation without installing anything\n */\npluginManager.completeInstall = function (handler) {\n  jenkins.post(\n    \"/setupWizard/completeInstall\",\n    {},\n    function () {\n      handler.call({ isError: false });\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, message: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Indicates there is a restart required to complete plugin installations\n */\npluginManager.getRestartStatus = function (handler) {\n  jenkins.get(\n    \"/setupWizard/restartStatus\",\n    function (response) {\n      handler.call({ isError: false }, response.data);\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, message: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Skip failed plugins, continue\n */\npluginManager.installPluginsDone = function (handler) {\n  jenkins.post(\n    \"/pluginManager/installPluginsDone\",\n    {},\n    function () {\n      handler();\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, message: errorThrown });\n      },\n    },\n  );\n};\n\n/**\n * Restart Jenkins\n */\npluginManager.restartJenkins = function (handler) {\n  jenkins.post(\n    \"/updateCenter/safeRestart\",\n    {},\n    function () {\n      handler.call({ isError: false });\n    },\n    {\n      timeout: pluginManagerErrorTimeoutMillis,\n      error: function (xhr, textStatus, errorThrown) {\n        handler.call({ isError: true, message: errorThrown });\n      },\n    },\n  );\n};\n\nexport default pluginManager;\n", "import debounce from \"lodash/debounce\";\n\nimport pluginManagerAvailable from \"./templates/plugin-manager/available.hbs\";\nimport pluginManager from \"./api/pluginManager\";\n\nfunction applyFilter(searchQuery) {\n  // debounce reduces number of server side calls while typing\n  pluginManager.availablePluginsSearch(\n    searchQuery.toLowerCase().trim(),\n    50,\n    function (plugins) {\n      var pluginsTable = document.getElementById(\"plugins\");\n      var tbody = pluginsTable.querySelector(\"tbody\");\n      var admin = pluginsTable.dataset.hasadmin === \"true\";\n      var selectedPlugins = [];\n\n      var filterInput = document.getElementById(\"filter-box\");\n      filterInput.parentElement.classList.remove(\"jenkins-search--loading\");\n\n      function clearOldResults() {\n        if (!admin) {\n          tbody.innerHTML = \"\";\n        } else {\n          var rows = tbody.querySelectorAll(\"tr\");\n          if (rows) {\n            selectedPlugins = [];\n            rows.forEach(function (row) {\n              var input = row.querySelector(\"input\");\n              if (input.checked === true) {\n                var pluginName = input.name.split(\".\")[1];\n                selectedPlugins.push(pluginName);\n              } else {\n                row.remove();\n              }\n            });\n          }\n        }\n      }\n\n      clearOldResults();\n      var rows = pluginManagerAvailable({\n        plugins: plugins.filter(\n          (plugin) => selectedPlugins.indexOf(plugin.name) === -1,\n        ),\n        admin,\n      });\n\n      tbody.insertAdjacentHTML(\"beforeend\", rows);\n\n      updateInstallButtonState();\n    },\n  );\n}\n\nvar handleFilter = function (e) {\n  applyFilter(e.target.value);\n};\n\nvar debouncedFilter = debounce(handleFilter, 150);\n\ndocument.addEventListener(\"DOMContentLoaded\", function () {\n  var filterInput = document.getElementById(\"filter-box\");\n  filterInput.addEventListener(\"input\", function (e) {\n    debouncedFilter(e);\n    filterInput.parentElement.classList.add(\"jenkins-search--loading\");\n  });\n\n  applyFilter(filterInput.value);\n\n  setTimeout(function () {\n    layoutUpdateCallback.call();\n  }, 350);\n\n  // Show update center error if element exists\n  const updateCenterError = document.querySelector(\"#update-center-error\");\n  if (updateCenterError) {\n    notificationBar.show(\n      updateCenterError.content.textContent,\n      notificationBar.ERROR,\n    );\n  }\n});\n\nfunction updateInstallButtonState() {\n  // Enable/disable the 'Install' button depending on if any plugins are checked\n  const anyCheckboxesSelected = () => {\n    return (\n      document.querySelectorAll(\"input[type='checkbox']:checked\").length > 0\n    );\n  };\n  const installButton = document.querySelector(\"#button-install\");\n  const installAfterRestartButton = document.querySelector(\n    \"#button-install-after-restart\",\n  );\n  if (!anyCheckboxesSelected()) {\n    installButton.disabled = true;\n    installAfterRestartButton.disabled = true;\n  }\n  const checkboxes = document.querySelectorAll(\"input[type='checkbox']\");\n  checkboxes.forEach((checkbox) => {\n    checkbox.addEventListener(\"click\", () => {\n      setTimeout(() => {\n        installButton.disabled = !anyCheckboxesSelected();\n        installAfterRestartButton.disabled = !anyCheckboxesSelected();\n      });\n    });\n  });\n}\n", "var Handlebars = require(\"../../../../../node_modules/handlebars/runtime.js\");\nfunction __default(obj) { return obj && (obj.__esModule ? obj[\"default\"] : obj); }\nmodule.exports = (Handlebars[\"default\"] || Handlebars).template({\"1\":function(container,depth0,helpers,partials,data) {\n    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"    <tr data-plugin-id=\\\"\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"name\") : depth0), depth0))\n    + \"\\\" data-plugin-version=\\\"\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"version\") : depth0), depth0))\n    + \"\\\">\\n\"\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,((stack1 = (data && lookupProperty(data,\"root\"))) && lookupProperty(stack1,\"admin\")),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(2, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":3,\"column\":8},\"end\":{\"line\":10,\"column\":15}}})) != null ? stack1 : \"\")\n    + \"        <td>\\n            <div>\\n                <a href=\\\"\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"wiki\") : depth0), depth0))\n    + \"\\\" class=\\\"jenkins-table__link\\\" target=\\\"_blank\\\" rel=\\\"noopener noreferrer\\\">\\n                    \"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"displayName\") : depth0), depth0))\n    + \"\\n                    <span class=\\\"jenkins-visually-hidden\\\">Version</span>\\n                    <span class=\\\"jenkins-label jenkins-label--tertiary\\\" style=\\\"margin-left: 1ch;\\\">\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"version\") : depth0), depth0))\n    + \"</span>\\n                </a>\\n            </div>\\n\"\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"categories\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(4, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":19,\"column\":12},\"end\":{\"line\":27,\"column\":19}}})) != null ? stack1 : \"\")\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"excerpt\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(7, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":28,\"column\":12},\"end\":{\"line\":32,\"column\":19}}})) != null ? stack1 : \"\")\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"newerCoreRequired\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(9, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":33,\"column\":12},\"end\":{\"line\":37,\"column\":19}}})) != null ? stack1 : \"\")\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"unresolvedSecurityWarnings\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(11, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":38,\"column\":12},\"end\":{\"line\":51,\"column\":19}}})) != null ? stack1 : \"\")\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"deprecated\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(14, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":52,\"column\":12},\"end\":{\"line\":56,\"column\":19}}})) != null ? stack1 : \"\")\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"adoptMe\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(16, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":57,\"column\":12},\"end\":{\"line\":61,\"column\":19}}})) != null ? stack1 : \"\")\n    + \"        </td>\\n\"\n    + ((stack1 = lookupProperty(helpers,\"if\").call(alias3,(depth0 != null ? lookupProperty(depth0,\"releaseTimestamp\") : depth0),{\"name\":\"if\",\"hash\":{},\"fn\":container.program(18, data, 0),\"inverse\":container.program(20, data, 0),\"data\":data,\"loc\":{\"start\":{\"line\":63,\"column\":8},\"end\":{\"line\":71,\"column\":15}}})) != null ? stack1 : \"\")\n    + \"    </tr>\\n\";\n},\"2\":function(container,depth0,helpers,partials,data) {\n    var alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"            <td class=\\\"jenkins-table__cell--checkbox\\\">\\n                <span class=\\\"jenkins-checkbox\\\">\\n                    <input type=\\\"checkbox\\\" name=\\\"plugin.\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"name\") : depth0), depth0))\n    + \".\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"sourceId\") : depth0), depth0))\n    + \"\\\" id=\\\"plugin.\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"name\") : depth0), depth0))\n    + \".\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"sourceId\") : depth0), depth0))\n    + \"\\\">\\n                    <label for=\\\"plugin.\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"name\") : depth0), depth0))\n    + \".\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"sourceId\") : depth0), depth0))\n    + \"\\\"></label>\\n                </span>\\n            </td>\\n\";\n},\"4\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                <div class=\\\"app-plugin-manager__categories\\\">\\n\"\n    + ((stack1 = lookupProperty(helpers,\"each\").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,\"categories\") : depth0),{\"name\":\"each\",\"hash\":{},\"fn\":container.program(5, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":21,\"column\":20},\"end\":{\"line\":25,\"column\":29}}})) != null ? stack1 : \"\")\n    + \"                </div>\\n\";\n},\"5\":function(container,depth0,helpers,partials,data) {\n    var alias1=container.lambda, alias2=container.escapeExpression;\n\n  return \"                        <a href=\\\"?filter=\"\n    + alias2(alias1(depth0, depth0))\n    + \"\\\" class=\\\"jenkins-table__link jenkins-table__badge\\\">\\n                        \"\n    + alias2(alias1(depth0, depth0))\n    + \"\\n                        </a>\\n\";\n},\"7\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                <div class=\\\"except\\\">\\n                    \"\n    + ((stack1 = container.lambda((depth0 != null ? lookupProperty(depth0,\"excerpt\") : depth0), depth0)) != null ? stack1 : \"\")\n    + \"\\n                </div>\\n\";\n},\"9\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                <div class=\\\"jenkins-alert jenkins-alert-danger\\\">\\n                    \"\n    + ((stack1 = container.lambda((depth0 != null ? lookupProperty(depth0,\"newerCoreRequired\") : depth0), depth0)) != null ? stack1 : \"\")\n    + \"\\n                </div>\\n\";\n},\"11\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                <div class=\\\"jenkins-alert jenkins-alert-danger\\\">\\n                    \"\n    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,\"unresolvedSecurityWarnings\") : depth0)) != null ? lookupProperty(stack1,\"text\") : stack1), depth0))\n    + \"\\n                    <ul>\\n\"\n    + ((stack1 = lookupProperty(helpers,\"each\").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,\"unresolvedSecurityWarnings\") : depth0)) != null ? lookupProperty(stack1,\"warnings\") : stack1),{\"name\":\"each\",\"hash\":{},\"fn\":container.program(12, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":42,\"column\":24},\"end\":{\"line\":48,\"column\":33}}})) != null ? stack1 : \"\")\n    + \"                    </ul>\\n                </div>\\n\";\n},\"12\":function(container,depth0,helpers,partials,data) {\n    var alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                            <li>\\n                                <a href=\\\"\"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"url\") : depth0), depth0))\n    + \"\\\" target=\\\"_blank\\\" rel=\\\"noopener noreferrer\\\">\\n                                    \"\n    + alias2(alias1((depth0 != null ? lookupProperty(depth0,\"message\") : depth0), depth0))\n    + \"\\n                                </a>\\n                            </li>\\n\";\n},\"14\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                <div class=\\\"jenkins-alert jenkins-alert-warning\\\">\\n                    \"\n    + ((stack1 = container.lambda((depth0 != null ? lookupProperty(depth0,\"deprecated\") : depth0), depth0)) != null ? stack1 : \"\")\n    + \"\\n                </div>\\n\";\n},\"16\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"                <div class=\\\"jenkins-alert jenkins-alert-warning\\\">\\n                    \"\n    + ((stack1 = container.lambda((depth0 != null ? lookupProperty(depth0,\"adoptMe\") : depth0), depth0)) != null ? stack1 : \"\")\n    + \"\\n                </div>\\n\";\n},\"18\":function(container,depth0,helpers,partials,data) {\n    var stack1, alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return \"            <td style=\\\"width: 25%\\\" data=\\\"\"\n    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,\"releaseTimestamp\") : depth0)) != null ? lookupProperty(stack1,\"iso8601\") : stack1), depth0))\n    + \"\\\">\\n                <time datetime=\\\"\"\n    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,\"releaseTimestamp\") : depth0)) != null ? lookupProperty(stack1,\"iso8601\") : stack1), depth0))\n    + \"\\\">\\n                    \"\n    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,\"releaseTimestamp\") : depth0)) != null ? lookupProperty(stack1,\"displayValue\") : stack1), depth0))\n    + \"\\n                </time>\\n            </td>\\n\";\n},\"20\":function(container,depth0,helpers,partials,data) {\n    return \"            <td style=\\\"width: 25%\\\"></td>\\n\";\n},\"compiler\":[8,\">= 4.3.0\"],\"main\":function(container,depth0,helpers,partials,data) {\n    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    };\n\n  return ((stack1 = lookupProperty(helpers,\"each\").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,\"plugins\") : depth0),{\"name\":\"each\",\"hash\":{},\"fn\":container.program(1, data, 0),\"inverse\":container.noop,\"data\":data,\"loc\":{\"start\":{\"line\":1,\"column\":0},\"end\":{\"line\":73,\"column\":9}}})) != null ? stack1 : \"\");\n},\"useData\":true});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "__webpack_require__.j = 187;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t187: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkjenkins_ui\"] = self[\"webpackChunkjenkins_ui\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [96], function() { return __webpack_require__(268); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["$", "wh", "Handlebars", "debug", "jenkins", "baseUrl", "u", "attr", "goTo", "url", "getWindow", "location", "replace", "get", "success", "options", "console", "log", "args", "type", "cache", "dataType", "Object", "extend", "ajax", "post", "data", "headers", "wnd", "crumb", "fieldName", "value", "formBody", "JSON", "stringify", "contentType", "initHandlebars", "loadTranslations", "bundleName", "handler", "onError", "res", "status", "message", "translations", "Proxy", "target", "property", "testConnectivity", "siteId", "response", "uncheckedStatuses", "indexOf", "updatesite", "internet", "setTimeout", "error", "xhr", "textStatus", "errorThrown", "call", "isError", "errorMessage", "$form", "top", "document", "find", "each", "windowFrame", "contentWindow", "$f", "contents", "buildFormPost", "form", "buildFormTree", "serialize", "param", "Submit", "json", "val", "getFormCrumb", "staplerPost", "postBody", "processData", "plugins", "pluginManager", "initialPluginList", "timeout", "pluginManagerErrorTimeoutMillis", "init", "initialPluginCategories", "names", "recommendedPlugins", "availablePlugins", "i", "length", "pluginCategory", "categoryPlugins", "ii", "plugin", "pluginName", "name", "push", "suggested", "category", "language", "window", "navigator", "userLanguage", "code", "toLocaleLowerCase", "pluginNames", "recommendedPluginNames", "slice", "installPlugins", "dynamicLoad", "correlationId", "installStatus", "undefined", "availablePluginsSearch", "query", "limit", "incompleteInstallStatus", "completeInstall", "getRestartStatus", "installPluginsDone", "<PERSON><PERSON><PERSON><PERSON>", "debounce", "pluginManagerAvailable", "applyFilter", "searchQuery", "toLowerCase", "trim", "pluginsTable", "getElementById", "tbody", "querySelector", "admin", "dataset", "<PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "filterInput", "parentElement", "classList", "remove", "clearOldResults", "innerHTML", "rows", "querySelectorAll", "for<PERSON>ach", "row", "input", "checked", "split", "filter", "insertAdjacentHTML", "updateInstallButtonState", "handleFilter", "e", "debounced<PERSON><PERSON><PERSON>", "addEventListener", "add", "layoutUpdateCallback", "updateCenterError", "notificationBar", "show", "content", "textContent", "ERROR", "anyCheckboxesSelected", "installButton", "installAfterRestartButton", "disabled", "checkboxes", "checkbox"], "sourceRoot": ""}