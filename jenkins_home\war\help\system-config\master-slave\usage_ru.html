﻿<div>
  Контролирует планирование сборок на сервере.

  <dl>
    <dt><b>Загружать этот узел настолько, насколько возможно</b></dt>
    <dd>
      Это обычное поведение по умолчанию.
      <br />
      В этом режиме Jenkins свободно распоряжается ресурсами узла. Как только
      возникает необходимость в сборке, которая может быть выполнена этим узлом,
      Jenkins его использует.
    </dd>

    <dt><b>Собирать только проекты с метками, совпадающими с этим узлом</b></dt>
    <dd>
      В этом режиме Jenkins будет выполнять на этом узле сборки тех проектов,
      которые ограничены для определённых узлов с помощью выражения метки, и имя
      и/или метки узла совпадают с этим выражением.
      <p>
        Это позволяет узлу быть зарезервированным для определенных групп
        проектов. Например, если у вас есть проекты, выполняющие тесты
        производительности используя Jenkins, вам может понадобиться запускать
        их на специально настроенной для этого машине, и не давать другим
        проектам использовать эту машину. Для этого вы можете ограничить, где
        могут запускаться проекты с тестами, назначив им метку, совпадающую с
        этой машиной.
        <br />
        Более того, если вы установите
        <i>количество исполнителей</i>
        в 1, вы можете гарантировать, что только один процесс теста
        производительности будет запускаться на этой машине в каждый момент
        времени, и никакая другая сборка ему не помешает.
      </p>
    </dd>
  </dl>
</div>
