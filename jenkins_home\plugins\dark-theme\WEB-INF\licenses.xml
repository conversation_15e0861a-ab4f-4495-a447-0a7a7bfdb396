<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='dark-theme' version='524.vd675b_22b_30cb_'><l:dependency name='Dark Theme' groupId='io.jenkins.plugins' artifactId='dark-theme' version='524.vd675b_22b_30cb_' url='https://github.com/jenkinsci/dark-theme-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JSON Api Plugin' groupId='io.jenkins.plugins' artifactId='json-api' version='20241224-119.va_dca_a_b_ea_7da_5' url='https://github.com/jenkinsci/json-api-plugin'><l:description>The <PERSON> Plugins Parent POM Project</l:description><l:license name='MIT No Attribution License' url='https://opensource.org/license/mit-0/'/></l:dependency><l:dependency name='SnakeYAML' groupId='org.yaml' artifactId='snakeyaml' version='2.3' url='https://bitbucket.org/snakeyaml/snakeyaml'><l:description>YAML 1.1 parser and emitter for Java</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JSON in Java' groupId='org.json' artifactId='json' version='20241224' url='https://github.com/douglascrockford/JSON-java'><l:description>JSON is a light-weight, language independent, data interchange format.
        See http://www.JSON.org/

        The files in this package implement JSON encoders/decoders in Java.
        It also includes the capability to convert between JSON and XML, HTTP
        headers, Cookies, and CDL.

        This is a reference implementation. There are a large number of JSON packages
        in Java. Perhaps someday the Java community will standardize on one. Until
        then, choose carefully.</l:description><l:license name='Public Domain' url='https://github.com/stleary/JSON-java/blob/master/LICENSE'/></l:dependency><l:dependency name='SnakeYAML API Plugin' groupId='io.jenkins.plugins' artifactId='snakeyaml-api' version='2.3-123.v13484c65210a_' url='https://github.com/jenkinsci/snakeyaml-api-plugin'><l:description>This plugin packages stock SnakeYAML library</l:description><l:license name='The MIT License (MIT)' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>