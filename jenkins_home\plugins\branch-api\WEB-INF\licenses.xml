<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='branch-api' version='2.1217.v43d8b_b_d8b_2c7'><l:dependency name='Branch API Plugin' groupId='org.jenkins-ci.plugins' artifactId='branch-api' version='2.1217.v43d8b_b_d8b_2c7' url='https://github.com/jenkinsci/branch-api-plugin'><l:description>This plugin provides an API for multiple branch based projects.</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>