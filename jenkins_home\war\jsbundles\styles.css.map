{"version": 3, "file": "styles.css", "mappings": "AA8BA,iDAGE,qMAIA,wFAEA,qBAAsB,CACtB,uBAAwB,CACxB,sBAAuB,CACvB,yBAA0B,CAC1B,sBAAuB,CAGvB,sBAAuB,CACvB,yBAA0B,CAG1B,yBAA0B,CAC1B,oBAAgC,CAChC,qBAAsB,CACtB,mBAAoB,CAGpB,4CAA6C,CAC7C,wCAAyC,CACzC,yEAA4E,CAG5E,mCAAoC,CACpC,oCAAqC,CAGrC,wCAAyC,CACzC,yCAA0C,CAG1C,yBAA0B,CAG1B,mCAAoC,CACpC,gCAAiC,CACjC,gCAAiC,CACjC,sCAAuC,CACvC,uCAAwC,CAGxC,6EAKA,wFAOA,+CAAgD,CAChD,iFAKA,oFAKA,mCAAoC,CACpC,oEAAuE,CACvE,uEAA0E,CAC1E,yFAKA,iFAKA,oFAKA,4CAA6C,CAC7C,8EAKA,iFAOA,uCAAwC,CAGxC,4BAA6B,CAC7B,iGAKA,gFAOA,+EAIA,+FAEA,gFAcA,sEAAyE,CACzE,2CAA4C,CAC5C,yCAA0C,CAC1C,yCAA0C,CAC1C,6BAA8B,CAC9B,mCAAoC,CAGpC,uCAAwC,CACxC,gDAAiD,CACjD,qCAAsC,CACtC,kCAAmC,CACnC,yCAA0C,CAC1C,8CAA+C,CAC/C,iCAAkC,CAClC,gCAAiC,CACjC,8CAA+C,CAC/C,2CAA4C,CAG5C,gCAAiC,CACjC,sCAAuC,CACvC,qCAAsC,CACtC,sCAAuC,CACvC,2BAA4B,CAC5B,uCAAwC,CACxC,wCAAyC,CACzC,0CAA2C,CAG3C,iEAAkE,CAClE,wJAcA,8DAA+D,CAC/D,iCAAkC,CAClC,yJAKA,+DAAgE,CAChE,yJASA,yJAKA,mCAAoC,CACpC,gDAAiD,CACjD,6CAA8C,CAC9C,+CAAgD,CAChD,gCAAiC,CACjC,4CAA6C,CAC7C,6CAA8C,CAC9C,+CAAgD,CAGhD,uBAAwB,CACxB,0CAA2C,CAC3C,kCAAmC,CACnC,4CAA6C,CAC7C,8BAA+B,CAC/B,qCAAsC,CACtC,mCAAoC,CACpC,sBAAwB,CACxB,+BAAiC,CAGjC,mCAAoC,CACpC,oCAAqC,CACrC,qCAAsC,CACtC,sEAAyE,CACzE,6EAGA,8EAGA,+CAAgD,CAOhD,mEAAsE,CACtE,kCAAmC,CACnC,wFAKA,6CAAgD,CAChD,+CAAgD,CAChD,6CAAgD,CAChD,gDAAiD,CACjD,qCAAuC,CACvC,kDAAmD,CACnD,yBAA6C,CAG7C,wBAAyB,CACzB,yCAA0C,CAC1C,sCAAuC,CAGvC,0BAA2B,CAC3B,oFAKA,8EAKA,oFAKA,wCAAyC,CACzC,sCAAwC,CACxC,8CAAgD,CAChD,6CAA+C,CAS/C,gDAAiD,CACjD,oCAAqC,CACrC,mCAAoC,CACpC,0CAA2C,CAC3C,8DAA+D,CAC/D,yCAA0C,CAC1C,6BAA8B,CAC9B,2DAA8D,CAU9D,gCAAiC,CACjC,sDAA0D,CAG1D,4DAA6D,CAG7D,uEAA0E,CAC1E,+EAGA,gFAGA,6EAGA,yCAA0C,CAG1C,0EAA6E,CAC7E,6EAGA,0EAA6E,CAG7E,6BAA8B,CAC9B,sBAAuB,CACvB,mBAAoB,CACpB,uBAAwB,CAItB,mCAGE,yCACA,wCAJF,mCAGE,yCACA,wCAJF,mCAGE,yCACA,wCAJF,iCAGE,uCACA,sCAJF,oCAGE,0CACA,yCAJF,oCAGE,0CACA,yCAJF,kCAGE,wCACA,uCAJF,oCAGE,0CACA,yCAJF,8BAGE,oCACA,mCAJF,+BAGE,sCACA,oCAJF,mCAGE,yCACA,wCAJF,gEASA,sPAjOF,6DAhIF,iDAiII,0BAA2B,CAA3B,CAGF,8BApIF,iDAqII,sCAAuC,CACvC,wCAAyC,CACzC,gDAAiD,CAAjD,CAwCF,uEACE,wFAqBF,uEACE,8CAAoD,CAyCtD,8BA/OF,iDAgPI,qCAAsC,CAAtC,CA8CF,oCA9RF,iDA+RI,yBAA0B,CAC1B,0BAA2B,CAC3B,kCAAmC,CACnC,iCAAkC,CAAlC,CAYF,8BA9SF,iDA+SI,0CAA4C,CAC5C,gDAAkD,CAClD,qFC1UF,6BACE,0BAEA,kCAEF,uBACE,oBAEA,4BAEF,4BACE,yBAEA,iCAbF,8BACE,2BAEA,mCAEF,wBACE,qBAEA,6BAEF,6BACE,0BAEA,kCAbF,6BACE,0BAEA,kCAEF,uBACE,oBAEA,4BAEF,4BACE,yBAEA,iCAbF,8BACE,2BAEA,mCAEF,wBACE,qBAEA,6BAEF,6BACE,0BAEA,kCAbF,+BACE,4BAEA,oCAEF,yBACE,sBAEA,8BAEF,8BACE,2BAEA,mCAbF,+BACE,4BAEA,oCAEF,yBACE,sBAEA,8BAEF,8BACE,2BAEA,mCAbF,6BACE,0BAEA,kCAEF,uBACE,oBAEA,4BAEF,4BACE,yBAEA,iCAbF,+BACE,4BAEA,oCAEF,yBACE,sBAEA,8BAEF,8BACE,2BAEA,mCAbF,4BACE,yBAEA,iCAEF,sBACE,mBAEA,2BAEF,2BACE,wBAEA,gCAbF,+BACE,4BAEA,oCAEF,yBACE,sBAEA,8BAEF,8BACE,2BAEA,mCAbF,6BACE,0BAEA,kCAEF,uBACE,oBAEA,4BAEF,4BACE,yBAEA,iCAbF,8BACE,2BAEA,mCAEF,wBACE,qBAEA,6BAEF,6BACE,0BAEA,kCAbF,8BACE,2BAEA,mCAEF,wBACE,qBAEA,6BAEF,6BACE,0BAEA,kCAKF,yBACE,oBAEA,4BAHF,uBACE,qBAEA,6BAHF,wBACE,mBAEA,2BAHF,0BACE,sBAEA,8BAHF,0BACE,qBAEA,6BAHF,8BACE,mBAEA,2BAHF,wBACE,qBAEA,6BAHF,yBACE,mBAEA,2BAHF,uBACE,oBAEA,4BAKJ,QACE,WAGF,SACE,iBACA,gBAGF,WACE,mBACA,gBC1CF,KAEE,sBADA,YAEA,0CACA,wBAGF,KAKE,mCAFA,aACA,sBAHA,SACA,gBAGA,CAGF,iBAGE,mBAGF,iBACE,kCADF,YACE,kCCtBF,6BACE,iBCGF,mBAGE,mBADA,oBAGA,YAJA,mBAGA,kBAEA,WAGF,mBACE,kBAGF,mBACE,cAGF,mBACE,mBAKF,eAGE,oBAFA,aAGA,cAFA,0BAEA,CAIA,wBACE,gCAGE,sBAFA,gBACA,QACA,EAKN,uBACE,WACA,WACA,cAGF,YACE,cAGF,YAEE,qBADA,+BAEA,WAGF,2BACE,uCAAwC,CAoBxC,YAdA,cAcA,CAlBA,qCAHF,2BAII,0BAA2B,CAA3B,CAKF,uCACE,YAGF,4CACE,gBAGF,yBAjBF,2BAkBI,iBAMJ,4BAGE,cADA,OADA,0CAEA,CAOF,8CACE,UAGF,4BACE,8BAGF,wBACE,2BACE,eAGF,4BAEE,kBADA,UACA,CAGF,4BACE,cACA,YAIJ,0BAEE,gBADA,UACA,CAGF,yBACE,4BACE,uEAOJ,0jBA6BE,YADA,aACA,CAGF,sRAcE,WC/KA,qBACE,mBAGF,yBACE,uBAGF,0BACE,wBAGF,4BACE,0BAGF,2BACE,yBAjBF,qBACE,uBAGF,yBACE,2BAGF,0BACE,4BAGF,4BACE,8BAGF,2BACE,6BAjBF,qBACE,uBAGF,yBACE,2BAGF,0BACE,4BAGF,4BACE,8BAGF,2BACE,6BAjBF,qBACE,wBAGF,yBACE,4BAGF,0BACE,6BAGF,4BACE,+BAGF,2BACE,8BAjBF,qBACE,wBAGF,yBACE,4BAGF,0BACE,6BAGF,4BACE,+BAGF,2BACE,8BAjBF,qBACE,sBAGF,yBACE,0BAGF,0BACE,2BAGF,4BACE,6BAGF,2BACE,4BAjBF,qBACE,wBAGF,yBACE,4BAGF,0BACE,6BAGF,4BACE,+BAGF,2BACE,8BAIJ,wBACE,cAIA,sBACE,oBAGF,0BACE,wBAGF,2BACE,yBAGF,6BACE,2BAGF,4BACE,0BAjBF,sBACE,wBAGF,0BACE,4BAGF,2BACE,6BAGF,6BACE,+BAGF,4BACE,8BAjBF,sBACE,wBAGF,0BACE,4BAGF,2BACE,6BAGF,6BACE,+BAGF,4BACE,8BAjBF,sBACE,yBAGF,0BACE,6BAGF,2BACE,8BAGF,6BACE,gCAGF,4BACE,+BAjBF,sBACE,yBAGF,0BACE,6BAGF,2BACE,8BAGF,6BACE,gCAGF,4BACE,+BAjBF,sBACE,uBAGF,0BACE,2BAGF,2BACE,4BAGF,6BACE,8BAGF,4BACE,6BAjBF,sBACE,yBAGF,0BACE,6BAGF,2BACE,8BAGF,6BACE,gCAGF,4BACE,+BCtBJ,KACE,SAGF,GACE,mBAGF,GACE,gBAGF,aACE,sBAGF,QACE,kBAGF,iCAEE,sBAGF,iCAEE,kBAGF,uBAEE,mBAGF,YAEE,yBADA,SACA,CAGF,kBAGE,+BAKA,CAGF,gCAHE,8CANA,WADA,gBAIA,qCACA,aAFA,iBAeA,CARF,cAGE,wBAKA,CAGF,cAEE,cADA,oBACA,CAGF,eAEE,cADA,yBACA,CAGF,iBAEE,cADA,oBACA,CAIF,MACE,kBAEA,0BADA,UACA,CAGF,YACE,WAGF,WACE,aAGF,iBAOE,uBADA,sBAEA,WAPA,cAGA,SAFA,kBAOA,gBANA,QAEA,WAIA,CAGF,IAEE,QAAO,CADP,qBACA,CAGF,aAEE,sBADA,UACA,CAGF,UACE,yBAGF,OAGE,mBADA,sBADA,qBAEA,CAGF,IAOE,uCADA,qCADA,8CAGA,uBACA,oCACA,gBACA,iBARA,kCACA,mBAHA,oBAUA,CAEA,UAXA,oBAYE,CAIJ,qBACE,0BAGF,YACE,cAGF,YACE,mBAEA,kBAKE,WAJA,YAEA,cACA,SAFA,iBAGA,CAIJ,mBACE,UAGF,cACE,oCACA,oBACA,mBAGF,cAKE,YAJA,kBACA,mBAEA,sBADA,UAEA,CAGF,iBACE,WAGF,eAGE,qCADA,kBAEA,4CAEA,sBACA,8BACA,2BAHA,YAJA,UAOA,CAGF,qBACE,8BACA,aACA,cAGF,0EAIE,YAAW,CADX,qBACA,CAGF,iBAEE,mBADA,YACA,CAGF,wBACE,OAGF,yBACE,cAGF,cACE,aAGF,wBACE,cACA,kBAGF,mBAGE,uCADA,8CADA,kBAEA,CAEA,uBAGE,6BADA,gBADA,iBAEA,CAIJ,aACE,WAGF,WAEE,YADA,UACA,CAGF,WAEE,YADA,UACA,CAGF,WAEE,YADA,UACA,CAKF,MAOE,0CACA,qCAFA,8CAJA,aAEA,cADA,aAFA,kBAIA,sBAIA,UAEA,wBAAyB,CAEzB,sBACE,aAGF,qBACE,gBAMF,0UAYE,kCAIJ,mBAEE,WADA,gBACA,CAGF,+BAEE,eACA,cAGF,cACE,wBAKF,gBACE,qBAGF,4BACE,YAGF,sCACE,aACA,iBACA,UAEA,yBAA0B,CAE1B,4CACE,gBAGA,uBADA,gBAEA,mBAHA,WAGA,CAEA,8CACE,sBAKN,wFAEE,cAIF,cASE,6BANA,8CACA,uCAFA,wBAIA,gBADA,eAEA,iBANA,kBASA,cAFA,YAEA,CAGF,cAGE,eACA,oCAFA,oBADA,iBAGA,CAEA,qBAIE,6BACA,8CAJA,WAEA,QAGA,UAJA,kBAKA,sCACA,WAIA,2BACE,WAMF,uDACE,WAKN,iBACE,aAIF,iBAEE,mBADA,aAEA,eACA,UAEA,gCACE,oBACA,YAIJ,YACE,gBAGF,mBACE,iBAGF,eACE,UAGF,sCAEE,kCADA,kBACA,CAEA,0CACE,kCAIJ,2BACE,iBAIF,eACE,cAGF,gBACE,WAGF,cACE,cACA,gBAGF,eACE,cAGF,mBACE,cACA,gBAGF,oBAEE,gCACA,gBAFA,iBAEA,CAIF,QACE,iBAGF,4BACE,gCACA,sBAGF,8BACE,iBACA,kBAGF,sBACE,sBAEA,mCACE,qCAIJ,gIAOE,0CAFA,kBADA,kBAEA,qCACA,CAEA,wJAOE,+BACA,kBAHA,SAJA,WAGA,UAFA,kBACA,MAMA,sCAHA,iCAGA,CAGF,wJACE,aAIJ,wBACE,qCAGF,wBAKE,mBAHA,mBAEA,uBADA,oBAGA,gBALA,2BAKA,CAGF,aACE,aAKF,SACE,gBACA,gBAKF,mBAEE,yBADA,yBAIA,WAFA,WACA,WACA,CAGF,8BACE,qDAGF,qBACE,yBAGF,qBACE,yBAGF,uBACE,sBAGF,kCACE,yDAGF,4CACE,sBAKF,wBACE,kCACA,qBAEA,+BAJF,wBAKI,uBAIJ,gBACE,GACE,uBAGF,GACE,yBAIJ,wBACE,GACE,wBAGF,GACE,wBAQJ,MACE,cAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAGF,MACE,gBAKF,qBACE,8CAEA,qCADA,eACA,CAEA,yBAIE,eAAc,CADd,gBADA,qBADA,aAGA,CAEA,uCACE,kBAGF,sCACE,qBAKN,oBACE,8BAGF,wBACE,mBAGF,wBACE,kCAIF,2BACE,oCACA,yBAGF,cACE,2CACA,wCACA,+CAGF,iBACE,oCACA,YAGF,gBACE,YAGF,qBAGE,kCAFA,oCACA,gBAEA,YAGF,eACE,oCACA,kBAIF,2BAKE,4CAEA,wBADA,4BAFA,kBADA,oBAFA,mBACA,gBAKA,CAIF,MACE,aAGF,+BACE,wBASF,yHACE,aAIF,qBAIE,sBAEA,kDAHA,YADA,UAME,CAIJ,qBAIE,sBAEA,kDAHA,YADA,UAME,CAIJ,qBAIE,sBAEA,kDAHA,YADA,UAME,CAIJ,qBAIE,sBAEA,kDAHA,YADA,UAME,CAIJ,uBAGE,YACA,sBAFA,UAEA,CAEA,+BAEE,YADA,UACA,CAQJ,UACE,qBAIA,YAHA,sBAIA,WAGA,uBACA,kBAGF,uBACE,kBACA,WAKF,QACE,8BAIF,gBACE,aAKE,oEAGE,sBACA,kBAFA,oBAEA,CCt2BN,OAKE,wBAHA,oCACA,8BACA,mCACA,CAGF,OACE,oBACA,kBAUF,uCAGE,8BAEA,wBALF,sBAMI,iCAKJ,qBAKE,qCAOF,0CAcE,cADA,gBADA,uCAIA,qCADA,YACA,CAGF,OAEE,iBAGF,OAEE,mBAGF,OAEE,oBAGF,OAEE,eAGF,OAEE,mBAGF,OAEE,kBAGF,qBACE,kCACA,iBAEA,qCADA,YACA,CAIA,yBACE,kCACA,WAIJ,EC3GE,oCADA,gGAGA,8BADA,yBACA,CAEA,OACE,wBAGF,UACE,gCAGF,gBAEE,+BACA,8GAGF,SACE,gCACA,gHAGF,8BDoFF,ECnFI,0BAEA,QACE,+BDoFN,yBAEE,mBADA,oBAEA,2BAEA,6BAGE,kCADA,YADA,UAEA,CEjHJ,gDAIE,uBAGF,wBACE,YACE,wBAGF,iBACE,cAGF,cACE,4BAGF,4BAEE,8BAIJ,8CACE,YACE,wBAGF,iBACE,cAGF,cACE,4BAGF,4BAEE,8BAIJ,+CACE,YACE,wBAGF,iBACE,cAGF,cACE,4BAGF,4BAEE,8BAIJ,yBACE,YACE,wBAGF,iBACE,cAGF,cACE,4BAGF,4BAEE,8BAIJ,wBACE,WACE,wBAIJ,8CACE,WACE,wBAIJ,+CACE,WACE,wBAIJ,yBACE,WACE,wBAIJ,eACE,uBAGF,aACE,eACE,wBAGF,oBACE,cAGF,iBACE,4BAGF,kCAEE,6BAKF,cACE,uBANA,CAUJ,gBACE,uBAIF,yBAGE,WAIA,SAEA,gBADA,UAPA,kBACA,UAQA,mBAEA,SADA,qBAMA,mBAIA,wBADF,qBAEI,wBAKF,wBADF,qBAEI,wBCjLJ,sBAKE,qDACA,8CAJA,8BAEA,qCADA,YAGA,CAEA,oCACE,oCAGF,0BACE,cACA,0BAEA,oHAGE,0BAIJ,sCAEE,+CACA,+CAFA,qCAEA,CAGF,gCAEE,4CACA,4CAFA,kCAEA,CAGF,sCAEE,+CACA,+CAFA,qCAEA,CAEA,0CACE,sCAIJ,oCAEE,8CACA,8CAEA,4EAJA,oCAKE,CAIJ,8BAvDF,sBAwDI,gCCxDJ,iBAEE,mBADA,aAKA,eADA,2BAFA,8BACA,oCAEA,CAEA,wBARF,iBASI,oBACA,uBAGF,2CACE,aAEA,sBAEA,WAAU,CAHV,uBAEA,kBACA,CAGF,4CAEE,mBADA,aAGA,WADA,sBACA,CAEA,4DACE,gBAGF,wBAVF,4CAYI,eADA,uBACA,CAEA,8CAEE,YAAW,CADX,WACA,CAGF,wEAEE,0BADA,UACA,EAIJ,kDACE,aAIJ,yBAIE,2CADA,mCAFA,gBACA,SAGA,UAEA,+DAEE,WAEA,wEAGA,oBAJA,kBAGA,UACA,CAGF,gCACE,6BACA,0GACA,YAEA,qFALF,gCAMI,WAIJ,+BACE,8DACA,0GAIJ,wCAGE,iBADA,QACA,CAGF,2BACE,kCACA,iBAIJ,uBAGE,mBAFA,aACA,6BAGA,SADA,gCAEA,iBACA,gBACA,uBAEA,2BAEE,sBADA,oBACA,CAGF,2BAEE,gBADA,cACA,CAIJ,gCAOE,YACA,4CANA,gBAOA,4CAIA,YAGF,sBAEE,mCADA,kBAEA,UAEA,6BAIE,6BAHA,WAEA,QAEA,UAHA,kBAIA,WAGF,4BAME,0EALA,WAOA,YAJA,OAOA,4QAJA,eAEA,UASA,oBAhBA,kBAGA,QAFA,UAOA,qCAQA,CAIA,mDACE,8DAEA,0DACE,sBAEA,qFAHF,0DAII,qBAIJ,yDACE,oBCpLN,qBAIE,yEAHA,oBAKA,qCAJA,yGAGA,mDACA,CAEA,8BARF,qBASI,iCCVN,eAEE,mBAQA,4EANA,oBAHA,oBAIA,mBACA,gBAHA,uBAIA,gBACA,eACA,eACA,CAMA,6BAKE,gEAJA,2DAEA,qEADA,6BAEA,oCACA,CCnBJ,qBAKE,mBAEA,8DAEA,6CALA,aAIA,gBAFA,mCALA,gBACA,MACA,WAMA,CAEA,2BACE,iBACA,qBAEA,6BACE,cAGF,gCAOE,8BACA,oBAEA,kEAPA,mBAEA,wBAHA,oBAIA,gBAFA,uBAHA,iBAqBE,CAZF,kCN6BJ,6DAMA,uBAEA,YAJA,8CACA,eM3BM,kBADA,oBAEA,SAGA,yBNyBN,aM3BM,UNsBN,+BMnBM,sCNkBN,SMlBM,CN2BN,iFAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,yCACE,wCACA,qCAGF,wCACE,mCAGF,gDACE,aAQE,0RACE,+CAIJ,wDACE,uBACA,UAEA,+DACE,gDAGF,8DACE,sDAKF,qEACE,mDACA,oBAOF,sIACE,yBMnFA,iFAEE,qBAGF,yKAIE,wBAKF,mBADF,4CAEI,6BAGF,oGAEE,4BAMN,2EAIE,YACA,qBAHA,kBACA,UAEA,CAEA,uFAKE,uCAJA,WAEA,QAGA,8mBACA,WALA,kBAEA,qCAGA,CAKF,mDACE,aAIJ,qCACE,eAGE,iDACE,UACA,wBAKF,kDAEE,WADA,uCACA,CAQF,6MACE,6BAKJ,4CAIE,uBAHA,WAEA,iBADA,iBAEA,CAMR,+BAYE,uBANA,YAEA,eANA,qBAEA,YAGA,aAEA,UACA,oBATA,kBAIA,QAQA,QAFA,sCARA,UAUA,CAGE,2CACE,oBAKF,4CAEE,qBADA,yBACA,CAKJ,sCACE,WAEA,YADA,iBACA,CAGF,qCASE,6BALA,SAHA,WASA,wmBACA,kDACA,oDALA,UALA,kBAGA,QAFA,MAKA,sCAFA,UAMA,CAIJ,YAGE,4BAFA,kBACA,+CACA,CAEA,mBACE,qCAEE,sBAGF,2CACE,YAEA,iDACE,WAKN,oBACE,yBACE,wBAGF,mCACE,8BAIA,yBACE,yDACA,sBAMF,kFACE,8BACA,6BAGF,kGAEE,YAGF,kGACE,mBACA,YAEA,8GACE,YAOV,mBACE,UAEA,mBAHF,mBAII,4BAEA,mDAEE,sBAGF,kDACE,YAEA,wDACE,WAKN,oBApBF,mBAqBI,yBAEA,mDAME,kBAJA,WAEA,iBACA,UAFA,kBAIA,sCAGF,0BAEE,6BADA,UACA,CAGF,yBAEE,6BADA,UACA,CAGF,yBACE,YAEA,qCACE,+DAEE,0BA7BR,oBAiCI,gCACE,UAGF,+BACE,YAIA,8DACE,YChTV,gBACE,0CAA2C,CAC3C,wDAAyD,CACzD,0DAA2D,CAC3D,wDAAyD,CAKzD,mBPiDA,6DAMA,uBAEA,YAJA,8COhDA,kCPiDA,eOvDA,oBAKA,8BPgDA,gBO5CA,QAPA,uBACA,SAIA,oBPkDA,aOrDA,mBP6CA,kBAGA,+BO5CA,mBP2CA,SO1CA,CPmDA,6CAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,uBACE,wCACA,qCAGF,sBACE,mCAGF,8BACE,aAQE,kNACE,+CAIJ,sCACE,uBACA,UAEA,6CACE,gDAGF,4CACE,sDAKF,mDACE,mDACA,oBOnGN,oBAEE,gBADA,cACA,CAGF,yBAGE,mBADA,oBADA,UAEA,CAIJ,yBACE,yDAA0D,CAC1D,oEAAuE,CACvE,qEAAwE,CACxE,oEAAuE,CAEvE,6CAKF,8BACE,sDAAyD,CACzD,8DAAiE,CACjE,+DAAkE,CAClE,+DAAkE,CAElE,6BAGF,uCACE,kDAAmD,CACnD,6DAAgE,CAChE,8DAAiE,CACjE,6DAAgE,CAEhE,kCAGF,0BACE,yCAA2C,CASzC,wMACE,yBAMN,qBAEE,mBADA,aAEA,WAEA,6BACE,yBASF,oFACE,iBAGF,wBAnBF,qBAoBI,wBAEA,qCACE,aAMJ,gDAEE,aACA,0BAGA,gBAFA,mBAHA,kBAMA,sCAFA,cAEA,CAEA,oDAME,2BADA,iBAJA,cAEA,UADA,UAEA,oCAEA,CAEA,sDACE,kBAIJ,6GAME,mBACA,oBALA,WAWA,cARA,eAFA,kBAKA,qJACE,CALF,cASA,CAGF,uDAEE,uEADA,mBACA,CAUF,sDACE,kBAIJ,6BACE,qCAEA,sFAKA,mDAAoD,CACpD,oDAAqD,CAGnD,6HAIE,iBADA,UADA,UAEA,CAGF,4DAGE,eADA,UADA,UAEA,CAOF,6DACE,mBAGF,4DACE,iBAMJ,uDACE,qBAOF,mEACE,0BAIJ,oEAEE,sBADA,YACA,CAGF,oDACE,YAMF,0CAEE,eACA,sCAFA,aAEA,CAKE,4GACE,oBAMJ,kFACE,cAON,sBACE,aACA,QAEA,mCAEE,iCADA,6BAPiC,CAWnC,mDAGE,gCADA,4BAbiC,CAYjC,aAZiC,CAgBjC,uDAEE,aADA,WACA,CAKN,kBACE,+DAAkE,CAClE,sEAAyE,CACzE,uEAA0E,CAC1E,oCAAqC,CAKrC,mBP7NA,6DAMA,uBAEA,YAJA,8CO6NA,qBP5NA,eOuNA,oBPzNA,gBO6NA,YAFA,uBPvNA,aARA,kBAGA,+BO6NA,WP9NA,SOgOA,CPvNA,iDAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,yBACE,wCACA,qCAGF,wBACE,mCAGF,gCACE,aAQE,0NACE,+CAIJ,wCACE,uBACA,UAEA,+CACE,gDAGF,8CACE,sDAKF,qDACE,mDACA,oBOuKN,sBAGE,iBADA,aADA,WAEA,CAEA,wBACE,kBClSN,cAIE,kCAFA,mBACA,6CAFA,iBAGA,CAEA,qBAEE,mBADA,aAKA,wCACA,oCAFA,YAFA,8BACA,eAIA,WACA,UAGF,wBAEE,mBADA,aAGA,UADA,uBAEA,oBAIA,gGAEE,kCAIJ,8BACE,wEAEE,mCAKF,0CACE,wBAIJ,uBACE,aACA,sBACA,oBAEA,6BACE,aAIJ,oBAKE,+DADA,sBAHA,WAEA,QAIA,oBALA,kBAIA,SACA,CAGF,oCAEE,mBAOA,qBARA,aAOA,YALA,uBACA,oBACA,aACA,UAIA,+EAHA,UAIE,CAGF,wCAEE,YACA,4CAFA,UAEA,CAGF,qFAEE,UAGF,0CACE,YAGF,2CAEE,WADA,SACA,CAIJ,iBACE,mBACA,YACA,sEAEA,QAAO,CADP,UACA,CAIJ,uBAGE,kBAFA,aACA,sBAEA,WAGF,6BACE,aAGA,gBADA,WADA,8BAEA,CAEA,mCAEE,mBAEA,iBAHA,aAKA,WAHA,uBAEA,cACA,CAEA,uCAGE,wBADA,eAvIM,CAsIN,cAEA,CChIN,iCACE,gBACA,YACA,wBAIA,mBAHA,4BAEA,0BAEA,oBACA,gEAJA,qBAIA,CAEA,2CAGE,yDADA,4DADA,qDAEA,CAGF,uCACE,qEAGF,0CACE,kDAEA,oDACE,0DAKN,sCACE,GAGE,UADA,YAEA,yBAHA,eAGA,EAIJ,uCACE,GAEE,SAAQ,CADR,WACA,EAIJ,kCACE,YAAa,CAGb,YACA,kBACA,gBACA,yBAJA,UAIA,CAEA,wBATF,kCAUI,YAAa,CAAb,CAIJ,yBAIE,wBAEA,2BAHA,gBAEA,oBAJA,kBACA,UAIA,CAEA,wBARF,yBASI,6CAGF,iCACE,kCAAoC,CAQpC,sIANA,sEAjFyB,CAoFzB,mBAFA,+CACA,qCAKA,gBAHA,sCACA,UAEA,CAEA,uCAEE,iCADA,uBAGA,0BAA2B,CAC3B,gCAAiC,CAEjC,sBAEA,2FAEE,sBAIJ,wCACE,cAIJ,4CAKE,sIADA,sEAlHyB,CAiHzB,mBAGA,+CALA,aACA,sBAOA,WAEA,gBADA,4BAEA,mBAGF,kCACE,aACA,sBACA,cAEA,2CAIE,kCAHA,8BACA,SACA,uBACA,CAEA,+DACE,qBAIJ,wCTpFF,6DAMA,uBAEA,YAJA,8CACA,eAFA,gBAIA,aARA,kBAGA,+BADA,USsFI,wFAKA,yFAOA,mBAGA,oBACA,kCALA,aAEA,2BACA,eAEA,CT7FJ,6FAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,+CACE,wCACA,qCAGF,8CACE,mCAGF,sDACE,aAQE,kTACE,+CAIJ,8DACE,uBACA,UAEA,qEACE,gDAGF,oEACE,sDAKF,2EACE,mDACA,oBAOF,wJACE,yBSsCA,sDACE,+CACA,+CAIJ,8CAEE,mBAOA,wBARA,aAIA,gBAFA,uBAGA,qBACA,gBACA,oBAJA,cAKA,CAEA,oGAGE,eADA,aACA,CAIJ,qDAEE,gBADA,UACA,CAGF,iDAKE,YACA,WALA,kBAEA,aADA,oBAEA,UAEA,CAKN,+BAKE,wBAJA,8BAGA,iBAFA,SACA,iBAEA,CAEA,oCACE,kCCpNN,qBAKE,mBVqDA,6DAMA,uBAEA,YAJA,8CUvDA,kCVwDA,eU5DA,aV0DA,gBUrDA,oCAJA,8BV6DA,aU3DA,sBVmDA,kBUhDA,+BVkDA,SUlDA,CV2DA,uDAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,4BACE,wCACA,qCAGF,2BACE,mCAGF,mCACE,aAQE,sOACE,+CAIJ,2CACE,uBACA,UAEA,kDACE,gDAGF,iDACE,sDAKF,wDACE,mDACA,oBU3GN,4BACE,oCAGF,oCAGE,mBAFA,aAIA,eAHA,uBAEA,aACA,CAEA,wCAEE,eADA,aACA,CAKN,mBAEE,cADA,eACA,CAGF,qBACE,kCAGF,0BACE,aACA,sBACA,YACA,UAEA,6BACE,gBC7CJ,gBAKE,oEAFA,wCADA,YADA,8CAGA,oCAIA,aACA,sBACA,UAZuB,CAavB,aALA,gBACA,kBAIA,CAEA,0BAEE,kDADA,4CACA,CAGF,uBAIE,wBAHA,mBACA,oCACA,gBACA,CAGF,0BAME,kCADA,eADA,gBAFA,yBADA,gBAEA,gBAGA,CAEA,iCAEE,wBADA,qBACA,CAIJ,uBACE,aACA,oBAGF,yBAGE,2BADA,sBADA,uBAEA,CAEA,yCACE,gBAIJ,0BAGE,kCAFA,eACA,oCAGA,gBADA,SACA,CAGF,8BAIE,eAEA,mBADA,UAJA,kBAEA,YADA,SAIA,CAIJ,8CACE,GACE,WAIJ,+CACE,GACE,WAIJ,qCACE,GAEE,SAAQ,CADR,SACA,ECpFJ,iCAKE,wGADA,qEAHA,kBAJuB,CAKvB,sCAKA,gBADA,0BAHA,uBAKA,gBAEA,gDACE,aACA,sBACA,UAEA,iEACE,YAKN,uDAIE,UACA,qBAEA,4EAEE,qCADA,uBACA,CAGF,+EAEE,sCADA,oBACA,CAGF,6EAEE,qCADA,sBACA,CAGF,8EAEE,sCADA,qBACA,CAIJ,kBACE,aACA,sBACA,eAtDiB,CAyDf,0GAGE,0BADA,eACA,CAIJ,0BAEE,mBAGA,4CAJA,aAEA,QACA,kBACA,CAEA,gCAQE,uBAFA,YACA,kCAFA,UAGA,CAPA,sCACE,aASJ,8BACE,eAIJ,6BAKE,6CADA,YAFA,mCACA,wBAFA,iBAIA,CAGF,2BACE,4CAEA,mBACA,oCAFA,sBAGA,WAEA,+CACE,gBAIJ,+BACE,4CAEA,mBADA,sBAEA,WAGF,4BACE,4CAMA,eAJA,UAIA,CAGF,oDALE,mBADA,oBAFA,mBAIA,QA8BA,CA1BF,wBACE,wDAAyD,CACzD,0DAA2D,CAC3D,wDAAyD,CZtE3D,6CY0EE,gBAWA,uBAPA,YZ1EF,8CYmFE,sBADA,kCAEA,eALA,gBAQA,UAdA,2BAYA,gBAGA,gBAbA,aAEA,sCZlFF,kBYqFE,+BASA,gEAHA,mBZzFF,SY4FE,CZnFF,6DAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,+BACE,wCACA,qCAGF,8BACE,mCAGF,sCACE,aAQE,kPACE,+CAIJ,8CACE,uBACA,UAEA,qDACE,gDAGF,oDACE,sDAKF,2DACE,mDACA,oBAOF,wGACE,yBY2BJ,8BAEE,mBADA,oBAIA,gBAFA,uBAGA,mBAFA,cAEA,CAEA,oEAIE,cADA,gBADA,cAEA,CAIJ,iCAGE,oBADA,WADA,mBAEA,CAGF,sCACE,uBACA,6BAEA,6CACE,kCACA,UAGF,4CACE,qCACA,UAIA,mDACE,YAMF,uGACE,WAGF,qGACE,qCACA,WAKN,+BACE,iBACA,qBAGF,iCAKE,uCADA,SAGA,8mBAGA,kDADA,oDADA,4CAGA,WAVA,kBAEA,eADA,MAIA,UAKA,CAMA,iEACE,UAON,yCAGE,wCAFA,yCACA,8CACA,CAIA,+CACE,oBAIJ,iBACE,IACE,2CAKN,mCAEE,mBADA,aAGA,WAEA,cAHA,uBAIA,qBAFA,YAEA,CAEA,wCAGE,iBACA,kBAFA,oBADA,kBAGA,CAIJ,uBAKE,mBAJA,6DAEA,gBADA,YAKA,eAHA,aAEA,sBACA,CAEA,2BAEE,eADA,aACA,CC/RJ,WACE,0BAEA,kCAGE,kCADA,0BACA,CAIJ,YAEE,YADA,UACA,CAGF,aAEE,YADA,UACA,CAGF,YAEE,YADA,UACA,CAGF,aAEE,YADA,UACA,CAOF,gDAGE,sBAiBF,4BARE,SAHyC,CAKzC,4EAJA,0BAY2B,CAX3B,yBAW2B,CAG7B,0BAbE,SAHyC,CAKzC,wEAJA,8BAiB2B,CAhB3B,6BAgB2B,CAG7B,gCAlBE,SAHyC,CAKzC,oFAJA,sCAsB2B,CArB3B,qCAqB2B,CAG7B,kCAvBE,SAHyC,CA+B3C,4HA9BE,kBA2B2B,CA1B3B,iBA+ByC,CAF3C,oCA5BE,UA8ByC,CA5BzC,wDACE,kBA2ByB,CA1BzB,iBA0ByB,CAG7B,8DAnCE,yBAuC2B,CAtC3B,wBAsC2B,CArC3B,SAHyC,CAKzC,sGACE,yBAkCyB,CAjCzB,wBAiCyB,CAG7B,4BACE,oBACA,kBAGF,0BACE,aAKA,YAFA,SAFA,kBACA,QAIA,+BAFA,UAEA,CAEA,oCAEE,YADA,UACA,CAWF,8XACE,oCAIJ,2BACE,MAEE,UAGF,IACE,WAIJ,iBACE,2CClIF,gBAEE,mBAKA,oCACA,qCACA,8CARA,aAGA,sBAMA,gCAEA,oCAPA,SAFA,uBAQA,qCALA,iBAOA,uCACA,kBAEA,oBAEE,cADA,YACA,CAGF,6BACE,kCACA,oBAEA,mCACE,aCvBN,sBAGE,cAGA,aACA,+BALA,YAGA,0CADA,gBAHA,eAOA,eAcA,0FAZA,8CAIA,kGACE,CAFF,wBAOA,eATA,oCACA,iBAHA,cAYA,6CAHA,8BACA,WAGA,CAEA,0BAEE,cADA,YACA,CAGF,4BAME,kDACA,qCAHA,sBAHA,WAEA,QADA,kBAGA,UAEA,CAGF,qFACE,4BACE,YAIJ,4BACE,uBAGF,6BACE,sBAIJ,+BACE,wBAEA,qCACE,sCACA,UAIJ,+BACE,wBAEA,qCACE,sCACA,UAIJ,6BACE,wBAEA,mCACE,oCACA,UAIJ,+BACE,wEAEA,iCACE,6EAKJ,8BACE,2DAGF,6BACE,GACE,UACA,6BAGF,GACE,UACA,wBACA,oBAIJ,kCACE,GACE,UACA,6BAIJ,6BACE,GACE,UACA,mBAGF,GACE,UACA,oBACA,mBC5HJ,aAEE,WACA,8BAFA,UAEA,CAGF,8BACE,UAGF,uBAEE,oBADA,aAEA,eAGF,oCAEE,mBADA,aAIA,mCAFA,4EAEA,CAGF,oCACE,OACA,eAGF,kBACE,qBAGF,6BACE,sCAEA,mCACE,kCAGF,iGAEE,aC1CJ,aAEE,mBAIA,0CALA,aAGA,gCADA,cAEA,mCACA,CAGF,eACE,oBAGF,oBACE,qBAGA,MAAK,CAFL,cACA,iBACA,CAMF,0BACE,aAGF,yBACE,cAGF,0BACE,YAEA,oBADA,YACA,CAGF,yBACE,iBAGF,yBAEE,mBADA,YACA,CAGF,2EjBYE,6CAIA,8CADA,gBAJA,kBAGA,+BADA,UiBRA,qCAAsC,CAGtC,mBACA,gBACA,uBAEA,YAEA,wBADA,eANA,oBAUA,yBANA,aAKA,cADA,oBAEA,CjBKA,6LAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,gGACE,wCACA,qCAGF,6FACE,mCAGF,qHACE,aAQE,kuBACE,+CAIJ,6IACE,uBACA,UAEA,kKACE,gDAGF,+JACE,sDAKF,oLACE,mDACA,oBiBrDN,uFAEE,eADA,aACA,CAGF,6LAEE,kBAGF,wKAIE,kBAHA,kBAEA,sBADA,mBAEA,CAEA,0LACE,UAMJ,kDACE,mBCzFJ,YACE,+DACA,yCAGF,0BAIE,iCADA,oCADA,aAEA,CAGF,MACE,6BAGF,aACE,gBACA,2BACA,4BAGF,aACE,mBACA,8BACA,+BAGF,SACE,eACA,sBAGF,WAGE,+DADA,yBADA,UAEA,CAGF,QACE,eACA,sBAWF,4GACE,iCAGF,iFAGE,oDAGF,gBACE,2CACA,mBAGF,0BACE,YACA,gBAGF,QACE,2CAKF,eACE,YAKF,qBACE,WAIF,+DAEE,YAIF,oCAIE,qCADA,wCADA,8CAEA,CAGF,aAEE,wEACA,gBAFA,kBAEA,CAEA,2BACE,UAGF,0BACE,iBAIJ,4CAGE,mCADA,uBACA,CAGF,aACE,+EAEA,sBAGF,2BACE,kFAIF,0BAEE,sEAGF,8CAEE,gBAIF,4EAEE,wCAKF,4CAGE,sBADA,kBACA,CC1JF,cACE,qBAGA,cAFA,kBACA,WACA,CAGF,kBAOE,gDADA,uCADA,kBADA,aAHA,kBACA,SACA,WAIA,CAGF,8BAEE,qCADA,SACA,CAGF,+BAEE,qCADA,SACA,CAGF,+BAEE,qCADA,SACA,CAGF,+BAEE,qCADA,UACA,CAGF,yBACE,GACE,mBAGF,GACE,oBAIJ,yBACE,GACE,mBAGF,GACE,oBAIJ,yBACE,GACE,uBAGF,GACE,2BCjEJ,kBACE,2BAA4B,CAM5B,gFADA,kBAMA,cATA,YAUA,oBARA,YADA,WASA,CAEA,8BACE,0BAA2B,CAG7B,2BACE,WAGF,yBAIE,mBAHA,YAEA,YADA,WAEA,CAEA,8BACE,4BAIJ,uBAGE,8BAEA,kBADA,cAHA,YACA,YAGA,CAGF,2BAaE,0CAZA,uKAWA,0BADA,SAEA,CAEA,wBACE,GACE,wBAGF,GACE,+BAMJ,6BACE,sBAGF,8BACE,qBC1EN,mCAEE,aADA,iBACA,CAGF,yBACE,6DAIA,uBAEA,YAIA,kBAHA,6FACE,CAIF,eAXA,oBAEA,gBAEA,aAQA,SAAQ,CAFR,sCATA,cAWA,CAEA,+BACE,gFACE,CAIJ,+DAEE,2FACE,CAIJ,6BAQE,wBALA,oBACA,iBAFA,cAOA,SAAQ,CAJR,yBAEA,mBADA,qCALA,YAQA,CAEA,+BACE,kBAIJ,8DACE,wBAIA,uFAEE,SAAQ,CADR,kBACA,CAIJ,8BACE,gFACE,CAGF,oCACE,gFACE,CAIJ,yEAEE,2FACE,CAIJ,mEAEE,SAAQ,CADR,kBACA,CAIJ,qEAGE,WADA,oBAEA,gBAIJ,iCAKE,kBAJA,gBAGA,6BAFA,2BACA,yBAEA,CAEA,qCAIE,0BAFA,iBADA,gBAEA,oBAEA,sCAEA,uCACE,kBAIJ,+EAEE,qBAIA,2CACE,gCAMF,uFACE,gCAIJ,qFAGE,WADA,oBAEA,gBAIJ,kCAME,oBAGA,4HACE,CANF,aACA,sBAFA,cAWA,gBACA,UAPA,cAPA,kBACA,aAeA,oBACA,0BACA,sCAHA,oBATA,WAYA,CAEA,iFAKE,sBAHA,WAEA,QADA,kBAGA,WAGF,yCACE,wFAGF,wCACE,6BACA,WAGF,uCAEE,kCADA,gBAEA,qBAGF,kDAGE,8CADA,WADA,2BAGA,mBAEA,6BADA,6BACA,CAGF,2CACE,UAEA,mBADA,kBACA,CAIJ,wCAKE,mBADA,aADA,cAGA,uBALA,kBACA,YAIA,CAEA,+CAIE,+CACA,kBAJA,WAEA,cAGA,YAJA,iBAIA,CAGF,4CAEE,aADA,WACA,CAEA,8CACE,kBC3NN,iBACE,iCAEA,iBADA,oCACA,CAEA,4BACE,iBAGF,uBACE,aAGF,4BACE,gBACA,cAGF,iCACE,sCAIJ,wBAEE,eACA,oCAFA,mCAEA,CAGF,8BAGE,kCACA,iBAFA,uBAEA,CAGF,wBACE,aACA,0BACA,gCAEA,oCALF,wBAMI,+BAGF,qCATF,wBAUI,mCAGF,qCAbF,wBAcI,uCAIJ,yBtBIE,6DAMA,uBAEA,YAJA,8CACA,esBNA,atBIA,gBsBFA,QAAO,CtBMP,aARA,kBAGA,+BsBFA,qBtBCA,SsBAA,CtBSA,+DAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,gCACE,wCACA,qCAGF,+BACE,mCAGF,uCACE,aAQE,sPACE,+CAIJ,+CACE,uBACA,UAEA,sDACE,gDAGF,qDACE,sDAKF,4DACE,mDACA,oBAOF,2GACE,yBsBjEN,+DAEE,cAGF,4BACE,SAEA,gBADA,SACA,CAGF,sDAGE,mBAMA,wBAPA,aAMA,cADA,YAHA,uBACA,kBAJA,kBAKA,UAGA,CAEA,6DAME,0CACA,qCAHA,mBAHA,WAEA,QAEA,oBAHA,iBAKA,CAGF,oHAKE,mBADA,qBAFA,kBACA,mBAEA,CAGF,6DACE,kBAEA,WADA,QACA,CAIJ,4BAIE,wBAHA,mBACA,oCACA,oBACA,CAGF,4BACE,kCAIA,mBAHA,gBACA,gBACA,mBACA,CCrHJ,iBAEE,aACA,sBAEA,YADA,6BACA,CAEA,wBAPF,iBAQI,oBAIJ,UAEE,eAAc,CADd,cACA,CAEA,gBACE,aAKF,6BAEE,mCACA,oCAFA,iCAEA,CAGF,6CAGE,6CAFA,mBACA,mBACA,CAEA,mEAEE,mBADA,YACA,CAGF,oEACE,oBAKN,mEACE,WAGF,aACE,gBAGF,wBAIE,mBvBDA,6DAMA,uBAEA,YAJA,8CuBGA,wBvBFA,euBLA,aAMA,8BvBHA,gBuBCA,WAFA,2BAMA,SvBDA,auBJA,oBvBJA,kBAGA,+BuBOA,8CAJA,WvBJA,SuBQA,CvBCA,6DAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,+BACE,wCACA,qCAGF,8BACE,mCAGF,sCACE,aAQE,kPACE,+CAIJ,8CACE,uBACA,UAEA,qDACE,gDAGF,oDACE,sDAKF,2DACE,mDACA,oBuBjDN,wCACE,oBAEA,wFAIE,wBADA,0BADA,wBAEA,CAEA,4FACE,mDAKN,yCACE,iBAGF,wCACE,iBACA,sBAGF,gCAEE,eADA,eACA,CAEA,sCACE,kBAGF,uCACE,0DAGF,sCACE,0BAKF,wIACE,yBChHN,wBAEE,kCACA,+DAFA,mBAIA,6CADA,iBAEA,gBAGF,yBAEE,aADA,6BACA,CAGF,yBACE,8BAGF,kDAGE,uBADA,6BAEA,oBAGF,+BACE,qBACA,OACA,oCAEA,mCACE,gBAIJ,oEAGE,mBADA,aAEA,uBACA,iBACA,mBAEA,4EAEE,YADA,UACA,CAIJ,iCAEE,8BADA,eACA,CAGF,0BACE,8BAGF,gCACE,8BACA,qBAEA,4CACE,kBAGF,2CACE,mBAIJ,gCACE,kBAIA,qDACE,+CAIJ,kFxB/CE,yCADA,0GwBqDA,0BxBlDA,iGACE,6BAGF,0GACE,qCAGF,wMAEE,oCACA,wHAGF,uGACE,qCACA,0HwByCF,mBACE,gBAGF,wBACE,mBAGF,6BACE,aACA,QACA,mBAEA,iCAEE,eADA,gBACA,CAIJ,2BACE,eAGF,gCACE,qBAGF,yBAGE,mBAFA,aACA,iBACA,CAGF,0BAEE,iBADA,UACA,CAGF,0BACE,WAGF,sCACE,mBACA,oBAEA,0CACE,kBAIJ,0BACE,WC/IJ,mBAKE,6BAMA,8CALA,8DACE,CAEF,kCANA,0CAcA,UALA,aAFA,oBASA,oBAlBA,eAiBA,UALA,+BAXA,yCAYA,8EACE,CAXF,YAeA,CAEA,0BAKE,yBADA,sBAHA,WAEA,QAGA,YAJA,kBAKA,mBAIA,gCACE,WAKF,iCACE,WAIJ,iCACE,iEACE,CAEF,UAGA,qBAFA,WACA,8BACA,CCtDJ,iBAGE,mBADA,oBAEA,8BACA,oCACA,QAAO,CALP,iBAKA,CAEA,+CAOE,oBADA,mBAJA,WACA,qBAEA,YADA,UAGA,CAGF,wBAIE,yCAFA,kBACA,WAFA,iBAGA,CAGF,uBAME,6CADA,6BAFA,OAFA,kBACA,QAEA,kBAEA,CAEA,+BARF,uBASI,uBAKF,8BACE,eAKN,2BACE,GACE,yBAIJ,kBAGE,mBAIA,8DALA,uBAGA,QADA,uBAHA,eAOA,sCAFA,WAEA,CAEA,yBAIE,6BAHA,WAEA,QAEA,YAHA,iBAGA,CAGF,mCACE,2CAGF,0BACE,UAEA,oBADA,mBACA,CAEA,mDACE,WACA,qBAKN,mCACE,GACE,WACA,sBCtFJ,eACE,uBAAwB,CAIxB,mCAOA,4BAHA,sEACA,6CAJA,4EAKA,0CAEA,qCAVA,kBACA,UASA,CAGA,qCACE,sBAIE,qCACA,sBAJA,WAEA,qBAGA,oBAJA,iBAIA,EAIJ,iBACE,oCACA,kCAKE,2BACE,qCAKA,8BACA,oCAHA,mDACA,oBAFA,0CADA,eAKA,CAEA,yCACE,kBAGF,wCACE,iBAGF,yCACE,0CAGF,wCACE,mCAGF,6BACE,qCACA,gBAEA,4CACE,WAIJ,+BAGE,aAFA,sBACA,WACA,CAON,wBACE,mCAEA,2BACE,wCAGA,YADA,2DADA,qBAEA,CAEA,yCACE,0CAGF,wCACE,mCAKJ,yCAEE,yDADA,qDACA,CAGF,wCAEE,0DADA,sDACA,CAKA,uDACE,kDAGF,sDACE,mDAMF,sDACE,qDAGF,qDACE,sDAMR,qCAEE,mBADA,aAEA,uBAGF,4BACE,yBACA,2BACA,mBACA,WAGF,+BACE,+CACA,6BACA,0CAGF,8BACE,mBAOF,uEAEE,oBAIA,oDACE,MAIJ,+BACE,eAEA,mBADA,oBACA,CAWA,+OAIE,wBADA,sBACA,CAIJ,uBACE,gCAAkC,CAElC,mCACE,YAMA,8fAIE,wBADA,sBACA,CAIJ,uCACE,oBAEA,2CAEE,wBADA,sBACA,CAKN,2BACE,qBAGF,sBACE,sBAAuB,CAEvB,kCACE,YAMA,qfAIE,sBADA,oBACA,CAIJ,sCACE,oBAEA,0CAEE,sBADA,oBACA,CAKN,4CAEE,wBAGF,uEAKE,mB3B3MF,6C2B6ME,gBASA,uBAPA,Y3B3MF,8C2B+ME,cADA,eAGA,kB3BlNF,gB2BiNE,oBARA,uBAYA,wBARA,SAFA,aAGA,U3BlNF,kB2BwNE,+BAEA,sC3BxNF,S2BwNE,C3B/MF,qLAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,4FACE,wCACA,qCAGF,yFACE,mCAGF,iHACE,aAQE,ktBACE,+CAIJ,yIACE,uBACA,UAEA,8JACE,gDAGF,2JACE,sDAKF,gLACE,mDACA,oBAOF,+pBACE,yB2BuJJ,qLAGE,kBADA,gBACA,CAIJ,sB3BlOA,6DAMA,uBAEA,YAJA,8CACA,eAFA,gBAIA,aARA,kBAGA,+BADA,U2BoOE,gEAAmE,CACnE,wEAA2E,CAC3E,0EAKA,uEAA0E,CAG1E,mBADA,0BACA,C3BrOF,yDAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,6BACE,wCACA,qCAGF,4BACE,mCAGF,oCACE,aAQE,0OACE,+CAIJ,4CACE,uBACA,UAEA,mDACE,gDAGF,kDACE,sDAKF,yDACE,mDACA,oB2BqLJ,yDAEE,gBCvSN,qBAHE,iBAWA,CARF,QAGE,mBAEA,oCACA,kBAZc,CAQd,oBAEA,eAIA,qCADA,WACA,CAEA,eAIE,qCACA,sBAJA,WAEA,QAGA,oBAJA,iBAIA,CAGF,aACE,WAIJ,eAIE,mB5BwBA,6DAMA,uBAEA,YAJA,8C4BxBA,kBAtCc,CAuCd,kC5BwBA,e4B9BA,aAQA,8BADA,gBALA,uBACA,gBAMA,kB5BuBA,a4B5BA,iB5BoBA,kBAGA,+BADA,S4BjBA,C5B0BA,2CAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,sBACE,wCACA,qCAGF,qBACE,mCAGF,6BACE,aAQE,8MACE,+CAIJ,qCACE,uBACA,UAEA,4CACE,gDAGF,2CACE,sDAKF,kDACE,mDACA,oBAOF,6EACE,yB4BlFN,2CAEE,UAIJ,yBAEE,gBADA,cACA,CAGF,0BACE,aAGF,sBAIE,0CAFA,eACA,gBAFA,SAGA,CAEA,6BACE,4BACA,iEACA,+CAGF,4BACE,aAGF,yDAEE,QAIJ,yBACE,iBACA,uBACA,UCtFF,gCAWE,sGADA,qEAPA,qBACA,qCAHA,2BAIA,iBACA,gBACA,qCALA,qBAMA,qBACA,SAEA,CAEA,+CACE,UAIF,gEACE,iCAEA,oBADA,qBACA,CAGF,+CACE,8BAA+B,CAC/B,yBAA0B,CAE1B,SACA,YAIA,iEAcE,wDATA,qEADA,sBAHA,WAEA,QAIA,wJAMA,gDAPA,WAJA,iBAYA,CAGF,mCACE,GAEE,wDADA,SACA,CAGF,IACE,UAGF,GAEE,wCADA,SACA,EAMR,sDAIE,UACA,qBAEA,2EAEE,qCADA,uBACA,CAGF,8EAEE,sCADA,oBACA,CAMJ,oCACE,uBAGF,2BAGE,mBADA,oBAEA,uBAEA,iBADA,gBAEA,qBANA,iBAMA,CAEA,kCAKE,6DADA,oBAHA,WAEA,QADA,iBAGA,CAGF,+BAEE,WADA,SACA,CAIJ,oCAGE,mBADA,oBAGA,SADA,uBAHA,iBAIA,CCxHF,+BAEE,mBADA,aAEA,2BAGF,wCACE,mBAGF,kBAEE,oBADA,iBACA,CAGF,wBAGE,WAIA,SAEA,gBADA,UAPA,kBACA,UAQA,mBACA,qBAMM,qHACE,kFACE,CAqCJ,mVACE,mFACE,CAON,6CACE,4EACE,CAIJ,4CACE,mBAMJ,uCACE,mBAEA,8CAEE,gFADA,qBAEE,CAOF,sDACE,sFACE,CAIJ,qDACE,6BAOV,wBAGE,uBAGA,eAJA,oBAMA,0CAJA,2BAGA,iBAFA,SAJA,iBAOA,CAEA,+BAYE,8BANA,kBAGA,6FACE,CATF,WACA,qBAMA,kBAHA,gBADA,eADA,kBAIA,qCAKA,CAGF,8BAGE,mBAOA,6BATA,WACA,aAOA,YALA,uBAGA,OAIA,83DAGA,kDADA,oDADA,gDAPA,kBACA,MAUA,mBADA,qCAPA,UAQA,CAIA,qCACE,eAKF,qCACE,2EACE,CAOJ,2EACE,kFACE,CAMR,+BAEE,kCACA,iBAFA,gBAEA,CC/LF,YAEE,8BACA,6DACA,8CAEA,kCAIA,YATA,cAUA,qCAJA,+CACE,CAHF,UAMA,CAGA,gBACE,8CACA,0BACA,2BAGF,kBACE,uCAGF,4CAGE,uCACA,yCAFA,YAEA,CAGF,qBACE,uBACA,YACA,aAGF,yBACE,kCAGF,iCACE,kDAGF,8BACE,kCAGF,oCAEE,kCADA,oCACA,CAIJ,4BAOE,wBADA,iBAHA,YACA,wCAIA,w/BAEA,oDACA,oDAFA,4CAGA,YAXA,kBACA,WAGA,UAOA,CCnEF,qBAME,uBAHA,uBAEA,aADA,yBAHA,kBACA,UAIA,CAEA,4BAOE,wBANA,WAEA,cAGA,YAFA,wBAIA,klCACA,kDACA,oDACA,oBATA,kBAGA,UAMA,CAGF,2CAEE,mBAkBA,oCAGA,qCAPA,8CAMA,kCAPA,wBAEA,eAhBA,oBAYA,8BACA,gBAXA,uBAEA,kBAaA,gBAdA,aAEA,kCAeA,sCAFA,kBAIA,CAdA,gCATF,2CAUI,sBAeF,iDACE,2CAGF,kDACE,4CACA,qDAKF,yDACE,mDC3DN,eAEE,8BACA,6DAEA,8CAGA,kCAPA,cAMA,oBAHA,kCAOA,+DALA,UAME,CAKA,oCACE,uCAGF,yEAGE,uCACA,yCAFA,YAEA,CAIJ,wBACE,mBAIJ,sBAIE,oCADA,wBAEA,oBCrCF,cACE,qCAGF,0BAEE,kCADA,mBAGA,iBADA,mEACA,CAGF,kBACE,YACA,SACA,UAEA,kDACE,gBAIJ,mBAEE,qCADA,oCACA,CAIA,yBAKE,WAJA,YAEA,cACA,SAFA,iBAGA,CAGF,oDACE,kBAGF,0BACE,4CAGF,2BACE,6CAIJ,oBAEE,mBADA,aAEA,0CAEA,oBADA,aAEA,iBAGF,0BAKE,kCAJA,cAKA,iBAFA,qBAEA,CAGF,eAGE,mBACA,0CACA,8CAHA,oBAOA,WAHA,sCACA,oDACA,eAPA,iBAQA,CAEA,oCAOE,6DADA,cAJA,qBACA,qBAFA,kBAGA,UACA,YAEA,CAGF,0BACE,oCAIJ,qBlClCE,6DAMA,uBAEA,YAJA,8CACA,eAFA,gBAIA,aARA,kBAGA,+BADA,UkCoCA,0CAA2C,CAI3C,mBAEA,mBAJA,oBACA,uBAEA,eACA,ClCjCA,uDAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,4BACE,wCACA,qCAGF,2BACE,mCAGF,mCACE,aAQE,sOACE,+CAIJ,2CACE,uBACA,UAEA,kDACE,gDAGF,iDACE,sDAKF,wDACE,mDACA,oBkCfN,0BAME,6BACA,kBANA,qBAEA,YAKA,soSAEA,kDACA,oDAFA,4CAJA,gBADA,eAFA,UASA,CAIJ,qBACE,kBAGF,2CACE,kBAEA,YADA,WACA,CAGF,8BAGE,mBAFA,kCAMA,eALA,oBAIA,gBAFA,WACA,iBAEA,CAEA,kCAEE,YADA,UACA,CAIJ,sBAOE,sBANA,aACA,sBAIA,2BAHA,qBAEA,kCADA,SAGA,CAEA,yBAIE,kBAFA,aACA,sBAEA,UACA,kBACA,mBANA,iBAMA,CAEA,sDACE,oCACA,SAGF,gCAKE,mBAIA,6BAIA,oBADA,wBAVA,6BADA,0BAGA,aAWA,oCAJA,YALA,uBAEA,OALA,kBAWA,kBAPA,MAGA,UAKA,CChLN,4BAEE,mBADA,aAEA,2BAIA,kCACE,sBAGF,sBAGE,mBADA,UADA,iBAEA,CAII,uDACE,iFACE,CASJ,8KACE,oFACE,CAQN,oCACE,eAEA,2CACE,8EACE,CAUF,yMACE,oFACE,CASR,qCACE,mBAEA,4CACE,YAMR,sBAKE,eAHA,qBAIA,0CAHA,gBACA,mBAHA,kBAQA,0BAEA,6BASE,8BADA,kBAEA,wEACE,CATF,sBADA,WAMA,eApGe,CAkGf,OAFA,kBACA,MASA,iDAPA,cAOA,CAIJ,4BAEE,kCACA,iBAFA,iBAEA,CAGF,mFACE,aCnHJ,gBAEE,sCAEA,8CACA,mBACA,gBAHA,aAFA,kBAMA,qDACE,CAIJ,iDAEE,UAGF,8BACE,kBAOF,qEACE,mBAGF,uCACE,kBAGF,kCACE,mBAQF,2FACE,kBAOF,qGACE,mBAGF,uDACE,kBAGF,kDACE,mBAQF,2HACE,kBAOF,qIACE,mBAGF,uEACE,kBAGF,kEACE,mBAGF,6EACE,kBAQF,kBACE,aAKF,yGAEE,mBAIF,gCACE,aAEA,gBADA,UACA,CAGF,iCAGE,6BACA,qCAFA,aADA,UAGA,CAEA,mCACE,aAKF,kCACE,gBAGF,wBAEE,mBADA,aAGA,gBADA,2BAGA,qBADA,iBACA,CAEA,mCAME,YAHA,YAIA,iBAFA,oBADA,gBAHA,kBACA,UAKA,CAEA,0CAIE,6BACA,8CAJA,WAEA,QAGA,UAJA,kBAKA,sCAGF,yCAIE,mCAHA,WAEA,YAEA,4nBACA,kDACA,oDACA,4CANA,iBAMA,CAIA,gDACE,WAOR,mCAKE,mBASA,uBALA,YAGA,iBAGA,eAXA,aAIA,YAFA,uBAKA,iBADA,aATA,kBAEA,YADA,UAKA,WAMA,SAEA,CAEA,0CAIE,uBAIA,CAGF,mFANE,oBAJA,WAEA,QAIA,aALA,kBAMA,sCAFA,UAaA,CARF,yCAIE,iCAIA,CAGF,uCAEE,YADA,UACA,CAIA,gDACE,WAMF,iGACE,YAGF,+FACE,gCCtOR,0CAHE,iBAMA,CAHF,gBACE,4BAEA,CAEA,uBAEE,gCADA,oDACA,CAEA,yCACE,kCADF,oCACE,kCAIF,kDACE,wBAGF,qDACE,wBAGA,eAEA,uCAQA,eAZA,WAGA,mBAEA,gzBAEA,oDADA,4CALA,kBAOA,UACA,oBACA,oBACA,mFACA,CAEA,2DACE,sBAGF,4DACE,qBAOA,qJACE,WACA,mBACA,mBAOJ,uHACE,UACA,mBACA,mBAKN,sBAEE,iBAGA,SACA,aAFA,OAGA,oBANA,kBAEA,KAIA,CAEA,0BAKE,oBACA,iBAJA,YAEA,gBADA,eAIA,yBACA,sCAPA,UAOA,CAGF,yDAOE,iDACA,mBANA,WAaA,mBALA,oBACA,iBAPA,WAEA,kBADA,iBAQA,UADA,yBAEA,QALA,sCANA,SAYA,CAGF,4BAGE,6CAFA,0BACA,4BACA,CAEA,+BALF,4BAMI,uBAKN,2CAOE,kCAFA,oBADA,mBAEA,UALA,kBAEA,YADA,wBAKA,CAKE,mDAGE,iBAFA,UACA,OACA,CAGF,sDAGE,eAFA,WACA,OACA,CAGF,qDAGE,eAFA,UACA,OACA,CAKN,yBACE,wBAAyB,CAGzB,kBADA,UACA,CAEA,wBANF,yBAOI,gBAGF,oDACE,eAGF,gCAME,8DAFA,8CAGA,wDANA,WAEA,QADA,kBAGA,UAEA,CAEA,qFATF,gCAUI,8BAMJ,mDACE,+BAGF,wDACE,UAEA,oBADA,mBACA,CAIJ,0BACE,kCACA,WAEA,qDACE,aAKN,mCAWE,wGADA,qEAPA,mBACA,sCAGA,WACA,UAHA,gBAJA,kBAYA,UAJA,sCAKA,sBAFA,oBAVA,WAaA,iCATA,UASA,CAEA,4CACE,UACA,WAEA,qBADA,kBACA,CAIJ,2CAIE,kCACA,oCAHA,YACA,UAFA,iBAIA,CCxOF,gBACE,kBACA,WAEA,sBAOE,8BAFA,SAJA,WAOA,0jEAGA,kDADA,oDADA,4CAGA,oBAVA,kBAEA,WADA,MAUA,+CAPA,aAOA,CAGF,uBACE,6DAEA,6DAIA,8CACA,kCAGA,eATA,cAIA,yBAIA,gBANA,kCAKA,sCAJA,oBAMA,CAEA,6BACE,uCAGF,2DAGE,uCACA,yCAFA,YAEA,CAGF,gCACE,oBAKF,4BACE,gBAKN,sBAGE,6DACA,8CACA,kCAEA,aANA,kBAKA,sCAJA,UAKA,CAEA,4BACE,uCACA,yCAGF,+BACE,oBCvEJ,4BAIE,kCAHA,aACA,UACA,gBACA,CAGF,kBACE,0CACA,qCAGA,8CADA,iBADA,iCAEA,CCZF,uBAEE,qBADA,iBACA,CAMQ,+DACE,qBACA,oBAMR,2CACE,2BAEA,kDACE,iBACA,yBAGF,iDACE,qBACA,WAKA,+GACE,qBAIJ,kLAEE,qBACA,kCAMR,6BAGE,WAIA,SAEA,gBADA,UAPA,kBACA,UAQA,mBACA,qBAEA,kDACE,2CAKE,iDACE,UACA,s1DAKA,iHACE,wHACE,CAKJ,+GACE,UACA,WAIJ,wFACE,UAGF,0FACE,UAMJ,4CACE,mBAKN,6BAGE,uBAGA,eAJA,aAMA,0CAJA,2BAGA,iBAFA,SAJA,kBAQA,gEAEA,oCAME,+BACA,mBAGA,kHARA,WADA,qBAQA,kBAJA,gBADA,eADA,kBAKA,qCAGE,CAKJ,mCAGE,mBAOA,6BAGA,oBAEA,qFAdA,WACA,aAOA,YALA,uBAGA,SAKA,kDADA,gDANA,kBACA,QAQA,sCANA,UAOA,CAIF,0CACE,2CAKA,qFACE,mKACE,CAMJ,mFACE,SACA,WAIJ,0EACE,UAGF,sJAGE,uBADA,kBAEA,uBACA,gEChLJ,uBAGE,SADA,UAEA,gBAHA,qCAGA,CAGF,gCACE,kBACA,UAEA,kCACE,mEAIJ,yCACE,GACE,UAGF,GACE,WAIJ,sBAKE,oCADA,gCADA,iBAEA,CAEA,2CAQE,8BAHA,SAJA,WAMA,YAHA,OAKA,0DACA,oDACA,4CATA,kBACA,MAGA,UAKA,CAIJ,IACE,kCAGF,OACE,iBAEA,cACE,o0BAIJ,cACE,iBACA,oCAGF,SACE,oBAEA,gBACE,s+BAIJ,gBACE,oBACA,oCAGF,MACE,wBAEA,aACE,koBClFJ,oBAGE,mBAMA,mBAPA,aAEA,uBAGA,qCACA,gBAHA,uCACA,oBALA,iBAQA,CAEA,qDAEE,WACA,kBACA,UAGF,2BAOE,mDALA,eACA,qFAKA,YAPA,UAOA,CAGF,0BAEE,qCACA,sBAFA,OAEA,CAGF,wBACE,cACA,UAIJ,4BAGE,cAAa,CAFb,kBACA,UACA,CAEA,qEAME,mBAJA,WAEA,QADA,kBAEA,UACA,CAGF,mCAWE,kDAVA,8BACA,kYACE,CAOF,UACA,CAGF,kCAUE,mDATA,kYACE,CAOF,UACA,CAIJ,gCACE,GACE,WAGF,IACE,WACA,0BAGF,GACE,WACA,0BAIJ,gCACE,GACE,WAGF,IACE,YACA,yBAGF,GACE,WACA,yBAIJ,+BACE,GACE,yBAIJ,mBAEE,0BACA,iBAFA,gBAGA,8BAGF,mBAEE,kCADA,oCAEA,SAGF,qBACE,eAEA,oCADA,oCACA,CCvIF,kCAEE,mBADA,aAGA,iBACA,SAFA,sBAEA,CAGF,iBACE,mCACA,qCAEA,+BACE,gBAGF,2BARF,iBASI,aACA,sBACA,8BAEA,wCACE,kBAIJ,yBAlBF,iBAmBI,aAEA,8BADA,wBAEA,6CAEA,+BAEE,eAAc,CADd,aACA,CAMA,yFACE,mBAIJ,wCACE,cAIJ,yBA1CF,iBA2CI,kCAGE,6CACE,mBAGF,4CACE,oBAKN,yBAxDF,iBAyDI,sCAGE,6CACE,mBAGF,4CACE,oBAKN,yBAtEF,iBAuEI,0CAGE,6CACE,mBAGF,4CACE,oBAMR,kBAIE,cADA,cAFA,kBAIA,UAHA,YAGA,CAEA,oDAIE,mBADA,QADA,iBAEA,CAEA,gEAME,wBACA,kBAJA,YAFA,WAGA,UAFA,kBAGA,SAEA,CAIJ,yBACE,oBAEA,+BACE,WAIJ,2BACE,sBAEA,iCACE,WAIJ,wBAKE,iBADA,mBAFA,WACA,QAFA,iBAIA,CAIJ,2BAEE,qBADA,gBAEA,oBAEA,+BACE,uBACA,YAIA,8BADA,iBAFA,SACA,SAEA,CCvJJ,mBAEE,mBADA,aAEA,8BACA,gCAEA,0BAEE,mBADA,aAEA,UAEA,6BAGE,mBADA,aAKA,WADA,qBAFA,SACA,UAJA,iBAMA,CAEA,oCAKE,oCADA,oBAHA,WAEA,QAGA,UAJA,kBAKA,sCAIA,0CACE,aACA,UAMJ,6CAIE,eACA,gBAJA,mBAEA,sBADA,mBA3CU,CAkDd,yDAGE,mBAEA,eAKA,2CAHA,8CAIA,eATA,aAOA,+BADA,wCAFA,eAxDY,CAsDZ,uBAHA,iBAUA,CAKN,mBACE,aACA,sBACA,SAEA,yBAOE,mB5CpBF,6DAMA,uB4CgBE,kC5CdF,Y4CeE,+D5CnBF,8C4CiBE,mB5ChBF,e4CWE,a5CbF,gB4CeE,SADA,+B5CVF,a4CYE,iD5CpBF,kBAGA,+B4CsBE,sC5CvBF,S4CuBE,C5CdF,+DAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,gCACE,wCACA,qCAGF,+BACE,mCAGF,uCACE,aAQE,sPACE,+CAIJ,+CACE,uBACA,UAEA,sDACE,gDAGF,qDACE,sDAKF,4DACE,mDACA,oB4ClCJ,gCACE,aAGF,gCAEE,wBADA,mBACA,CAGF,kCACE,aAEA,SADA,+BAGA,gBADA,8BACA,CAGF,wCACE,aACA,sBACA,UACA,gBAGF,kCACE,aACA,sBACA,UACA,mBAEA,wCACE,aAIJ,gCACE,wBAGA,eAFA,oCACA,QACA,CAGF,sCAEE,mBAEA,kCAHA,aAKA,eAHA,SAEA,SAGA,kJADA,kBACA,CAEA,0CAEE,YADA,UACA,CAGF,4CACE,aAIJ,yCAEE,mBADA,oBAGA,UADA,sBACA,CAGF,+BACE,qCAAsC,CAEtC,yCACA,6CAGF,+DAEE,sCAAuC,CAEvC,0CACA,8CAKN,mBAEE,mBADA,oBAEA,uBAEA,uBAEE,YADA,UACA,CAIJ,kBACE,aACA,sBACA,UAEA,0BAGE,yEAFA,aACA,sBAEA,iBACA,kBCjMJ,iBAIE,mBAHA,aAEA,SADA,+BAGA,SACA,UAEA,oCACE,qCAGF,oBACE,aACA,0BAEA,wBAEE,YADA,UACA,CAIJ,oBAGE,mBACA,oCACA,gBAJA,SACA,SAGA,CCzBJ,kBACE,wBAEA,kCACE,sBACA,kBAIJ,sBACE,8CAEA,6BACE,qBAGF,mCAEE,mBAIA,gEALA,aAEA,uBAEA,aADA,iBAEA,CAEA,sCACE,GACE,WAKN,+BAGE,kCAFA,aACA,iBAGA,oCACA,kBAFA,eAEA,CAGF,gCACE,aAEA,SADA,8BAIA,2BAEA,gDACE,aAEA,oDACE,gDAGF,8DACE,sBAEA,oEACE,iBAEA,wEACE,iBAKN,6DACE,oBAEA,mEACE,gBAEA,uEACE,gBAMR,wEACE,4CACA,YACA,oBAIJ,+BAEE,kBADA,UACA,CAIJ,4B9CnCE,6DAMA,uBAEA,YAJA,8CACA,e8CiCA,aAKA,6B9CxCA,gB8CqCA,iBADA,oCAGA,gBAEA,gB9CrCA,a8CkCA,mB9C1CA,kBAGA,+BADA,S8C2CA,C9ClCA,qEAME,sBAHA,WACA,QAIA,oBANA,kBAKA,sCAFA,UAGA,CAGF,mCACE,wCACA,qCAGF,kCACE,mCAGF,0CACE,aAQE,kQACE,+CAIJ,kDACE,uBACA,UAEA,yDACE,gDAGF,wDACE,sDAKF,+DACE,mDACA,oBAOF,oHACE,yB8CtBN,kCACE,oBACA,uBAEA,mBADA,mBACA,CAEA,sCAEE,eADA,aACA,CAIJ,+DAEE,oBADA,aAEA,eAEA,qEAEE,wBADA,aAKA,YADA,oCAFA,UAMA,uBAFA,mBAHA,qBAIA,iBACA,CAEA,uGACE,kCACA,mBAIJ,yEAEE,mBADA,aAGA,eACA,UAFA,sBAGA,iBAIJ,8CACE,eAEA,yGAEE,aAGF,uFACE,gBAIJ,mDACE,iBACA,oBAGF,yCACE,kCAGA,qBADA,gBAGA,uBAJA,qBAGA,iBACA,CAEA,gDAOE,uCACA,8CAHA,WAJA,WAEA,UAMA,WAPA,kBAEA,SAEA,SAGA,CCzLN,8CAEE,wBACA,qBAGF,kDAEE,0BAOF,mEACE,aAGF,uBAEE,gBADA,eACA,CAGF,uBACE,iBAGF,+BACE,gBAGF,6BACE,aAKA,kFACE,mBAGF,4DACE,0BAGF,kEAIE,aAFA,YAGA,UAFA,0BAFA,iBAIA,CAEA,0EACE,iBAGF,4EACE,2BCzDJ,yCACE,eAEA,2CAEE,iBADA,8BACA,CAIJ,6BACE,iEAGF,8KAIE,oBADA,8BAEA,mBAGF,wSAOE,YADA,mBACA,CAGF,0LAIE,UADA,oBAEA,mBAGF,8KAGE,WAGF,sGAGE,4BAGF,mDAEE,aAGF,2DAIE,sCACA,kBAFA,cADA,iBAIA,kBAGF,4BACE,WAGF,+BACE,YAGF,mFAEE,WAGF,wCAEE,iBACA,mBAFA,iBAEA,CAGF,sCAEE,qCAUA,oBAJA,mBALA,qBAEA,8BACA,gBACA,cANA,mBAGA,uBAKA,kBAEA,wBADA,kBAEA,CAIJ,6BACE,gBACA,wBAEA,gCAEE,8BADA,yBACA,CC3GJ,mBAEE,WADA,kBACA,CAGF,4BACE,mBAGF,sBACE,eAGA,gBAFA,iBACA,eACA,CAGF,yBACE,mBAGF,4CAEE,iBACA,kBCvBF,oBAEE,WADA,kBACA,CAGF,6BACE,mBAGF,uBACE,eACA,gBAGF,0BAEE,iBACA,kBAFA,kBAEA,CAGF,uCACE,WAGF,2JAGE,iBACA,yCAGF,qCACE,WACA,eAGF,uCACE,aAGF,iDACE", "sources": ["webpack://jenkins-ui/./src/main/scss/abstracts/_theme.scss", "webpack://jenkins-ui/./src/main/scss/abstracts/_colors.scss", "webpack://jenkins-ui/./src/main/scss/base/_core.scss", "webpack://jenkins-ui/./src/main/scss/base/_display.scss", "webpack://jenkins-ui/./src/main/scss/base/_layout-commons.scss", "webpack://jenkins-ui/./src/main/scss/base/_spacing.scss", "webpack://jenkins-ui/./src/main/scss/base/_style.scss", "webpack://jenkins-ui/./src/main/scss/base/_typography.scss", "webpack://jenkins-ui/./src/main/scss/abstracts/_mixins.scss", "webpack://jenkins-ui/./src/main/scss/base/_visibility-utils.scss", "webpack://jenkins-ui/./src/main/scss/components/_alert.scss", "webpack://jenkins-ui/./src/main/scss/components/_app-bar.scss", "webpack://jenkins-ui/./src/main/scss/components/_avatar.scss", "webpack://jenkins-ui/./src/main/scss/components/_badges.scss", "webpack://jenkins-ui/./src/main/scss/components/_breadcrumbs.scss", "webpack://jenkins-ui/./src/main/scss/components/_buttons.scss", "webpack://jenkins-ui/./src/main/scss/components/_cards.scss", "webpack://jenkins-ui/./src/main/scss/components/_command-palette.scss", "webpack://jenkins-ui/./src/main/scss/components/_content-blocks.scss", "webpack://jenkins-ui/./src/main/scss/components/_dialogs.scss", "webpack://jenkins-ui/./src/main/scss/components/_dropdowns.scss", "webpack://jenkins-ui/./src/main/scss/components/_icons.scss", "webpack://jenkins-ui/./src/main/scss/components/_notice.scss", "webpack://jenkins-ui/./src/main/scss/components/_notifications.scss", "webpack://jenkins-ui/./src/main/scss/components/_page-footer.scss", "webpack://jenkins-ui/./src/main/scss/components/_page-header.scss", "webpack://jenkins-ui/./src/main/scss/components/_panes-and-bigtable.scss", "webpack://jenkins-ui/./src/main/scss/components/_progress-animation.scss", "webpack://jenkins-ui/./src/main/scss/components/_progress-bar.scss", "webpack://jenkins-ui/./src/main/scss/components/_row-selection-controller.scss", "webpack://jenkins-ui/./src/main/scss/components/_section.scss", "webpack://jenkins-ui/./src/main/scss/components/_side-panel-tasks.scss", "webpack://jenkins-ui/./src/main/scss/components/_side-panel-widgets.scss", "webpack://jenkins-ui/./src/main/scss/components/_skip-link.scss", "webpack://jenkins-ui/./src/main/scss/components/_spinner.scss", "webpack://jenkins-ui/./src/main/scss/components/_table.scss", "webpack://jenkins-ui/./src/main/scss/components/_tabs.scss", "webpack://jenkins-ui/./src/main/scss/components/_tooltips.scss", "webpack://jenkins-ui/./src/main/scss/form/_checkbox.scss", "webpack://jenkins-ui/./src/main/scss/form/_codemirror.scss", "webpack://jenkins-ui/./src/main/scss/form/_file-upload.scss", "webpack://jenkins-ui/./src/main/scss/form/_input.scss", "webpack://jenkins-ui/./src/main/scss/form/_layout.scss", "webpack://jenkins-ui/./src/main/scss/form/_radio.scss", "webpack://jenkins-ui/./src/main/scss/form/_reorderable-list.scss", "webpack://jenkins-ui/./src/main/scss/form/_search-bar.scss", "webpack://jenkins-ui/./src/main/scss/form/_select.scss", "webpack://jenkins-ui/./src/main/scss/form/_textarea.scss", "webpack://jenkins-ui/./src/main/scss/form/_toggle-switch.scss", "webpack://jenkins-ui/./src/main/scss/form/_validation.scss", "webpack://jenkins-ui/./src/main/scss/pages/_about.scss", "webpack://jenkins-ui/./src/main/scss/pages/_build.scss", "webpack://jenkins-ui/./src/main/scss/pages/_dashboard.scss", "webpack://jenkins-ui/./src/main/scss/pages/_icon-legend.scss", "webpack://jenkins-ui/./src/main/scss/pages/_job.scss", "webpack://jenkins-ui/./src/main/scss/pages/_manage-jenkins.scss", "webpack://jenkins-ui/./src/main/scss/pages/_plugin-manager.scss", "webpack://jenkins-ui/./src/main/scss/pages/_setupWizardFirstUser.scss", "webpack://jenkins-ui/./src/main/scss/pages/_setupWizardConfigureInstance.scss"], "sourcesContent": ["@use \"sass:color\";\n@use \"../base/breakpoints\";\n\n$colors: (\n  \"blue\": oklch(55% 0.2308 256.91),\n  \"brown\": oklch(60% 0.0941 72.67),\n  \"cyan\": oklch(60% 0.1497 234.48),\n  \"green\": oklch(70% 0.2155 150),\n  \"indigo\": oklch(60% 0.191 278.34),\n  \"orange\": oklch(70% 0.2001 50.74),\n  \"pink\": oklch(60% 0.2601 12.28),\n  \"purple\": oklch(60% 0.2308 314.6),\n  \"red\": oklch(60% 0.2671 30),\n  \"yellow\": oklch(80% 0.17 76),\n  \"teal\": oklch(60% 0.1122 216.72),\n  \"white\": #fff,\n  \"black\": oklch(from var(--accent-color) 2% 0.075 h),\n);\n$semantics: (\n  \"accent\": var(--blue),\n  \"text\": var(--black),\n  \"error\": var(--red),\n  \"warning\": var(--orange),\n  \"success\": var(--green),\n  \"destructive\": var(--red),\n  \"build\": var(--green),\n  \"danger\": var(--red),\n  \"info\": var(--blue),\n);\n\n:root,\n.app-theme-picker__picker[data-theme=\"none\"] {\n  // Font related properties\n  --font-family-sans:\n    system-ui, \"Segoe UI\", roboto, \"Noto Sans\", oxygen, ubuntu, cantarell,\n    \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", arial, sans-serif,\n    \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n  --font-family-mono:\n    ui-monospace, sfmono-regular, sf mono, jetbrainsmono, consolas, monospace;\n  --font-size-base: 1rem; // 16px\n  --font-size-sm: 0.875rem; // 14px\n  --font-size-xs: 0.75rem; // 12px\n  --font-size-monospace: 1em;\n  --font-bold-weight: 450;\n\n  // Line height\n  --line-height-base: 1.5;\n  --line-height-heading: 1.2;\n\n  // Color palette\n  --very-light-grey: #f8f8f8;\n  --light-grey: hsl(240 20% 96.5%);\n  --medium-grey: #9ba7af;\n  --dark-grey: #4d545d;\n\n  // branding\n  --secondary: oklch(from var(--black) 60% c h);\n  --focus-input-border: var(--accent-color);\n  --focus-input-glow: color-mix(in sRGB, var(--accent-color) 15%, transparent);\n\n  // State colors\n  --primary-hover: var(--accent-color);\n  --primary-active: var(--accent-color);\n\n  // Status icon colors\n  --weather-icon-color: var(--accent-color);\n  --unstable-build-icon-color: var(--orange);\n\n  // Background colors\n  --background: var(--white);\n\n  // Header\n  --brand-link-color: var(--secondary);\n  --header-link-color: var(--white);\n  --header-bg-classic: var(--black);\n  --header-link-bg-classic-hover: #404040;\n  --header-link-bg-classic-active: #404040;\n\n  // Breadcrumbs bar\n  --breadcrumbs-bar-background: oklch(\n    from var(--text-color) 96.8% 0.005 h / 0.8\n  );\n\n  // App bar\n  --bottom-app-bar-shadow: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 7.5%,\n    transparent\n  );\n\n  // Alert call outs\n  --alert-success-text-color: var(--success-color);\n  --alert-success-bg-color: color-mix(\n    in sRGB,\n    var(--success-color) 10%,\n    transparent\n  );\n  --alert-success-border-color: color-mix(\n    in sRGB,\n    var(--success-color) 5%,\n    transparent\n  );\n  --alert-info-text-color: var(--blue);\n  --alert-info-bg-color: color-mix(in sRGB, var(--blue) 10%, transparent);\n  --alert-info-border-color: color-mix(in sRGB, var(--blue) 5%, transparent);\n  --alert-warning-text-color: color-mix(\n    in sRGB,\n    var(--warning-color) 80%,\n    var(--text-color)\n  );\n  --alert-warning-bg-color: color-mix(\n    in sRGB,\n    var(--warning-color) 10%,\n    transparent\n  );\n  --alert-warning-border-color: color-mix(\n    in sRGB,\n    var(--warning-color) 5%,\n    transparent\n  );\n  --alert-danger-text-color: var(--error-color);\n  --alert-danger-bg-color: color-mix(\n    in sRGB,\n    var(--error-color) 10%,\n    transparent\n  );\n  --alert-danger-border-color: color-mix(\n    in sRGB,\n    var(--error-color) 5%,\n    transparent\n  );\n\n  // Typography\n  --text-color-secondary: var(--secondary);\n\n  // Borders\n  --jenkins-border-width: 1.5px;\n  --jenkins-border-color: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 15%,\n    var(--card-background)\n  );\n  --jenkins-border-color--subtle: color-mix(\n    in sRGB,\n    currentColor 1.5%,\n    transparent\n  );\n\n  /* This is a harsher border - for dividers, content blocks and more */\n  --jenkins-border: var(--jenkins-border-width) solid\n    var(--jenkins-border-color);\n\n  /* This is a subtle border - for increasing contrast on elements, such as buttons, menu and more */\n  --jenkins-border--subtle: var(--jenkins-border-width) solid\n    var(--jenkins-border-color--subtle);\n  --jenkins-border--subtle-shadow: 0 0 0 1.5px\n    var(--jenkins-border-color--subtle);\n\n  @media (resolution <= 1x) {\n    --jenkins-border-width: 2px;\n  }\n\n  @media (prefers-contrast: more) {\n    --focus-input-border: var(--text-color);\n    --jenkins-border-color: var(--text-color);\n    --jenkins-border-color--subtle: var(--text-color);\n  }\n\n  // Table\n  --table-background: oklch(from var(--text-color-secondary) l c h / 0.075);\n  --table-header-foreground: var(--text-color);\n  --table-body-background: var(--background);\n  --table-body-foreground: var(--text-color);\n  --table-border-radius: 0.75rem;\n  --table-row-border-radius: 0.3125rem;\n\n  // Deprecated\n  --even-row-color: var(--very-light-grey);\n  --bigtable-border-width: var(--pane-border-width);\n  --bigtable-header-bg: var(--dark-grey);\n  --bigtable-header-font-weight: bold; // Does specifying this make sense\n  --bigtable-header-text-color: var(--white);\n  --bigtable-row-border-color: var(--medium-grey);\n  --bigtable-cell-padding-x: 0.75rem;\n  --bigtable-cell-padding-y: 0.5rem;\n  --table-parameters-bg--hover: var(--light-grey);\n  --table-striped-bg--hover: var(--light-grey);\n\n  // Link\n  --link-color: var(--accent-color);\n  --link-visited-color: var(--link-color);\n  --link-color--hover: var(--link-color);\n  --link-color--active: var(--link-color);\n  --link-text-decoration: none;\n  --link-text-decoration--hover: underline;\n  --link-text-decoration--active: underline;\n  --link-font-weight: var(--font-bold-weight);\n\n  // Command Palette\n  --command-palette-results-backdrop-filter: saturate(1.5) blur(5px);\n  --command-palette-inset-shadow:\n    inset 0 0 2px 2px rgb(255 255 255 / 0.1),\n    var(--jenkins-border--subtle-shadow),\n    0 5px 10px var(--jenkins-border-color--subtle);\n\n  ::backdrop {\n    --command-palette-backdrop-background: color-mix(\n      in sRGB,\n      var(--black) 17.5%,\n      transparent\n    );\n  }\n\n  // Tooltips\n  --tooltip-backdrop-filter: contrast(1.1) saturate(2) blur(20px);\n  --tooltip-color: var(--text-color);\n  --tooltip-box-shadow:\n    0 0 8px 2px rgb(0 0 50 / 0.05), var(--jenkins-border--subtle-shadow),\n    0 10px 50px rgb(0 0 20 / 0.1), inset 0 -1px 2px rgb(255 255 255 / 0.025);\n\n  // Dropdowns\n  --dropdown-backdrop-filter: contrast(1.1) saturate(2) blur(20px);\n  --dropdown-box-shadow:\n    var(--jenkins-border--subtle-shadow), 0 10px 30px rgb(0 0 20 / 0.1),\n    0 2px 10px rgb(0 0 20 / 0.05), inset 0 -1px 2px rgb(255 255 255 / 0.025);\n\n  // Dialogs\n  ::backdrop {\n    --dialog-backdrop-background: hsl(240 10% 20% / 0.8);\n  }\n\n  --dialog-box-shadow:\n    var(--jenkins-border--subtle-shadow), 0 10px 40px rgb(0 0 20 / 0.15),\n    0 2px 15px rgb(0 0 20 / 0.05), inset 0 0 2px 2px rgb(255 255 255 / 0.025);\n\n  // Dark link\n  --link-dark-color: var(--text-color);\n  --link-dark-visited-color: var(--link-dark-color);\n  --link-dark-color--hover: var(--primary-hover);\n  --link-dark-color--active: var(--primary-active);\n  --link-dark-text-decoration: none;\n  --link-dark-text-decoration--hover: underline;\n  --link-dark-text-decoration--active: underline;\n  --link-dark-font-weight: var(--font-bold-weight);\n\n  // Pane\n  --pane-border-width: 1px;\n  --pane-header-text-color: var(--text-color);\n  --pane-header-bg: var(--light-grey);\n  --pane-header-border-color: var(--light-grey);\n  --pane-header-font-weight: bold;\n  --pane-border-color: var(--light-grey);\n  --pane-text-color: var(--text-color);\n  --pane-link-color: black;\n  --pane-link-color--visited: black;\n\n  // Cards\n  --card-background: var(--background);\n  --card-background--hover: transparent;\n  --card-background--active: transparent;\n  --card-border-color: oklch(from var(--text-color-secondary) l c h / 0.15);\n  --card-border-color--hover: oklch(\n    from var(--text-color-secondary) l c h / 0.3\n  );\n  --card-border-color--active: oklch(\n    from var(--text-color-secondary) l c h / 0.5\n  );\n  --card-border-width: var(--jenkins-border-width);\n\n  @media (prefers-contrast: more) {\n    --card-border-color: var(--text-color);\n  }\n\n  // Tab bar\n  --tabs-background: oklch(from var(--text-color-secondary) l c h / 0.1);\n  --tabs-item-background: transparent;\n  --tabs-item-foreground: color-mix(\n    in sRGB,\n    var(--text-color-secondary),\n    var(--text-color)\n  );\n  --tabs-item-background--hover: rgb(0 0 0 / 0.05);\n  --tabs-item-foreground--hover: var(--text-color);\n  --tabs-item-background--active: rgb(0 0 0 / 0.1);\n  --tabs-item-foreground--active: var(--text-color);\n  --tabs-item-background--selected: white;\n  --tabs-item-foreground--selected: var(--link-color);\n  --tabs-border-radius: calc((10px + 34px) / 2);\n\n  // Side panel\n  --side-panel-width: 340px;\n  --panel-header-bg-color: var(--light-grey);\n  --panel-border-color: var(--light-grey);\n\n  // Form\n  --section-padding: 1.625rem;\n  --input-color: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 1.5%,\n    var(--background)\n  );\n  --input-border: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 25%,\n    transparent\n  );\n  --input-border-hover: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 50%,\n    transparent\n  );\n  --input-hidden-password-bg-color: #f9f9f9;\n  --form-item-max-width: min(65vw, 1600px);\n  --form-item-max-width--medium: min(50vw, 1400px);\n  --form-item-max-width--small: min(35vw, 1200px);\n\n  @media screen and (max-width: breakpoints.$tablet-breakpoint) {\n    --section-padding: 1.25rem;\n    --form-item-max-width: 100%;\n    --form-item-max-width--medium: 100%;\n    --form-item-max-width--small: 100%;\n  }\n\n  --form-label-font-weight: var(--font-bold-weight);\n  --form-input-padding: 0.5rem 0.625rem;\n  --form-input-border-radius: 0.625rem;\n  --form-input-glow: 0 0 0 0.5rem transparent;\n  --form-input-glow--focus: 0 0 0 0.25rem var(--focus-input-glow);\n  --pre-background: var(--button-background);\n  --pre-color: var(--text-color);\n  --selection-color: oklch(from var(--accent-color) l c h / 0.2);\n\n  @media (prefers-contrast: more) {\n    --input-border: var(--text-color) !important;\n    --input-border-hover: var(--text-color) !important;\n    --form-input-glow--focus: 0 0 0 4px\n      color-mix(in sRGB, var(--text-color), transparent);\n  }\n\n  // Animations\n  --standard-transition: 0.25s ease;\n  --elastic-transition: 0.3s cubic-bezier(0, 0.68, 0.5, 1.5);\n\n  // Plugin manager\n  --plugin-manager-bg-color-already-upgraded: var(--light-grey);\n\n  // Default button\n  --button-background: oklch(from var(--text-color-secondary) l c h / 0.075);\n  --button-background--hover: oklch(\n    from var(--text-color-secondary) l c h / 0.125\n  );\n  --button-background--active: oklch(\n    from var(--text-color-secondary) l c h / 0.175\n  );\n  --button-box-shadow--focus: oklch(\n    from var(--text-color-secondary) l c h / 0.1\n  );\n  --button-color--primary: var(--background);\n\n  // Variables for sidebar items, card items\n  --item-background--hover: oklch(from var(--text-color-secondary) l c h / 0.1);\n  --item-background--active: oklch(\n    from var(--text-color-secondary) l c h / 0.15\n  );\n  --item-box-shadow--focus: oklch(from var(--text-color-secondary) l c h / 0.1);\n\n  // Deprecated\n  --primary: var(--accent-color); // Use var(--accent-color) instead\n  --success: var(--green); // Use var(--success-color) instead\n  --danger: var(--red); // Use var(--destructive-color) instead\n  --warning: var(--orange); // Use var(--warning-color) instead\n\n  // Colors\n  @each $key, $value in $colors {\n    --#{$key}: #{$value};\n\n    @if $key != \"black\" and $key != \"white\" {\n      --light-#{$key}: #{color.adjust($value, $lightness: 20%)};\n      --dark-#{$key}: #{color.adjust($value, $lightness: -20%)};\n    }\n  }\n\n  @each $key, $value in $semantics {\n    --#{$key}-color: #{$value};\n  }\n}\n", "@use \"theme\";\n\n// Generates a series of color override classes and their variations,\n// e.g. .jenkins-!-color-blue, .jenkins-!-color-light-blue, .jenkins-!-color-dark-blue\n@each $key, $value in theme.$colors {\n  .jenkins-\\!-color-light-#{$key} {\n    --color: var(--light-#{$key});\n\n    color: var(--light-#{$key}) !important;\n  }\n  .jenkins-\\!-color-#{$key} {\n    --color: var(--#{$key});\n\n    color: var(--#{$key}) !important;\n  }\n  .jenkins-\\!-color-dark-#{$key} {\n    --color: var(--dark-#{$key});\n\n    color: var(--dark-#{$key}) !important;\n  }\n}\n\n@each $key, $value in theme.$semantics {\n  .jenkins-\\!-#{$key}-color {\n    --color: #{$value};\n\n    color: #{$value} !important;\n  }\n}\n\n// Deprecated - don't use the below classes\n.greyed {\n  color: #999;\n}\n\n.redbold {\n  color: var(--red);\n  font-weight: bold;\n}\n\n.greenbold {\n  color: var(--green);\n  font-weight: bold;\n}\n", "html {\n  height: 100%;\n  box-sizing: border-box;\n  -webkit-tap-highlight-color: transparent;\n  color: var(--text-color);\n}\n\nbody {\n  margin: 0;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--background);\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\n::selection {\n  background: var(--selection-color);\n}\n", ".jen<PERSON>-\\!-display-contents {\n  display: contents;\n}\n", "@use \"../base/breakpoints\";\n\n/* --------------- header --------------- */\n\n#page-header .logo {\n  margin-left: 1.2rem;\n  display: inline-flex;\n  align-items: center;\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n#jenkins-home-link {\n  position: relative;\n}\n\n#jenkins-head-icon {\n  height: 2.5rem;\n}\n\n#jenkins-name-icon {\n  margin-left: 0.25rem;\n}\n\n/* -------------------------------------- */\n\n.app-page-body {\n  display: flex;\n  justify-content: flex-start;\n  align-items: stretch;\n  flex: 1 0 auto;\n}\n\n.app-page-body__sidebar {\n  @media (min-width: breakpoints.$tablet-breakpoint) {\n    &--sticky {\n      position: sticky;\n      top: 44px;\n      align-self: flex-start;\n    }\n  }\n}\n\n#page-body.clear::after {\n  clear: both;\n  content: \"\";\n  display: table;\n}\n\n#side-panel {\n  flex-shrink: 0;\n}\n\n#main-panel {\n  padding: var(--section-padding);\n  display: inline-block;\n  width: 100%;\n}\n\n.app-page-body--one-column {\n  --form-item-max-width: calc(85vw - 4rem);\n\n  @media screen and (width <= 1200px) {\n    --form-item-max-width: 100%;\n  }\n\n  max-width: 85vw;\n\n  #main-panel {\n    width: 100vw;\n  }\n\n  .jenkins-section {\n    max-width: unset;\n  }\n\n  @media (width <= 1200px) {\n    max-width: unset;\n  }\n\n  margin: auto;\n}\n\nbody.two-column #main-panel {\n  width: calc(100% - var(--side-panel-width));\n  flex: 1;\n  display: block;\n}\n\nbody.full-screen {\n  padding: 0;\n}\n\nbody.full-screen #main-panel {\n  padding: 0;\n}\n\nbody.two-column #side-panel {\n  width: var(--side-panel-width);\n}\n\n@media (max-width: breakpoints.$tablet-breakpoint) {\n  body.two-column #page-body {\n    flex-wrap: wrap;\n  }\n\n  body.two-column #side-panel {\n    width: 100%;\n    border-right: none;\n  }\n\n  body.two-column #main-panel {\n    margin-left: 0;\n    width: 100%;\n  }\n}\n\n.app-project-status-table {\n  width: 100%;\n  overflow-x: auto;\n}\n\n@media (width >= 1170px) {\n  body.two-column #main-panel {\n    width: calc(\n      100% - calc(var(--side-panel-width) + calc(var(--section-padding) * 2))\n    );\n  }\n}\n\n// Clearfixes extracted from responsive-grid.css as they essential\n.clearfix::before,\n.clearfix::after,\n.container::before,\n.container::after,\n.container-fluid::before,\n.container-fluid::after,\n.row::before,\n.row::after,\n.form-horizontal .form-group::before,\n.form-horizontal .form-group::after,\n.btn-toolbar::before,\n.btn-toolbar::after,\n.btn-group-vertical > .btn-group::before,\n.btn-group-vertical > .btn-group::after,\n.nav::before,\n.nav::after,\n.navbar::before,\n.navbar::after,\n.navbar-header::before,\n.navbar-header::after,\n.navbar-collapse::before,\n.navbar-collapse::after,\n.pager::before,\n.pager::after,\n.panel-body::before,\n.panel-body::after,\n.modal-footer::before,\n.modal-footer::after {\n  display: table;\n  content: \" \";\n}\n\n.clearfix::after,\n.container::after,\n.container-fluid::after,\n.row::after,\n.form-horizontal .form-group::after,\n.btn-toolbar::after,\n.btn-group-vertical > .btn-group::after,\n.nav::after,\n.navbar::after,\n.navbar-header::after,\n.navbar-collapse::after,\n.pager::after,\n.panel-body::after,\n.modal-footer::after {\n  clear: both;\n}\n", "$unit: 0.4rem;\n\n@for $i from 0 through 6 {\n  .jenkins-\\!-margin-#{$i} {\n    margin: $i * $unit !important;\n  }\n\n  .jenkins-\\!-margin-top-#{$i} {\n    margin-top: $i * $unit !important;\n  }\n\n  .jenkins-\\!-margin-left-#{$i} {\n    margin-left: $i * $unit !important;\n  }\n\n  .jenkins-\\!-margin-bottom-#{$i} {\n    margin-bottom: $i * $unit !important;\n  }\n\n  .jenkins-\\!-margin-right-#{$i} {\n    margin-right: $i * $unit !important;\n  }\n}\n\n.jenkins-\\!-margin-auto {\n  margin: 0 auto;\n}\n\n@for $i from 0 through 6 {\n  .jenkins-\\!-padding-#{$i} {\n    padding: $i * $unit !important;\n  }\n\n  .jenkins-\\!-padding-top-#{$i} {\n    padding-top: $i * $unit !important;\n  }\n\n  .jenkins-\\!-padding-left-#{$i} {\n    padding-left: $i * $unit !important;\n  }\n\n  .jenkins-\\!-padding-bottom-#{$i} {\n    padding-bottom: $i * $unit !important;\n  }\n\n  .jenkins-\\!-padding-right-#{$i} {\n    padding-right: $i * $unit !important;\n  }\n}\n", "/*\n * The MIT License\n *\n * Copyright (c) 2004-2010, Sun Microsystems, Inc., <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nform {\n  margin: 0;\n}\n\ntd {\n  vertical-align: top;\n}\n\ndt {\n  font-weight: bold;\n}\n\n.fixed-width {\n  font-family: monospace;\n}\n\n.center {\n  text-align: center;\n}\n\n.middle-align td,\ntd.middle-align {\n  vertical-align: middle;\n}\n\n.center-align td,\ntd.center-align {\n  text-align: center;\n}\n\n.no-wrap td,\ntd.no-wrap {\n  white-space: nowrap;\n}\n\n#main-table {\n  padding: 0;\n  border-collapse: collapse;\n}\n\n#safe-restart-msg {\n  font-weight: bold;\n  color: white;\n  background-color: var(--warning);\n  text-align: center;\n  margin-bottom: var(--section-padding);\n  padding: 0.5em;\n  -moz-border-radius: 0.5em;\n  border-radius: var(--form-input-border-radius);\n}\n\n#shutdown-msg {\n  font-weight: bold;\n  color: white;\n  background-color: #ef2929;\n  text-align: center;\n  margin-bottom: var(--section-padding);\n  padding: 0.5em;\n  -moz-border-radius: 0.5em;\n  border-radius: var(--form-input-border-radius);\n}\n\na.lowkey:link {\n  text-decoration: none;\n  color: inherit;\n}\n\na.lowkey:hover {\n  text-decoration: underline;\n  color: inherit;\n}\n\na.lowkey:visited {\n  text-decoration: none;\n  color: inherit;\n}\n\n/* tip - anchors of class info */\na.tip {\n  position: relative;\n  z-index: 24;\n  text-decoration: underline;\n}\n\na.tip:hover {\n  z-index: 25;\n}\n\na.tip span {\n  display: none;\n}\n\na.tip:hover span {\n  display: block;\n  position: absolute;\n  top: 2em;\n  left: 2em;\n  width: 400px;\n  border: 1px solid #bbb;\n  background-color: #fffff0;\n  color: #000;\n  text-align: left;\n}\n\nimg {\n  vertical-align: middle;\n  border: 0;\n}\n\ndiv.disabled {\n  opacity: 0.4;\n  background-color: #000;\n}\n\ntable.tab {\n  border-collapse: collapse;\n}\n\ntd.tab {\n  vertical-align: middle;\n  border: 1px #090 solid;\n  background: #f0f0f0;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  margin: 0 0 var(--section-padding);\n  padding: 0.8rem 1rem;\n  border-radius: var(--form-input-border-radius);\n  border: var(--jenkins-border--subtle);\n  background-color: var(--pre-background);\n  color: var(--pre-color);\n  font-family: var(--font-family-mono);\n  font-weight: 400;\n  line-height: 1.66;\n\n  a {\n    word-wrap: break-word;\n  }\n}\n\npre.jenkins-readonly {\n  margin-bottom: 0 !important;\n}\n\npre.console {\n  overflow: auto;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n\n  &::after {\n    content: \" \"; /* Older browser do not support empty content */\n    visibility: hidden;\n    display: block;\n    height: 0;\n    clear: both;\n  }\n}\n\n.setting-leftspace {\n  width: 2em;\n}\n\n.setting-name {\n  font-weight: var(--font-bold-weight);\n  margin-bottom: 0.5rem;\n  white-space: nowrap;\n}\n\n.setting-help {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n  width: 16px;\n  vertical-align: middle;\n  float: right;\n}\n\n.setting-no-help {\n  width: 16px;\n}\n\n.setting-input {\n  width: 100%;\n  border-radius: 3px;\n  border: 1px solid var(--input-border);\n  box-shadow: inset 0 1px 1px rgb(0 0 0 / 0.075);\n  padding: 6px;\n  box-sizing: border-box;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n}\n\n.setting-description {\n  font-size: var(--font-size-xs);\n  margin-top: 0;\n  padding-top: 0;\n}\n\n.setting-name,\n.setting-main > input:not([type=\"file\"]),\n.setting-main > textarea {\n  vertical-align: middle;\n  margin-top: 0;\n}\n\n.expanding-input {\n  display: flex;\n  align-items: center;\n}\n\n.expanding-input__input {\n  flex: 1;\n}\n\n.expanding-input__button {\n  flex-shrink: 1;\n}\n\n.advancedBody {\n  display: none;\n}\n\n.jenkins-not-applicable {\n  color: darkgrey;\n  font-style: italic;\n}\n\n.changeset-message {\n  padding: 0.8rem 1rem;\n  border-radius: var(--form-input-border-radius);\n  background-color: var(--pre-background);\n\n  pre {\n    padding: 0 0 0.1rem;\n    margin-bottom: 0;\n    background-color: transparent;\n  }\n}\n\n.disabledJob {\n  color: gray;\n}\n\n.icon16x16 {\n  width: 16px;\n  height: 16px;\n}\n\n.icon24x24 {\n  width: 24px;\n  height: 24px;\n}\n\n.icon32x32 {\n  width: 32px;\n  height: 32px;\n}\n\n/* ====================== help ===================================== */\n\n.help {\n  position: relative;\n  display: none; /* hidden until loaded */\n  padding: 1rem;\n  margin: 1rem 0;\n  word-break: break-word;\n  border-radius: var(--form-input-border-radius);\n  background-color: var(--button-background);\n  border: var(--jenkins-border--subtle);\n  z-index: 0;\n\n  --section-padding: 0.8rem;\n\n  p:first-of-type {\n    margin-top: 0;\n  }\n\n  p:last-of-type {\n    margin-bottom: 0;\n  }\n\n  // add spacing above headings except for when its the first element in the help\n  // the need for this is caused by p:last-of-type setting margin-bottom to 0\n  // unfortunately because of the varied markup I wasn't able to find a way to avoid this\n  h1:not(:first-child),\n  .h1:not(:first-child),\n  h2:not(:first-child),\n  .h2:not(:first-child),\n  h3:not(:first-child),\n  .h3:not(:first-child),\n  h4:not(:first-child),\n  .h4:not(:first-child),\n  h5:not(:first-child),\n  .h5:not(:first-child),\n  h6:not(:first-child),\n  .h6:not(:first-child) {\n    margin-top: var(--section-padding);\n  }\n}\n\n.help .from-plugin {\n  text-align: right;\n  color: #666;\n}\n\n.icon-help,\n.svg-icon.icon-help {\n  height: 1.25rem;\n  width: 1.25rem;\n}\n\nimg.icon-help {\n  vertical-align: text-top;\n}\n\n/* ============================ health report hover ========================== */\n\n.healthReport a {\n  text-decoration: none;\n}\n\n#side-panel .healthReport a {\n  height: 100%;\n}\n\n.healthReport div.healthReportDetails {\n  display: none;\n  margin-left: 20px;\n  padding: 0;\n\n  --table-border-radius: 7px;\n\n  table {\n    border-radius: 0;\n    width: 450px;\n    margin-bottom: 0;\n    box-sizing: content-box;\n    padding-bottom: 2px;\n\n    * {\n      box-sizing: border-box;\n    }\n  }\n}\n\n.healthReport:hover div.healthReportDetails,\n.healthReport.hover div.healthReportDetails {\n  display: block;\n}\n\n/* ========================= editable combobox style ========================= */\n.comboBoxList {\n  overflow-y: scroll;\n  color: var(--text-color);\n  border-radius: var(--form-input-border-radius);\n  box-shadow: 0 10px 20px rgba(black, 0.15);\n  margin-top: 8px;\n  margin-left: 3px;\n  max-height: 300px;\n  z-index: 1000;\n  background: var(--background);\n  padding: 0.3rem;\n}\n\n.comboBoxItem {\n  position: relative;\n  padding: 0.5rem 0.8rem;\n  cursor: pointer;\n  font-weight: var(--font-bold-weight);\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    background: var(--text-color);\n    border-radius: var(--form-input-border-radius);\n    opacity: 0;\n    transition: var(--standard-transition);\n    z-index: -1;\n  }\n\n  &:hover {\n    &::before {\n      opacity: 0.1;\n    }\n  }\n\n  &:active,\n  &:focus {\n    &::before {\n      opacity: 0.2;\n    }\n  }\n}\n\n.combobox-values {\n  display: none;\n}\n\n/* ========================= directory tree ========================= */\n.parentPath form {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n\n  .jenkins-input {\n    display: inline-flex;\n    width: 300px;\n  }\n}\n\n.dirTree li {\n  list-style: none;\n}\n\n.dirTree .rootIcon {\n  margin-right: 1em;\n}\n\ntable.fileList {\n  padding: 0;\n}\n\ntable.fileList td:not(:first-of-type) {\n  padding: 0 1rem 0 0;\n  color: var(--text-color-secondary);\n\n  svg {\n    color: var(--link-color) !important;\n  }\n}\n\ntable.fileList td.fileSize {\n  text-align: right;\n}\n\n/* ========================= test result ========================= */\n.result-passed {\n  color: #3465a4;\n}\n\n.result-skipped {\n  color: #db0;\n}\n\n.result-fixed {\n  color: #3465a4;\n  font-weight: bold;\n}\n\n.result-failed {\n  color: #ef2929;\n}\n\n.result-regression {\n  color: #ef2929;\n  font-weight: bold;\n}\n\n.test-trend-caption {\n  text-align: center;\n  font-size: var(--font-size-base);\n  font-weight: bold;\n}\n\n/* ========================= fingerprint ========================= */\n.md5sum {\n  text-align: right;\n}\n\n.fingerprint-summary-header {\n  font-size: var(--font-size-base);\n  vertical-align: middle;\n}\n\ntable.fingerprint-in-build td {\n  padding-left: 1em;\n  padding-right: 1em;\n}\n\n.radioBlock-container {\n  margin-bottom: 0.875rem;\n\n  &:last-of-type {\n    margin-bottom: var(--section-padding);\n  }\n}\n\n.optionalBlock-container > .form-container,\n.radioBlock-container > .form-container,\n.jenkins-radio__children,\n.dropdownList-container {\n  position: relative;\n  padding-left: 32px;\n  transition: var(--standard-transition);\n  margin-top: calc(var(--section-padding) / 3);\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    left: 10px;\n    bottom: 0;\n    width: var(--jenkins-border-width);\n    background: var(--input-border);\n    border-radius: 2px;\n    transition: var(--standard-transition);\n  }\n\n  &:empty {\n    display: none;\n  }\n}\n\n.dropdownList-container {\n  margin-bottom: var(--section-padding);\n}\n\n.form-container--hidden {\n  visibility: hidden !important;\n  margin: 0 !important;\n  opacity: 0 !important;\n  max-height: 0 !important;\n  height: 0 !important;\n  overflow: hidden;\n}\n\n.row-set-end {\n  display: none;\n}\n\n/* ========================= resizable text area ========================= */\n\ntextarea {\n  margin-bottom: 0;\n  resize: vertical;\n}\n\n/* ========================= progress bar ========================= */\n\ntable.progress-bar {\n  border-collapse: collapse;\n  border: 1px solid #3465a4;\n  height: 6px;\n  width: 100px;\n  clear: none;\n}\n\ntable.progress-bar tr.unknown {\n  background-image: url(\"../images/progress-unknown.gif\");\n}\n\ntd.progress-bar-done {\n  background-color: #3465a4;\n}\n\ntd.progress-bar-left {\n  background-color: #bababa;\n}\n\ntable.progress-bar.red {\n  border: 1px solid #c00;\n}\n\ntable.progress-bar.red tr.unknown {\n  background-image: url(\"../images/progress-unknown-red.gif\");\n}\n\ntable.progress-bar.red td.progress-bar-done {\n  background-color: #c00;\n}\n\n/* Unknown */\n\n[data-symbol-animation] {\n  animation: spin 1s linear infinite;\n  transform-origin: 0 0;\n\n  @media (prefers-reduced-motion) {\n    animation-duration: 3s;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes spin-reverse {\n  from {\n    transform: rotate(360deg);\n  }\n\n  to {\n    transform: rotate(0deg);\n  }\n}\n\n/* ========================= tags/labels ================== */\n// Used in core/src/main/java/hudson/util/TagCloud.java#getClassName\n\n/* tag0 is the least important tag in a tag cloud */\n.tag0 {\n  font-size: 1em;\n}\n\n.tag1 {\n  font-size: 1.1em;\n}\n\n.tag2 {\n  font-size: 1.2em;\n}\n\n.tag3 {\n  font-size: 1.3em;\n}\n\n.tag4 {\n  font-size: 1.4em;\n}\n\n.tag5 {\n  font-size: 1.5em;\n}\n\n.tag6 {\n  font-size: 1.6em;\n}\n\n.tag7 {\n  font-size: 1.7em;\n}\n\n.tag8 {\n  font-size: 1.8em;\n}\n\n.tag9 {\n  font-size: 1.9em;\n}\n\n/* ========================= logRecords.jelly ================== */\n\n.logrecord-container {\n  border-radius: var(--form-input-border-radius);\n  overflow: hidden;\n  margin-bottom: var(--section-padding);\n\n  pre {\n    padding-top: 0;\n    padding-bottom: 0.1rem;\n    margin-bottom: 0;\n    border-radius: 0;\n\n    &:first-of-type {\n      padding-top: 0.8rem;\n    }\n\n    &:last-of-type {\n      padding-bottom: 0.8rem;\n    }\n  }\n}\n\n.logrecord-metadata {\n  font-size: var(--font-size-xs);\n}\n\n.logrecord-metadata-new {\n  color: var(--green);\n}\n\n.logrecord-metadata-old {\n  color: var(--text-color-secondary);\n}\n\n/* ========================= matrix configuration table ================== */\ntable#configuration-matrix {\n  border: 1px var(--medium-grey) solid;\n  border-collapse: collapse;\n}\n\ntr.matrix-row {\n  background-color: var(--bigtable-header-bg);\n  color: var(--bigtable-header-text-color);\n  font-weight: var(--bigtable-header-font-weight);\n}\n\ntd.matrix-header {\n  border: 1px var(--medium-grey) solid;\n  padding: 3px;\n}\n\ntd#matrix-title {\n  padding: 3px;\n}\n\ntd.matrix-leftcolumn {\n  border: 1px var(--medium-grey) solid;\n  font-weight: bold;\n  background: var(--very-light-grey);\n  padding: 3px;\n}\n\ntd.matrix-cell {\n  border: 1px var(--medium-grey) solid;\n  text-align: center;\n}\n\n/* ========================= select.jelly ================== */\nselect.select-ajax-pending {\n  padding-left: 1.5em;\n  padding-top: 0.5em;\n  padding-bottom: 0.5em;\n  color: transparent;\n  background-image: url(\"../images/spinner.gif\"); /* this is why here and not in an adjunct */\n  background-repeat: no-repeat;\n  background-position: 2px;\n}\n\n/* ========================= Button styles ================= */\n.i18n {\n  display: none;\n}\n\nbody.no-decoration #main-panel {\n  margin: 0 auto !important;\n}\n\nbody.no-decoration #page-header,\nbody.no-decoration #side-panel,\nbody.no-decoration footer {\n  display: none;\n}\n\nbody.no-sticker #bottom-sticker {\n  display: none;\n}\n\n/* see the Icon class for the definition of these CSS classes */\n.icon-xs,\nsvg.icon-xs {\n  width: 12px;\n  height: 12px;\n  vertical-align: middle;\n\n  svg {\n    width: 12px;\n    height: 12px;\n  }\n}\n\n.icon-sm,\nsvg.icon-sm {\n  width: 16px;\n  height: 16px;\n  vertical-align: middle;\n\n  svg {\n    width: 16px;\n    height: 16px;\n  }\n}\n\n.icon-md,\nsvg.icon-md {\n  width: 24px;\n  height: 24px;\n  vertical-align: middle;\n\n  svg {\n    width: 24px;\n    height: 24px;\n  }\n}\n\n.icon-lg,\nsvg.icon-lg {\n  width: 32px;\n  height: 32px;\n  vertical-align: middle;\n\n  svg {\n    width: 32px;\n    height: 32px;\n  }\n}\n\n.icon-xlg,\nsvg.icon-xlg {\n  width: 48px;\n  height: 48px;\n  vertical-align: middle;\n\n  svg {\n    width: 48px;\n    height: 48px;\n  }\n}\n\n/* -------------------------------------- */\n\n/* -------------- SVG icons ------------- */\n\n.svg-icon {\n  display: inline-block;\n  vertical-align: middle;\n\n  /* default dimensions */\n  height: 24px;\n  width: 24px;\n\n  /* default fill fallback */\n  fill: var(--text-color);\n  fill: currentColor;\n}\n\n.jenkins-icon-adjacent {\n  margin-left: 0.5rem;\n  width: 100%;\n}\n\n/* -------------- Unclassified ---------- */\n\n.spacer {\n  height: var(--section-padding);\n}\n\n/* used by elements that are hidden by default but revealed throught JavaScript */\n.default-hidden {\n  display: none;\n}\n\n.app-summary {\n  & > td:first-of-type {\n    img,\n    svg {\n      width: 48px !important;\n      height: 48px !important;\n      margin-right: 1rem;\n    }\n  }\n}\n\n/* -------------------------------------- */\n", "@use \"../abstracts/mixins\";\n@use \"../base/breakpoints\";\n\nbody,\np {\n  font-family: var(--font-family-sans);\n  font-size: var(--font-size-sm);\n  line-height: var(--line-height-base);\n  color: var(--text-color);\n}\n\nbutton {\n  font-family: inherit;\n  font-size: inherit;\n}\n\ntable,\ntd,\nth,\nform {\n  font-size: var(--font-size-sm);\n}\n\ninput,\ntextarea,\nselect {\n  font-size: var(--font-size-sm);\n\n  @media (max-width: breakpoints.$tablet-breakpoint) {\n    font-size: var(--font-size-base);\n  }\n}\n\n// Reset monospaced font-size, because browsers reduce it by default to ~81%\npre,\ncode,\nkbd,\nsamp,\ntt {\n  font-size: var(--font-size-monospace);\n}\n\n/*\n * Headings\n */\n\nh1,\n.h1,\nh2,\n.h2,\nh3,\n.h3,\nh4,\n.h4,\nh5,\n.h5,\nh6,\n.h6 {\n  line-height: var(--line-height-heading);\n  font-weight: 600;\n  display: block;\n  margin-top: 0;\n  margin-bottom: var(--section-padding);\n}\n\nh1,\n.h1 {\n  font-size: 1.5rem;\n}\n\nh2,\n.h2 {\n  font-size: 1.375rem;\n}\n\nh3,\n.h3 {\n  font-size: 1.1875rem;\n}\n\nh4,\n.h4 {\n  font-size: 1rem;\n}\n\nh5,\n.h5 {\n  font-size: 0.8125rem;\n}\n\nh6,\n.h6 {\n  font-size: 0.625rem;\n}\n\n.jenkins-description {\n  color: var(--text-color-secondary);\n  line-height: 1.66;\n  margin-top: 0;\n  margin-bottom: var(--section-padding);\n}\n\n.jenkins-label {\n  &--tertiary {\n    color: var(--text-color-secondary);\n    opacity: 0.7;\n  }\n}\n\na {\n  @include mixins.link;\n}\n\n.jenkins-link--with-icon {\n  display: inline-flex;\n  align-items: center;\n  justify-content: flex-start;\n\n  svg {\n    width: 16px;\n    height: 16px;\n    color: var(--text-color) !important;\n  }\n}\n", "@mixin link {\n  text-decoration: var(--link-text-decoration);\n  font-weight: var(--link-font-weight);\n  text-underline-offset: 2px;\n  text-decoration-thickness: 2px;\n\n  &:link {\n    color: var(--link-color);\n  }\n\n  &:visited {\n    color: var(--link-visited-color);\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--link-color--hover);\n    text-decoration: var(--link-text-decoration--hover);\n  }\n\n  &:active {\n    color: var(--link-color--active);\n    text-decoration: var(--link-text-decoration--active);\n  }\n\n  @media (prefers-contrast: more) {\n    text-decoration: underline;\n\n    &:hover {\n      text-decoration-thickness: 3px;\n    }\n  }\n}\n\n@mixin link-dark {\n  text-decoration: var(--link-dark-text-decoration);\n  font-weight: var(--link-dark-font-weight);\n\n  &:link {\n    color: var(--link-dark-color);\n  }\n\n  &:visited {\n    color: var(--link-dark-visited-color);\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--link-dark-color--hover);\n    text-decoration: var(--link-dark-text-decoration--hover);\n  }\n\n  &:active {\n    color: var(--link-dark-color--active);\n    text-decoration: var(--link-dark-text-decoration--active);\n  }\n}\n\n@mixin item($border: true) {\n  position: relative;\n  appearance: none;\n  z-index: 0;\n  text-decoration: none !important;\n  font-weight: normal;\n  border-radius: var(--form-input-border-radius);\n  cursor: pointer;\n  background: transparent;\n  outline: none;\n  border: none;\n\n  &::before,\n  &::after {\n    position: absolute;\n    content: \"\";\n    inset: 0;\n    z-index: -1;\n    border-radius: inherit;\n    transition: var(--standard-transition);\n    pointer-events: none;\n  }\n\n  &::before {\n    background-color: var(--item-background);\n    border: var(--jenkins-border--subtle);\n  }\n\n  &::after {\n    box-shadow: 0 0 0 0.5rem transparent;\n  }\n\n  &:focus-visible {\n    outline: none;\n  }\n\n  &:not(:disabled) {\n    &:hover,\n    &:focus-visible,\n    &[aria-describedby],\n    &[aria-expanded=\"true\"] {\n      &::before {\n        background-color: var(--item-background--hover);\n      }\n    }\n\n    &:active {\n      outline: none !important;\n      z-index: 1;\n\n      &::before {\n        background-color: var(--item-background--active);\n      }\n\n      &::after {\n        box-shadow: 0 0 0 0.25rem var(--item-box-shadow--focus);\n      }\n    }\n\n    &:focus-visible {\n      &::after {\n        box-shadow: 0 0 0 0.2rem var(--text-color) !important;\n        opacity: 1 !important;\n      }\n    }\n  }\n\n  @if $border == false {\n    &:not(:hover, &:active, &:focus) {\n      &::before {\n        border-color: transparent;\n      }\n    }\n  }\n}\n", "/**\n * Visibility utils are derived from bootstrap 3, they should do not\n * have a conflict with bootstrap 4 utils.\n *\n * They are essential, so they are extracted from responsive-grid.css\n * and declared here.\n */\n\n.visible-xs,\n.visible-sm,\n.visible-md,\n.visible-lg {\n  display: none !important;\n}\n\n@media (width <= 767px) {\n  .visible-xs {\n    display: block !important;\n  }\n\n  table.visible-xs {\n    display: table;\n  }\n\n  tr.visible-xs {\n    display: table-row !important;\n  }\n\n  th.visible-xs,\n  td.visible-xs {\n    display: table-cell !important;\n  }\n}\n\n@media (768px <= width <= 991px) {\n  .visible-sm {\n    display: block !important;\n  }\n\n  table.visible-sm {\n    display: table;\n  }\n\n  tr.visible-sm {\n    display: table-row !important;\n  }\n\n  th.visible-sm,\n  td.visible-sm {\n    display: table-cell !important;\n  }\n}\n\n@media (992px <= width <= 1199px) {\n  .visible-md {\n    display: block !important;\n  }\n\n  table.visible-md {\n    display: table;\n  }\n\n  tr.visible-md {\n    display: table-row !important;\n  }\n\n  th.visible-md,\n  td.visible-md {\n    display: table-cell !important;\n  }\n}\n\n@media (width >= 1200px) {\n  .visible-lg {\n    display: block !important;\n  }\n\n  table.visible-lg {\n    display: table;\n  }\n\n  tr.visible-lg {\n    display: table-row !important;\n  }\n\n  th.visible-lg,\n  td.visible-lg {\n    display: table-cell !important;\n  }\n}\n\n@media (width <= 767px) {\n  .hidden-xs {\n    display: none !important;\n  }\n}\n\n@media (768px <= width <= 991px) {\n  .hidden-sm {\n    display: none !important;\n  }\n}\n\n@media (992px <= width <= 1199px) {\n  .hidden-md {\n    display: none !important;\n  }\n}\n\n@media (width >= 1200px) {\n  .hidden-lg {\n    display: none !important;\n  }\n}\n\n.visible-print {\n  display: none !important;\n}\n\n@media print {\n  .visible-print {\n    display: block !important;\n  }\n\n  table.visible-print {\n    display: table;\n  }\n\n  tr.visible-print {\n    display: table-row !important;\n  }\n\n  th.visible-print,\n  td.visible-print {\n    display: table-cell !important;\n  }\n}\n\n@media print {\n  .hidden-print {\n    display: none !important;\n  }\n}\n\n.jenkins-hidden {\n  display: none !important;\n}\n\n/// Hide an element visually, but have it available for screen readers\n.jenkins-visually-hidden {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n\n  // If margin is set to a negative value it can cause text to be announced in\n  // the wrong order in VoiceOver for OSX\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  clip-path: inset(50%);\n  border: 0;\n\n  // For long content, line feeds are not interpreted as spaces and small width\n  // causes content to wrap 1 word per line:\n  // https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe\n  white-space: nowrap;\n}\n\n.jenkins-mobile-show {\n  @media (width >= 767px) {\n    display: none !important;\n  }\n}\n\n.jenkins-mobile-hide {\n  @media (width <= 767px) {\n    display: none !important;\n  }\n}\n", ".alert,\n.jenkins-alert {\n  font-size: var(--font-size-sm);\n  padding: 15px;\n  margin-bottom: var(--section-padding);\n  border: var(--jenkins-border-width) solid transparent;\n  border-radius: var(--form-input-border-radius);\n\n  strong {\n    font-weight: var(--font-bold-weight);\n  }\n\n  a {\n    color: inherit;\n    text-decoration: underline;\n\n    &:hover,\n    &:focus,\n    &:active {\n      text-decoration: underline;\n    }\n  }\n\n  &-success {\n    color: var(--alert-success-text-color);\n    background-color: var(--alert-success-bg-color);\n    border-color: var(--alert-success-border-color);\n  }\n\n  &-info {\n    color: var(--alert-info-text-color);\n    background-color: var(--alert-info-bg-color);\n    border-color: var(--alert-info-border-color);\n  }\n\n  &-warning {\n    color: var(--alert-warning-text-color);\n    background-color: var(--alert-warning-bg-color);\n    border-color: var(--alert-warning-border-color);\n\n    p {\n      color: var(--alert-warning-text-color);\n    }\n  }\n\n  &-danger {\n    color: var(--alert-danger-text-color);\n    background-color: var(--alert-danger-bg-color);\n    border-color: var(--alert-danger-border-color);\n\n    p {\n      color: var(--alert-danger-text-color);\n    }\n  }\n\n  @media (prefers-contrast: more) {\n    border-color: var(--text-color);\n  }\n}\n", ".jenkins-app-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--section-padding);\n  gap: var(--section-padding);\n  flex-wrap: wrap;\n\n  @media (width <= 800px) {\n    align-items: stretch;\n    flex-direction: column;\n  }\n\n  .jenkins-app-bar__content {\n    display: flex;\n    justify-content: center;\n    flex-direction: column;\n    min-height: 2.25rem;\n    flex-grow: 1;\n  }\n\n  .jenkins-app-bar__controls {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 0.75rem;\n\n    .jenkins-search {\n      min-width: 260px;\n    }\n\n    @media (width <= 800px) {\n      justify-content: stretch;\n      flex-wrap: wrap;\n\n      & > * {\n        flex-grow: 1;\n        flex-basis: 0;\n      }\n\n      .jenkins-button:first-child {\n        width: 100%;\n        flex-basis: auto !important;\n      }\n    }\n\n    &:empty {\n      display: none;\n    }\n  }\n\n  &--sticky {\n    position: sticky;\n    top: 40px;\n    padding-top: var(--section-padding);\n    margin-top: calc(var(--section-padding) * -1);\n    z-index: 2;\n\n    &::before,\n    &::after {\n      content: \"\";\n      position: absolute;\n      inset: 0 calc(var(--section-padding) * -1)\n        calc(var(--section-padding) * -1);\n      z-index: -1;\n      pointer-events: none;\n    }\n\n    &::before {\n      background: var(--background);\n      mask-image: linear-gradient(black 70%, transparent);\n      opacity: 0.55;\n\n      @supports not (backdrop-filter: blur(15px)) {\n        opacity: 1;\n      }\n    }\n\n    &::after {\n      backdrop-filter: blur(15px);\n      mask-image: linear-gradient(black 50%, transparent);\n    }\n  }\n\n  h1,\n  h2 {\n    margin: 0;\n    font-size: 1.5rem;\n  }\n\n  &__subtitle {\n    color: var(--text-color-secondary);\n    margin-left: 0.5ch;\n  }\n}\n\n.jenkins-build-caption {\n  display: flex;\n  flex-direction: row !important;\n  align-items: center;\n  justify-content: start !important;\n  gap: 1rem;\n  max-width: 1200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n\n  svg {\n    width: 2rem !important;\n    height: 2rem !important;\n  }\n\n  img {\n    width: 2.375rem;\n    height: 2.375rem;\n  }\n}\n\n.bottom-sticker,\n#bottom-sticker {\n  position: sticky;\n\n  // This has to be set to -1px so that IntersectionObserver can add the\n  // &--stuck class when the element is stuck to the bottom of the screen\n  // https://css-tricks.com/how-to-detect-when-a-sticky-element-gets-pinned/\n  bottom: -1px;\n  margin-left: calc(var(--section-padding) * -1);\n  width: calc(\n    100% + calc(var(--section-padding) * 2)\n  ); /* it needs to occupy the entire width or else the underlying content will see through */\n\n  z-index: 998; /* behind top-sticker */\n}\n\n.bottom-sticker-inner {\n  position: relative;\n  padding: 1em var(--section-padding);\n  z-index: 0;\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    background: var(--background);\n    opacity: 0;\n    z-index: -1;\n  }\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    top: -30px;\n    left: 0;\n    right: 0;\n    background: linear-gradient(transparent, var(--bottom-app-bar-shadow) 110%);\n    max-width: 100%;\n    height: 30px;\n    opacity: 0;\n    transition: var(--standard-transition);\n    mask-image: linear-gradient(\n      to right,\n      transparent,\n      white var(--section-padding),\n      white calc(100% - var(--section-padding)),\n      transparent\n    );\n    pointer-events: none;\n  }\n\n  &--stuck {\n    .bottom-sticker-inner {\n      backdrop-filter: blur(15px);\n\n      &::before {\n        opacity: 0.75 !important;\n\n        @supports not (backdrop-filter: blur(15px)) {\n          opacity: 1 !important;\n        }\n      }\n\n      &::after {\n        opacity: 1 !important;\n      }\n    }\n  }\n}\n", ".jenkins-avatar {\n  &[src] {\n    border-radius: 100px;\n    outline: var(--jenkins-border-width) solid\n      color-mix(in sRGB, var(--text-color-secondary) 10%, transparent);\n    background: color-mix(in sRGB, var(--text-color-secondary) 5%, transparent);\n    outline-offset: calc(var(--jenkins-border-width) * -1);\n    object-fit: cover;\n\n    @media (prefers-contrast: more) {\n      outline-color: var(--text-color);\n    }\n  }\n}\n", ".jenkins-badge {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 100px;\n  font-size: 0.6875rem;\n  font-weight: 400;\n  min-height: 20px;\n  min-width: 20px;\n  padding: 0 0.4rem;\n  background: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 12.5%,\n    transparent\n  );\n\n  &[class*=\"color\"] {\n    background: color-mix(in sRGB, var(--color) 85%, transparent);\n    color: var(--white) !important;\n    box-shadow: inset 0 -1px 2px var(--color, var(--text-color-secondary));\n    text-shadow: 0 1px 1px rgb(0 0 0 / 0.1);\n    backdrop-filter: blur(2.5px);\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.jenkins-breadcrumbs {\n  position: sticky;\n  top: 0;\n  z-index: 999;\n  display: flex;\n  align-items: center;\n  padding: 0.55rem 0.7rem 0.55rem 0.75rem;\n  backdrop-filter: blur(15px);\n  overflow-x: auto;\n  background: var(--breadcrumbs-bar-background);\n\n  &__list {\n    display: contents;\n    list-style-type: none;\n\n    & > * {\n      flex-shrink: 0;\n    }\n\n    &-item {\n      position: relative;\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      color: var(--text-color);\n      font-weight: normal;\n      font-size: var(--font-size-sm);\n      padding: 0.2rem 0.4rem;\n\n      & > a {\n        @include mixins.item($border: false);\n\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        font-weight: inherit;\n        font-size: inherit;\n        margin: 0;\n        padding: 0;\n        color: var(--text-color);\n        margin-right: 0 !important;\n        transition: var(--standard-transition);\n\n        &::before,\n        &::after {\n          inset: -0.25rem -0.6rem;\n        }\n\n        &:hover,\n        &:active,\n        &:focus,\n        &:focus-visible {\n          color: var(--text-color);\n        }\n      }\n\n      & > .model-link {\n        @media (hover: none) {\n          margin-right: 30px !important;\n        }\n\n        &:hover,\n        &--open {\n          margin-right: 30px !important;\n        }\n      }\n    }\n\n    // '>' separator between two items\n    .children,\n    .separator {\n      position: relative;\n      width: 1rem;\n      height: 1rem;\n      margin: 0.375rem 0.2rem;\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        inset: 0;\n        transition: var(--standard-transition);\n        background: var(--text-color-secondary);\n        mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'%3E%3Ctitle%3EChevron Forward%3C/title%3E%3Cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144'/%3E%3C/svg%3E\");\n        opacity: 0.6;\n      }\n    }\n\n    .separator {\n      &:last-of-type {\n        display: none;\n      }\n    }\n\n    .children {\n      cursor: pointer;\n\n      &:hover {\n        &::after {\n          opacity: 1;\n          transform: rotate(90deg);\n        }\n      }\n\n      &:active {\n        &::after {\n          transform: translateY(2px) rotate(90deg);\n          opacity: 0.5;\n        }\n      }\n\n      &:hover,\n      &:active,\n      &:focus,\n      &:focus-visible {\n        &::after {\n          background: var(--text-color);\n        }\n      }\n\n      // Increase the hit target\n      &::before {\n        content: \"\";\n        position: absolute;\n        inset: -14px -5px;\n        background: transparent;\n      }\n    }\n  }\n}\n\n.jenkins-menu-dropdown-chevron {\n  position: absolute;\n  display: inline-block;\n  width: 14px;\n  height: 14px;\n  right: 0;\n  border: none;\n  outline: none;\n  cursor: pointer;\n  padding: 0;\n  pointer-events: none;\n  transition: var(--standard-transition);\n  background: transparent;\n  top: 4px;\n\n  &:hover {\n    &::after {\n      opacity: 1 !important;\n    }\n  }\n\n  &:active {\n    &::after {\n      transform: translateY(2px);\n      opacity: 0.5 !important;\n    }\n  }\n\n  // Increase the hit target\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: -10px;\n  }\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    right: 0;\n    width: 14px;\n    opacity: 0;\n    transition: var(--standard-transition);\n    background: var(--text-color);\n    mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'%3E%3Ctitle%3EChevron Down%3C/title%3E%3Cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144'/%3E%3C/svg%3E\");\n    mask-position: center;\n    mask-repeat: no-repeat;\n  }\n}\n\n.model-link {\n  position: relative;\n  transition: var(--standard-transition) !important;\n  margin-right: 40px !important;\n\n  @media (hover: none) {\n    &::before,\n    &::after {\n      right: -30px !important;\n    }\n\n    .jenkins-menu-dropdown-chevron {\n      right: -22px;\n\n      &::after {\n        opacity: 1;\n      }\n    }\n  }\n\n  @media (hover: hover) {\n    & + a.model-link {\n      margin-left: 0 !important;\n    }\n\n    & + a.jenkins-table__badge {\n      margin-left: -1.5rem !important;\n    }\n\n    &--open {\n      &::before {\n        background-color: var(--item-background--hover) !important;\n        right: -30px !important;\n      }\n    }\n\n    &--open,\n    &:hover {\n      & + a.jenkins-table__badge {\n        margin-left: -1.5rem !important;\n        transform: translateX(1.5rem);\n      }\n\n      &::before,\n      &::after {\n        right: -30px;\n      }\n\n      .jenkins-menu-dropdown-chevron {\n        pointer-events: all;\n        right: -22px;\n\n        &::after {\n          opacity: 0.5;\n        }\n      }\n    }\n  }\n}\n\n.model-link--float {\n  z-index: 0;\n\n  @media (hover: none) {\n    margin-right: 30px !important;\n\n    &::before,\n    &::after {\n      right: -30px !important;\n    }\n\n    .jenkins-menu-dropdown-chevron {\n      right: -10px;\n\n      &::after {\n        opacity: 1;\n      }\n    }\n  }\n\n  @media (hover: hover) {\n    margin-right: 0 !important;\n\n    &::before,\n    &::after {\n      content: \"\";\n      position: absolute;\n      inset: -7px -10px;\n      opacity: 0;\n      border-radius: 6px;\n      transition: var(--standard-transition);\n    }\n\n    &::before {\n      z-index: -2;\n      background: var(--background);\n    }\n\n    &::after {\n      z-index: -1;\n      background: var(--text-color);\n    }\n\n    &:hover {\n      z-index: 10;\n\n      @media (hover: hover) {\n        &::before,\n        &::after {\n          right: calc((2ch + 14px) * -1);\n        }\n      }\n\n      &::before {\n        opacity: 1;\n      }\n\n      &::after {\n        opacity: 0.15;\n      }\n\n      .jenkins-menu-dropdown-chevron {\n        &::after {\n          opacity: 0.5;\n        }\n      }\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.jenkins-button {\n  --item-background: var(--button-background);\n  --item-background--hover: var(--button-background--hover);\n  --item-background--active: var(--button-background--active);\n  --item-box-shadow--focus: var(--button-box-shadow--focus);\n\n  @include mixins.item;\n\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0;\n  padding: 0.5rem 1rem;\n  font-size: var(--font-size-sm);\n  color: var(--text-color) !important;\n  min-height: 2.375rem;\n  white-space: nowrap;\n  gap: 1ch;\n\n  svg {\n    width: 1.125rem;\n    height: 1.125rem;\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    filter: saturate(0.6);\n    cursor: not-allowed;\n  }\n}\n\n.jenkins-button--primary {\n  --button-background: oklch(from var(--accent-color) l c h);\n  --button-background--hover: oklch(from var(--accent-color) l c h / 0.9);\n  --button-background--active: oklch(from var(--accent-color) l c h / 0.8);\n  --button-box-shadow--focus: oklch(from var(--accent-color) l c h / 0.4);\n\n  color: var(--button-color--primary) !important;\n}\n\n// Support for custom colors\n// Modifier classes must include 'color' in name to work\n.jenkins-button[class*=\"color\"] {\n  --button-background: oklch(from currentColor l c h / 0.1);\n  --button-background--hover: oklch(from currentColor l c h / 0.15);\n  --button-background--active: oklch(from currentColor l c h / 0.25);\n  --button-box-shadow--focus: oklch(from currentColor l c h / 0.125);\n\n  color: var(--color) !important;\n}\n\n.jenkins-button--primary[class*=\"color\"] {\n  --button-background: oklch(from var(--color) l c h);\n  --button-background--hover: oklch(from var(--color) l c h / 0.9);\n  --button-background--active: oklch(from var(--color) l c h / 0.8);\n  --button-box-shadow--focus: oklch(from var(--color) l c h / 0.4);\n\n  color: var(--background) !important;\n}\n\n.jenkins-button--tertiary {\n  --button-background: transparent !important;\n\n  &:not(\n      :hover,\n      &:active,\n      &:focus,\n      &[aria-describedby],\n      &[aria-expanded=\"true\"]\n    ) {\n    &::before {\n      border-color: transparent;\n    }\n  }\n}\n\n// Additional button related classes\n.jenkins-buttons-row {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n\n  &--invert {\n    justify-content: flex-end;\n  }\n\n  &--equal-width {\n    .jenkins-button {\n      min-width: 6.5rem;\n    }\n  }\n\n  &--equal-width {\n    min-width: 6.5rem;\n  }\n\n  @media (width <= 600px) {\n    justify-content: stretch;\n\n    .jenkins-button {\n      flex-grow: 1;\n    }\n  }\n}\n\n.jenkins-copy-button {\n  .jenkins-copy-button__icon {\n    position: relative;\n    display: grid;\n    grid-template-columns: 1fr;\n    place-items: center;\n    width: 1.125rem;\n    height: 1.125rem;\n    transition: var(--standard-transition);\n\n    svg {\n      grid-area: 1 / 1;\n      scale: -0.5;\n      opacity: 0;\n      transition: var(--elastic-transition);\n      filter: blur(2px);\n      color: var(--success-color);\n\n      * {\n        stroke-width: 40px;\n      }\n    }\n\n    &::before,\n    &::after {\n      content: \"\";\n      position: relative;\n      width: 0.6875rem;\n      height: 0.875rem;\n      border: 0.1rem solid currentColor;\n      border-radius: 0.2rem;\n      transition:\n        translate var(--standard-transition),\n        scale var(--standard-transition),\n        opacity var(--standard-transition),\n        filter var(--standard-transition);\n      grid-area: 1 / 1;\n    }\n\n    &::before {\n      translate: -16% -10%;\n      clip-path: polygon(\n        100% 0,\n        100% 22.5%,\n        22.5% 22.5%,\n        32.5% 100%,\n        0 100%,\n        0 0\n      );\n    }\n\n    &::after {\n      translate: 16% 10%;\n    }\n  }\n\n  &--copied {\n    color: var(--success-color) !important;\n\n    --button-background: color-mix(\n      in sRGB,\n      var(--success-color) 10%,\n      transparent\n    ) !important;\n    --button-background--hover: var(--button-background);\n    --button-background--active: var(--button-background);\n\n    .jenkins-copy-button__icon {\n      &::before,\n      &::after {\n        scale: 1.25;\n        opacity: 0;\n        filter: blur(1px);\n      }\n\n      svg {\n        scale: 1.25;\n        opacity: 1;\n        filter: blur(0);\n      }\n    }\n  }\n\n  &:hover {\n    .jenkins-copy-button__icon {\n      &::before {\n        translate: -11% -8%;\n      }\n\n      &::after {\n        translate: 11% 8%;\n      }\n    }\n  }\n\n  &:active {\n    .jenkins-copy-button__icon {\n      transform: scale(0.85);\n    }\n  }\n}\n\n.jenkins-validate-button__container {\n  &__status {\n    .validation-error-area {\n      min-height: 36px !important;\n    }\n  }\n\n  .validation-error-area--visible {\n    margin-top: 0;\n    margin-bottom: 0.625rem;\n  }\n\n  & > .jenkins-button {\n    float: right;\n  }\n}\n\n.advanced-button,\n.hetero-list-add {\n  svg {\n    width: 0.875rem;\n    height: 0.875rem;\n    transition: var(--standard-transition);\n  }\n\n  &:not([data-expanded=\"true\"]) {\n    &:active {\n      svg {\n        translate: 0 0.125rem;\n      }\n    }\n  }\n\n  &[data-expanded=\"true\"] {\n    svg {\n      rotate: 180deg;\n    }\n  }\n}\n\n$jenkins-split-button-border-radius: 0.2rem;\n\n.jenkins-split-button {\n  display: flex;\n  gap: 1px;\n\n  & > :first-child {\n    border-top-right-radius: $jenkins-split-button-border-radius;\n    border-bottom-right-radius: $jenkins-split-button-border-radius;\n  }\n\n  & > .jenkins-button:last-of-type {\n    padding: 0 5px;\n    border-top-left-radius: $jenkins-split-button-border-radius;\n    border-bottom-left-radius: $jenkins-split-button-border-radius;\n\n    svg {\n      width: 0.8rem;\n      height: 0.8rem;\n    }\n  }\n}\n\n.stop-button-link {\n  --item-background: color-mix(in sRGB, var(--red) 15%, transparent);\n  --item-background--hover: color-mix(in sRGB, var(--red) 20%, transparent);\n  --item-background--active: color-mix(in sRGB, var(--red) 25%, transparent);\n  --item-box-shadow--focus: transparent;\n\n  @include mixins.item;\n\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 1rem;\n  height: 1rem;\n  border-radius: 0.25rem;\n\n  svg {\n    width: 87.5%;\n    height: 87.5%;\n    color: var(--red);\n\n    * {\n      stroke-width: 40px;\n    }\n  }\n}\n", "$card-padding: 1rem;\n$icon-size: 1.375rem;\n\n.jenkins-card {\n  position: relative;\n  border-radius: 1rem;\n  margin-bottom: calc(var(--section-padding) / 2);\n  background: var(--card-background);\n\n  &__title {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 $card-padding;\n    height: 50px;\n    font-size: var(--font-size-sm) !important;\n    font-weight: var(--font-bold-weight);\n    width: 100%;\n    z-index: 1;\n  }\n\n  &__controls {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 0.4rem;\n    margin-right: -0.2rem;\n  }\n\n  &:not(:hover) {\n    .jenkins-card__unveil,\n    .jenkins-card__reveal {\n      color: var(--text-color-secondary);\n    }\n  }\n\n  @media (prefers-contrast: more) {\n    .jenkins-card__unveil,\n    .jenkins-card__reveal {\n      color: var(--text-color) !important;\n    }\n  }\n\n  &:hover {\n    .jenkins-card__reveal {\n      color: var(--text-color);\n    }\n  }\n\n  &__content {\n    display: flex;\n    flex-direction: column;\n    padding: 0 $card-padding $card-padding;\n\n    &:empty {\n      display: none;\n    }\n  }\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    border-radius: inherit;\n    border: var(--card-border-width) solid var(--card-border-color);\n    z-index: 1;\n    pointer-events: none;\n  }\n\n  .jenkins-card__reveal {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-block: -0.5rem;\n    min-height: 0;\n    padding: 0;\n    width: 26px;\n    height: 26px;\n    border-radius: 0.33rem;\n    transition:\n      scale var(--standard-transition),\n      opacity var(--standard-transition);\n\n    svg {\n      width: 1rem;\n      height: 1rem;\n      transition: color var(--standard-transition);\n    }\n\n    &::before,\n    &::after {\n      opacity: 0;\n    }\n\n    &:hover {\n      opacity: 0.75;\n    }\n\n    &:active {\n      scale: 95%;\n      opacity: 0.5;\n    }\n  }\n\n  hr {\n    align-self: stretch;\n    border: none;\n    border-top: var(--card-border-width) solid var(--text-color-secondary);\n    opacity: 0.1;\n    margin: 0;\n  }\n}\n\n.jenkins-card__details {\n  display: flex;\n  flex-direction: column;\n  align-items: start;\n  gap: 0.75rem;\n}\n\n.jenkins-card__details__item {\n  display: grid;\n  grid-template-columns: auto 1fr;\n  gap: 0.75rem;\n  font-weight: normal;\n\n  &__icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    align-self: start;\n    width: $icon-size;\n    height: 1lh;\n\n    svg {\n      width: $icon-size;\n      height: $icon-size;\n      color: var(--text-color);\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n@use \"../base/breakpoints\";\n\n$command-palette-background: color-mix(\n  in sRGB,\n  var(--card-background) 92.5%,\n  transparent\n);\n\n.jenkins-command-palette__dialog {\n  background: none;\n  border: none;\n  height: 100dvh !important;\n  max-height: 100dvh !important;\n  width: 100vw !important;\n  max-width: 100vw !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  user-select: none;\n\n  &::backdrop {\n    background: var(--command-palette-backdrop-background);\n    backdrop-filter: blur(1px);\n    animation: jenkins-dialog-backdrop-animate-in 0.075s linear;\n  }\n\n  &[open] {\n    animation: command-palette-animate-in 0.075s cubic-bezier(0, 0.68, 0.5, 1.5);\n  }\n\n  &[closing] {\n    animation: command-palette-animate-out 0.05s linear;\n\n    &::backdrop {\n      animation: jenkins-dialog-backdrop-animate-out 0.05s linear;\n    }\n  }\n}\n\n@keyframes command-palette-animate-in {\n  from {\n    translate: 0 4px;\n    scale: 98.5%;\n    opacity: 0;\n    transform: rotateX(30deg);\n  }\n}\n\n@keyframes command-palette-animate-out {\n  to {\n    scale: 98.5%;\n    opacity: 0;\n  }\n}\n\n.jenkins-command-palette__wrapper {\n  --inset: 10vh;\n\n  width: 100%;\n  height: 100%;\n  max-height: 100dvh;\n  overflow: scroll;\n  padding-top: var(--inset);\n\n  @media (max-width: breakpoints.$tablet-breakpoint) {\n    --inset: 10vh;\n  }\n}\n\n.jenkins-command-palette {\n  position: relative;\n  width: 50vw;\n  max-width: 650px;\n  color: var(--text-color);\n  pointer-events: auto;\n  margin: 0 auto var(--inset);\n\n  @media (max-width: breakpoints.$tablet-breakpoint) {\n    width: calc(100% - (var(--section-padding) * 2));\n  }\n\n  &__search {\n    --search-bar-height: 3rem !important;\n\n    background: $command-palette-background;\n    box-shadow: var(--command-palette-inset-shadow);\n    margin-bottom: var(--section-padding);\n    border-radius: 1rem;\n    transition: var(--standard-transition);\n    z-index: 10;\n    backdrop-filter: var(--command-palette-results-backdrop-filter);\n    max-width: unset;\n\n    input {\n      padding: 0 0.5rem 0 45px;\n      background: transparent !important;\n\n      --input-border: transparent;\n      --input-border-hover: transparent;\n\n      border-radius: inherit;\n\n      &::before,\n      &::after {\n        border-radius: inherit;\n      }\n    }\n\n    &::before {\n      content: unset;\n    }\n  }\n\n  &__results-container {\n    display: flex;\n    flex-direction: column;\n    border-radius: 1rem;\n    background: $command-palette-background;\n    backdrop-filter: var(--command-palette-results-backdrop-filter);\n    box-shadow: var(--command-palette-inset-shadow);\n\n    // If set to 0, Safari won't always show the backdrop-filter\n    height: 1px;\n    transition: height 0.15s ease;\n    overflow: hidden;\n    will-change: height;\n  }\n\n  &__results {\n    display: flex;\n    flex-direction: column;\n    padding: 0.5rem;\n\n    &__heading {\n      font-size: var(--font-size-sm);\n      margin: 0;\n      padding: 0.375rem 0.625rem;\n      color: var(--text-color-secondary);\n\n      &:not(:first-of-type) {\n        padding-top: 1.375rem;\n      }\n    }\n\n    &__item {\n      @include mixins.item($border: false);\n\n      --item-background--hover: color-mix(\n        in sRGB,\n        var(--text-color-secondary) 15%,\n        transparent\n      );\n      --item-background--active: color-mix(\n        in sRGB,\n        var(--text-color-secondary) 20%,\n        transparent\n      );\n\n      display: flex;\n      align-items: center;\n      justify-content: flex-start;\n      padding: 0.625rem;\n      border-radius: 0.5rem;\n      color: var(--text-color) !important;\n\n      &--hover {\n        &::before {\n          background-color: var(--item-background--hover);\n          border: var(--jenkins-border--subtle) !important;\n        }\n      }\n\n      &__icon {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 1.375rem;\n        height: 1.375rem;\n        margin-right: 0.625rem;\n        overflow: hidden;\n        pointer-events: none;\n        color: var(--text-color);\n\n        svg,\n        img {\n          width: 1.25rem;\n          height: 1.25rem;\n        }\n      }\n\n      &__description {\n        opacity: 0.5;\n        margin-left: 1ch;\n      }\n\n      &__chevron {\n        position: absolute;\n        top: calc(50% - 8px);\n        right: 12.5px;\n        width: 16px;\n        height: 16px;\n        opacity: 0.5;\n      }\n    }\n  }\n\n  &__info {\n    font-size: var(--font-size-sm);\n    margin: 0;\n    padding: 0 0.625rem;\n    line-height: 42px;\n    color: var(--text-color);\n\n    span {\n      color: var(--text-color-secondary);\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.content-block__link {\n  @include mixins.item;\n\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 1.1rem;\n  color: var(--text-color) !important;\n  font-weight: var(--font-bold-weight);\n  text-decoration: none !important;\n\n  &::before {\n    background: var(--button-background);\n  }\n\n  .trailing-icon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 1.25rem;\n    height: 1.25rem;\n\n    svg {\n      width: 1.25rem;\n      height: 1.25rem;\n    }\n  }\n}\n\n.empty-state-block {\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.empty-state-section {\n  margin-top: var(--section-padding);\n}\n\n.empty-state-section-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.625rem;\n  padding: 0;\n\n  li {\n    list-style: none;\n  }\n}\n", "$jenkins-dialog-padding: 1.3rem;\n\n.jenkins-dialog {\n  border-radius: var(--form-input-border-radius);\n  border: none;\n  background-color: var(--card-background);\n  box-shadow: var(--dialog-box-shadow);\n  animation: jenkins-dialog-animate-in 0.25s cubic-bezier(0, 0.68, 0.5, 1.5);\n  overflow: hidden;\n  padding: $jenkins-dialog-padding 0 0 0;\n  display: flex;\n  flex-direction: column;\n  gap: $jenkins-dialog-padding;\n  outline: none;\n\n  &::backdrop {\n    background: var(--dialog-backdrop-background);\n    animation: jenkins-dialog-backdrop-animate-in 0.15s;\n  }\n\n  &__title {\n    font-size: 1.125rem;\n    font-weight: var(--font-bold-weight);\n    padding: 0 $jenkins-dialog-padding;\n    color: var(--text-color);\n  }\n\n  &__contents {\n    overflow-y: auto;\n    overflow-wrap: break-word;\n    padding: 0 $jenkins-dialog-padding;\n    max-height: 75vh;\n    font-size: 1rem;\n    color: var(--text-color-secondary);\n\n    &--modal {\n      padding-bottom: $jenkins-dialog-padding;\n      color: var(--text-color);\n    }\n  }\n\n  &__input {\n    display: flex;\n    padding: 0 1rem 1rem;\n  }\n\n  &__buttons {\n    padding: 0 $jenkins-dialog-padding $jenkins-dialog-padding;\n    justify-content: right;\n    flex-direction: row-reverse;\n\n    .jenkins-button {\n      min-width: 100px;\n    }\n  }\n\n  &__subtitle {\n    font-size: 1rem;\n    font-weight: var(--font-bold-weight);\n    color: var(--text-color-secondary);\n    padding: 0;\n    margin: 0 0 1rem;\n  }\n\n  &__close-button {\n    position: absolute;\n    top: $jenkins-dialog-padding - 0.5rem;\n    right: $jenkins-dialog-padding - 0.5rem;\n    aspect-ratio: 1;\n    padding: 0;\n    border-radius: 100%;\n  }\n}\n\n@keyframes jenkins-dialog-backdrop-animate-in {\n  from {\n    opacity: 0;\n  }\n}\n\n@keyframes jenkins-dialog-backdrop-animate-out {\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes jenkins-dialog-animate-in {\n  from {\n    scale: 85%;\n    opacity: 0;\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n$dropdown-border-radius: 1rem;\n$dropdown-padding: 0.375rem;\n\n.tippy-box[data-theme~=\"dropdown\"] {\n  border-radius: $dropdown-border-radius;\n  box-shadow: var(--dropdown-box-shadow);\n  outline: none !important;\n  background: color-mix(in sRGB, var(--card-background) 85%, transparent);\n  backdrop-filter: var(--dropdown-backdrop-filter);\n  max-width: unset !important;\n  max-height: 75vh;\n  overflow-y: auto;\n\n  .tippy-content {\n    display: flex;\n    flex-direction: column;\n    padding: 0;\n\n    .jenkins-spinner {\n      margin: 1rem;\n    }\n  }\n}\n\n.tippy-box[data-animation=\"dropdown\"][data-state=\"hidden\"] {\n  $translate: 5px;\n  $scale: 0.99;\n\n  opacity: 0;\n  transform: scale($scale);\n\n  &[data-placement^=\"top\"] {\n    transform-origin: bottom;\n    transform: translateY($translate) scale($scale);\n  }\n\n  &[data-placement^=\"bottom\"] {\n    transform-origin: top;\n    transform: translateY(-$translate) scale($scale);\n  }\n\n  &[data-placement^=\"left\"] {\n    transform-origin: right;\n    transform: translateX($translate) scale($scale);\n  }\n\n  &[data-placement^=\"right\"] {\n    transform-origin: left;\n    transform: translateX(-$translate) scale($scale);\n  }\n}\n\n.jenkins-dropdown {\n  display: flex;\n  flex-direction: column;\n  padding: $dropdown-padding;\n\n  &--compact {\n    > .jenkins-dropdown__item,\n    > .jenkins-dropdown__disabled {\n      padding: 0 0.6rem;\n      min-height: 30px !important;\n    }\n  }\n\n  &__filter {\n    display: flex;\n    align-items: center;\n    gap: 5px;\n    padding: 0.6rem 1rem;\n    border-bottom: 1px solid var(--input-border);\n\n    &-input {\n      &:focus {\n        outline: none;\n      }\n\n      width: 100%;\n      border: none;\n      color: var(--text-color-secondary);\n      background-color: unset;\n    }\n\n    svg {\n      margin-top: 2px;\n    }\n  }\n\n  &__separator {\n    position: relative;\n    height: var(--jenkins-border-width);\n    margin: $dropdown-padding calc($dropdown-padding * -1);\n    border: none;\n    background-color: var(--jenkins-border-color);\n  }\n\n  &__heading {\n    color: var(--text-color-secondary) !important;\n    margin: $dropdown-padding 0.65rem;\n    font-size: 0.8125rem;\n    font-weight: var(--font-bold-weight);\n    opacity: 0.8;\n\n    &:not(:first-of-type) {\n      margin-top: 1rem;\n    }\n  }\n\n  &__placeholder {\n    color: var(--text-color-secondary) !important;\n    margin: $dropdown-padding 0.55rem;\n    font-size: 0.8125rem;\n    opacity: 0.8;\n  }\n\n  &__disabled {\n    color: var(--text-color-secondary) !important;\n    font-size: 0.8125rem;\n    opacity: 0.8;\n    display: inline-flex;\n    align-items: center;\n    margin: 0;\n    cursor: default;\n  }\n\n  &__item {\n    --item-background--hover: var(--button-background--hover);\n    --item-background--active: var(--button-background--active);\n    --item-box-shadow--focus: var(--button-box-shadow--focus);\n\n    @include mixins.item($border: false);\n\n    appearance: none;\n    display: inline-flex;\n    align-items: center;\n    justify-content: flex-start;\n    border: none;\n    outline: none;\n    margin: 0;\n    padding: $dropdown-padding 1.75rem $dropdown-padding 0.6rem;\n    font-size: 0.8125rem;\n    font-weight: normal;\n    text-decoration: none !important;\n    background: transparent;\n    color: var(--text-color) !important;\n    border-radius: calc($dropdown-border-radius - $dropdown-padding);\n    cursor: pointer;\n    min-height: 36px;\n    white-space: nowrap;\n    gap: 1.2ch;\n    min-width: 180px;\n    user-select: none;\n\n    &__icon {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      width: 1.125rem;\n      height: 1.125rem;\n      margin-right: 0.1rem;\n\n      svg,\n      img {\n        width: 1.125rem;\n        height: 1.125rem;\n        color: inherit;\n      }\n    }\n\n    &:disabled {\n      pointer-events: none;\n      opacity: 0.5;\n      filter: saturate(0.6);\n    }\n\n    &[class*=\"color\"] {\n      background: transparent;\n      color: var(--color) !important;\n\n      &::before {\n        background: currentColor !important;\n        opacity: 0;\n      }\n\n      &::after {\n        box-shadow: 0 0 0 0.66rem currentColor;\n        opacity: 0;\n      }\n\n      &:hover {\n        &::before {\n          opacity: 0.15;\n        }\n      }\n\n      &:active,\n      &:focus {\n        &::before {\n          opacity: 0.2;\n        }\n\n        &::after {\n          box-shadow: 0 0 0 0.33rem currentColor;\n          opacity: 0.1;\n        }\n      }\n    }\n\n    &__badge {\n      margin-left: auto;\n      margin-right: -0.85rem;\n    }\n\n    &__chevron {\n      position: absolute;\n      top: 0;\n      right: 0.1875rem;\n      bottom: 0;\n      background: var(--text-color-secondary);\n      width: 1rem;\n      mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'%3E%3Ctitle%3EChevron Forward%3C/title%3E%3Cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144'/%3E%3C/svg%3E\");\n      mask-size: contain;\n      mask-repeat: no-repeat;\n      mask-position: center;\n      opacity: 0.6;\n    }\n  }\n\n  &:hover {\n    .jenkins-dropdown__item--selected {\n      &::before {\n        opacity: 0;\n      }\n    }\n  }\n}\n\n.jenkins-dropdown__item--selected {\n  &::before {\n    background: var(--item-background--hover);\n    border: var(--jenkins-border--subtle) !important;\n    animation: pulse 1s ease-in-out forwards;\n  }\n\n  &:hover {\n    &::before {\n      opacity: 1 !important;\n    }\n  }\n\n  @keyframes pulse {\n    50% {\n      background: var(--item-background--active);\n    }\n  }\n}\n\n.jenkins-overflow-button__ellipsis {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.15rem;\n  width: 1.4rem;\n  height: 1.1rem;\n  margin-inline: -0.1rem;\n\n  span {\n    min-width: 0.3125rem;\n    min-height: 0.3125rem;\n    border: 1px solid currentColor;\n    border-radius: 50%;\n  }\n}\n\n.jenkins-jumplist-link {\n  appearance: none;\n  border: none;\n  background: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n\n  svg {\n    width: 1.25rem;\n    height: 1.25rem;\n  }\n}\n", ".icon-help {\n  color: var(--accent-color);\n\n  &:hover,\n  &:focus {\n    color: var(--primary-hover);\n    border-color: var(--primary-hover);\n  }\n}\n\n.icon-small {\n  width: 16px;\n  height: 16px;\n}\n\n.icon-medium {\n  width: 24px;\n  height: 24px;\n}\n\n.icon-large {\n  width: 32px;\n  height: 32px;\n}\n\n.icon-xlarge {\n  width: 64px;\n  height: 64px;\n}\n\n/*****\n * Build status icons\n *****/\n\n.animated-spin,\n.icon-blue-anime,\n.icon-red-anime {\n  will-change: transform;\n\n  // animation: blink 1s infinite linear;\n}\n\n// Mixin to set icon colors\n@mixin status-icon-color($color, $opacity: 1) {\n  color: $color;\n  fill: $color;\n  opacity: $opacity;\n\n  .svg-icon {\n    color: $color;\n    fill: $color;\n  }\n}\n\n.icon-blue,\n.icon-blue-anime {\n  @include status-icon-color(var(--success-color));\n}\n\n.icon-red,\n.icon-red-anime {\n  @include status-icon-color(var(--destructive-color));\n}\n\n.icon-yellow,\n.icon-yellow-anime {\n  @include status-icon-color(var(--unstable-build-icon-color));\n}\n\n.icon-aborted,\n.icon-aborted-anime {\n  @include status-icon-color(var(--black));\n}\n\n.icon-disabled,\n.icon-disabled-anime {\n  @include status-icon-color(var(--black), 0.5);\n}\n\n.icon-grey,\n.icon-grey-anime,\n.icon-nobuilt,\n.icon-nobuilt-anime {\n  @include status-icon-color(var(--accent-color));\n}\n\n.build-status-icon__wrapper {\n  display: inline-flex;\n  position: relative;\n}\n\n.build-status-icon__outer {\n  display: flex;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 100%;\n  height: 100%;\n  transform: translate(-50%, -50%);\n\n  .svg-icon {\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.icon-blue-anime .build-status-icon__outer,\n.icon-red-anime .build-status-icon__outer,\n.icon-yellow-anime .build-status-icon__outer,\n.icon-aborted-anime .build-status-icon__outer,\n.icon-disabled-anime .build-status-icon__outer,\n.icon-grey-anime .build-status-icon__outer,\n.icon-nobuilt-anime .build-status-icon__outer {\n  .svg-icon {\n    animation: spin 1.7s linear infinite;\n  }\n}\n\n@keyframes pulse-animation {\n  0%,\n  100% {\n    opacity: 1;\n  }\n\n  50% {\n    opacity: 0;\n  }\n}\n\n.pulse-animation {\n  animation: pulse-animation 2s ease infinite;\n}\n", ".jenkins-notice {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n  gap: 1rem;\n  min-height: 15rem;\n  background: var(--button-background);\n  border: var(--jenkins-border--subtle);\n  border-radius: var(--form-input-border-radius);\n  font-size: var(--font-size-base);\n  margin-bottom: var(--section-padding);\n  font-weight: var(--font-bold-weight);\n  padding: calc(var(--section-padding) * 2);\n  text-align: center;\n\n  svg {\n    width: 2.5rem;\n    height: 2.5rem;\n  }\n\n  &__description {\n    color: var(--text-color-secondary);\n    margin-top: -0.375rem;\n\n    &:empty {\n      display: none;\n    }\n  }\n}\n", "@use \"sass:color\";\n@use \"sass:string\";\n\n.jenkins-notification {\n  position: fixed;\n  left: 1.2rem;\n  bottom: 1.2rem;\n  min-width: 321px;\n  max-width: min(600px, #{string.unquote(\"calc(100vw - 2.4rem)\")});\n  display: grid;\n  grid-template-columns: auto 1fr;\n  grid-gap: 1.5ch;\n  padding: 0.8rem;\n  border-radius: var(--form-input-border-radius);\n  font-weight: var(--font-bold-weight);\n  line-height: 1.66;\n  color: var(--text-color);\n  box-shadow:\n    0 0 1px 1px rgba(color.adjust(#024cb6, $lightness: -50%), 0.075),\n    0 10px 30px rgba(color.adjust(#024cb6, $lightness: -50%), 0.25),\n    0 0 30px 5px var(--background);\n  will-change: opacity, transform;\n  z-index: 999;\n  cursor: pointer;\n  transition: filter var(--standard-transition);\n  backdrop-filter: brightness(2) blur(30px);\n\n  svg {\n    width: 1.4rem;\n    height: 1.4rem;\n  }\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    border-radius: inherit;\n    z-index: -1;\n    background: oklch(from var(--background) l c h / 0.3);\n    border: var(--jenkins-border--subtle);\n  }\n\n  @supports not (backdrop-filter: blur(15px)) {\n    &::after {\n      opacity: 0.9;\n    }\n  }\n\n  &:hover {\n    filter: brightness(0.95);\n  }\n\n  &:active {\n    filter: brightness(0.9);\n  }\n}\n\n.jenkins-notification--success {\n  color: var(--background);\n\n  &::after {\n    background-color: var(--success-color);\n    opacity: 1;\n  }\n}\n\n.jenkins-notification--warning {\n  color: var(--background);\n\n  &::after {\n    background-color: var(--warning-color);\n    opacity: 1;\n  }\n}\n\n.jenkins-notification--error {\n  color: var(--background);\n\n  &::after {\n    background-color: var(--error-color);\n    opacity: 1;\n  }\n}\n\n.jenkins-notification--visible {\n  animation: show-notification var(--elastic-transition) 1 normal forwards;\n\n  & > * {\n    animation: show-notification-icon var(--elastic-transition) 1 normal\n      forwards;\n  }\n}\n\n.jenkins-notification--hidden {\n  animation: hide-notification 150ms ease-in 1 normal forwards;\n}\n\n@keyframes show-notification {\n  from {\n    opacity: 0;\n    transform: translateY(1.2rem);\n  }\n\n  to {\n    opacity: 1;\n    transform: translateY(0);\n    visibility: visible;\n  }\n}\n\n@keyframes show-notification-icon {\n  from {\n    opacity: 0;\n    transform: translateY(0.3rem);\n  }\n}\n\n@keyframes hide-notification {\n  from {\n    opacity: 1;\n    transform: scale(1);\n  }\n\n  to {\n    opacity: 0;\n    transform: scale(0.9);\n    visibility: hidden;\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.page-footer {\n  width: 100%;\n  clear: both;\n  font-size: var(--font-size-sm);\n}\n\n.page-footer .container-fluid {\n  padding: 0;\n}\n\n.page-footer__flex-row {\n  display: flex;\n  align-items: stretch;\n  flex-wrap: wrap;\n}\n\n.page-footer__flex-row > *:not(script) {\n  display: flex;\n  align-items: center;\n  padding: calc(var(--section-padding) / 2)\n    calc(var(--section-padding) - 0.85rem);\n  gap: calc(var(--section-padding) / 2);\n}\n\n.page-footer__footer-id-placeholder {\n  flex: 1;\n  flex-wrap: wrap;\n}\n\n.page-footer span {\n  display: inline-block;\n}\n\n.page-footer .jenkins-button {\n  transition: var(--standard-transition);\n\n  &:hover {\n    color: var(--text-color) !important;\n  }\n\n  svg,\n  .jenkins-overflow-button__ellipsis {\n    display: none;\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.page-header {\n  display: flex;\n  align-items: center;\n  height: 3.5rem;\n  font-size: var(--font-size-base);\n  line-height: var(--line-height-base);\n  background-color: var(--header-bg-classic);\n}\n\n.page-header > * {\n  margin-right: 0.75rem;\n}\n\n.page-header__brand {\n  display: inline-block;\n  height: 3.5rem;\n  position: relative;\n  flex: 1; // push controls to the end of the block\n}\n\n// Need to use the element selector to increase weight otherwise it will be overriden by the\n// a:visited selector if it is declared later\n// Only styled by the overrides with the new UI enabled\na.page-header__brand-link {\n  display: none;\n}\n\n.page-header__brand-name {\n  color: inherit;\n}\n\n.page-header__brand-image {\n  height: 2rem;\n  width: 1.5rem;\n  margin-right: 0.75rem;\n}\n\n.page-header__am-wrapper {\n  display: contents;\n}\n\n.page-header__hyperlinks {\n  display: flex;\n  align-items: center;\n}\n\n.page-header__hyperlinks > a,\n.page-header__hyperlinks > button,\n.am-container > a {\n  @include mixins.item;\n\n  --text-color: var(--header-link-color);\n\n  display: inline-flex;\n  align-items: center;\n  appearance: none;\n  background: transparent;\n  outline: none;\n  border: none;\n  cursor: pointer;\n  color: var(--text-color);\n  text-decoration: none;\n  padding: 0.5rem;\n  margin-right: 0 !important;\n\n  svg {\n    width: 1.25rem;\n    height: 1.25rem;\n  }\n\n  &::before,\n  &::after {\n    inset: 0 !important;\n  }\n\n  .jenkins-menu-dropdown-chevron {\n    position: relative;\n    top: unset !important;\n    right: unset !important;\n    margin-left: 0.5rem;\n\n    &::after {\n      opacity: 1;\n    }\n  }\n}\n\n.page-header__hyperlinks a span {\n  &:not(:first-child) {\n    margin-left: 0.25rem;\n  }\n}\n", "/* pane */\n\n.pane-frame {\n  border: var(--pane-border-width) solid var(--pane-border-color);\n  border-radius: var(--table-border-radius);\n}\n\n.pane-header,\n.pane-footer {\n  padding: 8px 0;\n  color: var(--pane-header-text-color);\n  background: var(--pane-header-bg);\n}\n\n.pane {\n  color: var(--pane-text-color);\n}\n\n.pane-header {\n  border-top: none;\n  border-top-left-radius: 4px;\n  border-top-right-radius: 4px;\n}\n\n.pane-footer {\n  border-bottom: none;\n  border-bottom-left-radius: 4px;\n  border-bottom-right-radius: 4px;\n}\n\n.pane td {\n  padding: 0.25rem;\n  vertical-align: middle;\n}\n\ntable.pane {\n  width: 100%;\n  border-collapse: collapse;\n  border: var(--pane-border-width) solid var(--pane-border-color);\n}\n\ntd.pane {\n  padding: 0.25rem;\n  vertical-align: middle;\n}\n\ntable.stripped tr:nth-of-type(even) {\n  background: var(--even-row-color);\n}\n\ntable.stripped-even tr:nth-child(even) {\n  background: var(--even-row-color);\n}\n\ntable.stripped-odd tr:nth-child(odd) {\n  background: var(--even-row-color);\n}\n\ntable.stripped tr:hover,\ntable.stripped-even tr:hover,\ntable.stripped-odd tr:hover {\n  background: var(--table-striped-bg--hover) !important;\n}\n\ndiv.pane-header {\n  font-weight: var(--pane-header-font-weight);\n  padding-right: 24px;\n}\n\ndiv.pane-header .collapse {\n  float: right;\n  margin-left: 3px;\n}\n\nth.pane {\n  font-weight: var(--pane-header-font-weight);\n}\n\n/* Bigtable */\n\ntable.bigtable {\n  border: none; // Needs to be set AFTER the table.pane declaration\n}\n\n/* For non-full screen table */\n\n.bigtable.width-auto {\n  width: auto;\n}\n\n// Keep this for tables still wrapped by .pane-frame\n.pane-frame table:not(.jenkins-table),\n.pane-frame .bigtable tr {\n  border: none; /* Border will be provided by the pane-frame */\n}\n\n// Avoid overriding .pane-header cells inside .bigtables with wrong styles\n.bigtable .pane-header,\n.bigtable th {\n  font-weight: var(--bigtable-header-font-weight);\n  color: var(--bigtable-header-text-color);\n  background: var(--bigtable-header-bg);\n}\n\n.bigtable th {\n  white-space: nowrap;\n  border-top: var(--bigtable-border-width) solid var(--bigtable-header-bg);\n  text-align: left;\n\n  &.minimum-width {\n    width: 1px;\n  }\n\n  &[align=\"right\"] {\n    text-align: right;\n  }\n}\n\n.bigtable tfoot th,\n.bigtable .sortbottom th {\n  color: var(--text-color);\n  background-color: var(--background);\n}\n\n.bigtable td {\n  border-top: var(--bigtable-border-width) solid\n    var(--bigtable-row-border-color);\n  vertical-align: middle;\n}\n\n.bigtable tr:last-child td {\n  border-bottom: var(--bigtable-border-width) solid\n    var(--bigtable-row-border-color);\n}\n\n.bigtable td,\n.bigtable th {\n  padding: var(--bigtable-cell-padding-y) var(--bigtable-cell-padding-x);\n}\n\n.bigtable .pane-header,\n.bigtable .pane-footer {\n  border-radius: 0;\n}\n\n/* ========================= sortable table ========================= */\ntable.bigtable.sortable a.sortheader,\ntable.bigtable.sortable span.sortarrow {\n  color: var(--bigtable-header-text-color);\n}\n\n/* ========================= Bigtable variants ========================= */\n\n.bigtable--compact th,\n.bigtable--compact td {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n", "/** Original source from loading.io, published under CC0 license */\n\n.lds-ellipsis {\n  display: inline-block;\n  position: relative;\n  width: 12rem;\n  height: 2.5rem;\n}\n\n.lds-ellipsis div {\n  position: absolute;\n  top: 1rem;\n  width: 0.5rem;\n  height: 0.5rem;\n  border-radius: 50%;\n  background: var(--text-color-secondary);\n  animation-timing-function: cubic-bezier(0, 1, 1, 0);\n}\n\n.lds-ellipsis div:nth-child(1) {\n  left: 1rem;\n  animation: lds-ellipsis1 0.6s infinite;\n}\n\n.lds-ellipsis div:nth-child(2) {\n  left: 1rem;\n  animation: lds-ellipsis2 0.6s infinite;\n}\n\n.lds-ellipsis div:nth-child(3) {\n  left: 4rem;\n  animation: lds-ellipsis2 0.6s infinite;\n}\n\n.lds-ellipsis div:nth-child(4) {\n  left: 10rem;\n  animation: lds-ellipsis3 0.6s infinite;\n}\n\n@keyframes lds-ellipsis1 {\n  0% {\n    transform: scale(0);\n  }\n\n  100% {\n    transform: scale(1);\n  }\n}\n\n@keyframes lds-ellipsis3 {\n  0% {\n    transform: scale(1);\n  }\n\n  100% {\n    transform: scale(0);\n  }\n}\n\n@keyframes lds-ellipsis2 {\n  0% {\n    transform: translate(0, 0);\n  }\n\n  100% {\n    transform: translate(3rem, 0);\n  }\n}\n", ".app-progress-bar {\n  --color: var(--accent-color);\n\n  height: 12px;\n  width: 104px;\n  padding: 2px;\n  border-radius: 6px;\n  background-color: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 25%,\n    transparent\n  );\n  display: block;\n  opacity: 1 !important;\n\n  &--error > span {\n    --color: var(--error-color);\n  }\n\n  &--unknown {\n    width: 100%;\n  }\n\n  &--large {\n    height: 24px;\n    width: 208px;\n    padding: 4px;\n    border-radius: 12px;\n\n    & > span {\n      border-radius: 8px !important;\n    }\n  }\n\n  & > span {\n    height: 100%;\n    min-width: 8%;\n    background-color: var(--color);\n    display: block;\n    border-radius: 4px;\n  }\n\n  &--animate {\n    background-image: linear-gradient(\n      -45deg,\n      var(--background) 25%,\n      transparent 25%,\n      transparent 50%,\n      var(--background) 50%,\n      var(--background) 75%,\n      transparent 75%,\n      transparent\n    );\n    z-index: 1;\n    background-size: 25px 25px;\n    animation: progress-bar 5s linear infinite;\n\n    @keyframes progress-bar {\n      0% {\n        background-position: 0 0;\n      }\n\n      100% {\n        background-position: 25px 25px;\n      }\n    }\n  }\n\n  &:link {\n    &:hover {\n      opacity: 0.75 !important;\n    }\n\n    &:active {\n      opacity: 0.5 !important;\n    }\n  }\n}\n", ".jenkins-table__checkbox-container {\n  position: relative;\n  display: flex;\n}\n\n.jenkins-table__checkbox {\n  appearance: none;\n  display: inline-grid;\n  width: 1.375rem;\n  height: 1.375rem;\n  background: transparent;\n  outline: none;\n  border: none;\n  box-shadow:\n    var(--form-input-glow),\n    inset 0 0 0 var(--jenkins-border-width) var(--input-border);\n  border-radius: 6px;\n  transition: var(--standard-transition);\n  cursor: pointer;\n  padding: 0;\n\n  &:hover {\n    box-shadow:\n      var(--form-input-glow),\n      inset 0 0 0 0.3125rem var(--input-border-hover);\n  }\n\n  &:active,\n  &:focus {\n    box-shadow:\n      0 0 0 4px var(--focus-input-glow),\n      inset 0 0 0 0.3125rem var(--focus-input-border);\n  }\n\n  svg {\n    width: 0.85rem;\n    height: 0.85rem;\n    grid-column-start: 1;\n    grid-row-start: 1;\n    place-self: center center;\n    transition: var(--elastic-transition);\n    transform: scale(0);\n    color: var(--text-color);\n    opacity: 0;\n\n    * {\n      stroke-width: 60px;\n    }\n  }\n\n  .jenkins-table__checkbox__all-symbol {\n    color: var(--background);\n  }\n\n  &--indeterminate {\n    .jenkins-table__checkbox__indeterminate-symbol {\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n\n  &--all {\n    box-shadow:\n      var(--form-input-glow),\n      inset 0 0 0 0.6875rem var(--focus-input-border);\n\n    &:hover {\n      box-shadow:\n        var(--form-input-glow),\n        inset 0 0 0 1.375rem var(--focus-input-border);\n    }\n\n    &:active,\n    &:focus {\n      box-shadow:\n        0 0 0 4px var(--focus-input-glow),\n        inset 0 0 0 1.375rem var(--focus-input-border);\n    }\n\n    .jenkins-table__checkbox__all-symbol {\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n\n  &[disabled],\n  &:disabled {\n    pointer-events: none;\n    opacity: 0.2;\n    transition: none;\n  }\n}\n\n.jenkins-table__checkbox-options {\n  height: 1.375rem;\n  min-height: unset !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0 0 0.2rem !important;\n  border-radius: 6px;\n\n  svg {\n    max-width: 0.9rem;\n    max-height: 0.9rem;\n    pointer-events: none;\n    color: var(--input-border);\n    transition: var(--standard-transition);\n\n    * {\n      stroke-width: 60px;\n    }\n  }\n\n  &::before,\n  &::after {\n    border-radius: 0.33rem;\n  }\n\n  &:hover {\n    svg {\n      color: var(--input-border-hover);\n    }\n  }\n\n  &:active,\n  &:focus {\n    svg {\n      color: var(--focus-input-border);\n    }\n  }\n\n  &[disabled],\n  &:disabled {\n    pointer-events: none;\n    opacity: 0.2;\n    transition: none;\n  }\n}\n\n.jenkins-table__checkbox-dropdown {\n  position: absolute;\n  top: 1.375rem;\n  left: 1.375rem;\n  display: flex;\n  flex-direction: column;\n  border-radius: 0.8rem;\n  z-index: 999;\n  padding: 0.4rem;\n  box-shadow:\n    inset 0 0 0 2px rgba(white, 0.05),\n    0 0 0 1px rgba(black, 0.05),\n    0 5px 10px rgba(black, 0.1),\n    0 5px 20px rgba(black, 0.3);\n  min-width: 170px;\n  opacity: 0;\n  visibility: collapse;\n  transform: scale(0.9);\n  transform-origin: top left;\n  transition: var(--standard-transition);\n\n  &::before,\n  &::after {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    border-radius: inherit;\n    z-index: -1;\n  }\n\n  &::before {\n    backdrop-filter: brightness(3) blur(5px);\n  }\n\n  &::after {\n    background: var(--background);\n    opacity: 0.9;\n  }\n\n  span {\n    font-size: 0.8rem;\n    color: var(--text-color-secondary);\n    padding: 0.5rem 0.75rem;\n  }\n\n  .jenkins-button {\n    justify-content: flex-start;\n    gap: 0.75rem;\n    border-radius: var(--form-input-border-radius);\n    margin: 0 !important;\n    padding: 0.5rem 0.9rem !important;\n    min-height: 2.25rem !important;\n  }\n\n  &--visible {\n    opacity: 1;\n    visibility: visible;\n    transform: scale(1);\n  }\n}\n\n.jenkins-table__checkbox-dropdown__icon {\n  position: relative;\n  width: 1.1rem;\n  height: 1.1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: -0.15rem;\n    border: 0.1rem solid var(--text-color-secondary);\n    border-radius: 6px;\n    opacity: 0.25;\n  }\n\n  svg {\n    width: 0.9rem;\n    height: 0.9rem;\n\n    * {\n      stroke-width: 50px;\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.jenkins-section {\n  border-top: var(--jenkins-border);\n  padding: var(--section-padding) 0 0 0;\n  max-width: 1800px;\n\n  &:last-child {\n    padding-bottom: 0;\n  }\n\n  &:empty {\n    display: none;\n  }\n\n  &--no-border {\n    border-top: none;\n    padding-top: 0;\n  }\n\n  &--bottom-padding {\n    padding-bottom: var(--section-padding);\n  }\n}\n\n.jenkins-section__title {\n  margin: 0 0 var(--section-padding) 0;\n  font-size: 1rem;\n  font-weight: var(--font-bold-weight);\n}\n\n.jenkins-section__description {\n  // Tweaked margin so that it appears visually centred when placed next to `.jenkins-section__title`\n  margin: -0.8125rem 0 1rem;\n  color: var(--text-color-secondary);\n  line-height: 1.66;\n}\n\n.jenkins-section__items {\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-gap: var(--section-padding);\n\n  @media screen and (width >= 800px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (width >= 1300px) {\n    grid-template-columns: 1fr 1fr 1fr;\n  }\n\n  @media screen and (width >= 1800px) {\n    grid-template-columns: 1fr 1fr 1fr 1fr;\n  }\n}\n\n.jenkins-section__item a {\n  @include mixins.item($border: false);\n\n  display: flex;\n  text-decoration: none;\n  margin: 0;\n\n  &::before,\n  &::after {\n    inset: -0.65rem;\n  }\n\n  dl {\n    margin: 0;\n    padding: 0;\n    min-height: 48px;\n  }\n\n  .jenkins-section__item__icon {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0 1rem 0 0;\n    width: 3rem;\n    height: 3rem;\n    flex-shrink: 0;\n    color: var(--text-color);\n\n    &::before {\n      content: \"\";\n      position: absolute;\n      inset: 0;\n      border-radius: 100%;\n      pointer-events: none;\n      background: var(--item-background--active);\n      border: var(--jenkins-border--subtle);\n    }\n\n    img,\n    svg {\n      position: relative;\n      width: 50% !important;\n      height: 50% !important;\n      color: currentColor;\n    }\n\n    &__badge {\n      position: absolute;\n      top: -4px;\n      right: -4px;\n    }\n  }\n\n  dt {\n    font-size: 0.9375rem;\n    font-weight: var(--font-bold-weight);\n    margin: 0.1rem 0 0.2rem;\n    color: var(--text-color);\n  }\n\n  dd {\n    color: var(--text-color-secondary);\n    font-weight: normal;\n    line-height: 1.6;\n    margin: 0 0.66rem 0 0;\n    font-size: 0.9375rem;\n  }\n}\n", "@use \"../abstracts/mixins\";\n@use \"../base/breakpoints\";\n\n$background-outset: 0.7rem;\n\n#tasks,\n.subtasks {\n  display: flex;\n  flex-direction: column;\n  margin: var(--section-padding);\n  gap: 0.125rem;\n\n  @media (min-width: breakpoints.$tablet-breakpoint) {\n    margin-right: calc($background-outset);\n  }\n}\n\n.subtasks {\n  margin-top: 5px;\n  margin-bottom: 0;\n\n  &:empty {\n    display: none;\n  }\n}\n\n#side-panel {\n  .jenkins-app-bar {\n    margin-top: var(--section-padding);\n    margin-left: var(--section-padding);\n    margin-right: var(--section-padding);\n  }\n\n  & > #tasks > .jenkins-search-container {\n    margin-left: -$background-outset;\n    margin-right: -$background-outset;\n    margin-bottom: calc(var(--section-padding) / 2);\n\n    .jenkins-search__icon {\n      width: 2.8rem;\n      aspect-ratio: unset;\n    }\n\n    .jenkins-search__input {\n      padding-left: 2.6rem;\n    }\n  }\n}\n\n#side-panel .jenkins-search__results-container--visible .task-link {\n  opacity: 0.3;\n}\n\n#tasks .task {\n  margin: 0 calc($background-outset * -1);\n}\n\n#tasks .task .task-link {\n  @include mixins.item;\n\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  padding: 0.5rem $background-outset;\n  gap: 0.65rem;\n  width: 100%;\n  font-size: var(--font-size-sm);\n  color: var(--text-color);\n  margin: 0;\n  transition: opacity var(--standard-transition);\n\n  .task-icon-link {\n    display: inline-flex;\n\n    svg,\n    img {\n      width: 1.375rem !important;\n      height: 1.375rem !important;\n      color: var(--text-color);\n\n      * {\n        transition: stroke-width var(--standard-transition);\n      }\n    }\n  }\n\n  .task-icon-badge {\n    margin-left: auto;\n  }\n\n  .task-link-text {\n    display: contents;\n    word-break: break-word;\n  }\n\n  &--active {\n    font-weight: 450;\n    cursor: default;\n\n    svg * {\n      stroke-width: 35px;\n    }\n\n    &::before {\n      background-color: var(--item-background--active) !important;\n    }\n\n    &::after {\n      box-shadow: none !important;\n    }\n  }\n\n  &:not(:hover, &:active, &:focus, &--active) {\n    &::before {\n      border-color: transparent;\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n#side-panel .pane-frame {\n  border-radius: 1rem;\n  background: var(--card-background);\n  border: var(--card-border-width) solid var(--card-border-color);\n  margin-left: 10px;\n  margin-bottom: calc(var(--section-padding) / 2);\n  overflow: hidden;\n}\n\n#side-panel .pane-header {\n  font-size: var(--font-size-sm);\n  display: flex;\n}\n\n#side-panel .pane-footer {\n  font-size: var(--font-size-xs);\n}\n\n#side-panel .pane-header,\n#side-panel .pane-footer {\n  color: var(--link-dark-color);\n  background: transparent;\n  padding: 0.65rem 1rem;\n}\n\n#side-panel .pane-header-title {\n  display: inline-block;\n  flex: 1;\n  font-weight: var(--font-bold-weight);\n\n  & > div {\n    font-weight: normal;\n  }\n}\n\n#side-panel .pane-header .expand,\n#side-panel .pane-header .collapse {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: auto;\n  padding-left: 0.5rem;\n\n  svg {\n    width: 1rem;\n    height: 1rem;\n  }\n}\n\n#side-panel .pane-header-details {\n  font-weight: normal;\n  font-size: var(--font-size-xs);\n}\n\n#side-panel .pane-content {\n  font-size: var(--font-size-xs);\n}\n\n#side-panel .pane-content .pane {\n  font-size: var(--font-size-xs);\n  padding: 0.5rem 0.25rem;\n\n  &:first-child {\n    padding-left: 1rem;\n  }\n\n  &:last-child {\n    padding-right: 1rem;\n  }\n}\n\n#side-panel .pane-content > table {\n  padding: 0 1.25rem;\n}\n\n#side-panel .pane-content tbody tr {\n  &:not(:first-child) {\n    border-top: 1px solid var(--panel-border-color);\n  }\n}\n\n#side-panel .pane-header a,\n#side-panel .pane-footer a,\n#side-panel .pane-content a {\n  @include mixins.link-dark;\n\n  text-decoration: underline;\n}\n\n/**\n * Executors\n */\n#executors {\n  th.pane {\n    text-align: left;\n  }\n\n  .pane-header {\n    align-items: center;\n  }\n\n  .computer-caption {\n    display: flex;\n    gap: 5px;\n    padding: 0.5rem 1rem;\n\n    & > div {\n      margin-left: auto;\n      align-self: end;\n    }\n  }\n\n  .executors-cell {\n    padding: 0 1rem;\n  }\n\n  .executors-collapsed {\n    padding: 0 1rem 0.5rem;\n  }\n\n  .executor-row {\n    display: flex;\n    padding: 0 0 0.5rem;\n    align-items: center;\n  }\n\n  .executor-type {\n    width: 16px;\n    margin-right: 1ch;\n  }\n\n  .executor-cell {\n    width: 100%;\n  }\n\n  .executor-cell-table .pane {\n    margin: 0 !important;\n    padding: 0 !important;\n\n    & > div {\n      margin-right: 15px;\n    }\n  }\n\n  .executor-stop {\n    width: 16px;\n  }\n}\n", "$skip-link-block-padding: 0.6rem;\n$skip-link-inline-padding: 0.9rem;\n$skip-link-box-shadow: 0 0 50px -15px var(--yellow);\n\n.jenkins-skip-link {\n  position: fixed;\n  top: calc(var(--section-padding) - $skip-link-block-padding);\n  left: calc(var(--section-padding) - $skip-link-inline-padding);\n  z-index: 2000;\n  background: var(--background);\n  box-shadow:\n    0 0 0 1rem transparent,\n    $skip-link-box-shadow;\n  color: var(--text-color) !important;\n  padding: $skip-link-block-padding $skip-link-inline-padding;\n  border-radius: var(--form-input-border-radius);\n  outline: none;\n  text-decoration: none !important;\n  transition:\n    var(--elastic-transition),\n    box-shadow var(--standard-transition) 0.1s;\n  opacity: 0;\n  scale: 80%;\n  pointer-events: none;\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    border-radius: inherit;\n    background: var(--yellow);\n    opacity: 0.05;\n    transition: inherit;\n  }\n\n  &:hover {\n    &::before {\n      opacity: 0.1;\n    }\n  }\n\n  &:active {\n    &::before {\n      opacity: 0.2;\n    }\n  }\n\n  &:focus-visible {\n    box-shadow:\n      0 0 0 0.2rem var(--yellow),\n      $skip-link-box-shadow;\n    opacity: 1;\n    scale: 100%;\n    text-decoration: none !important;\n    pointer-events: unset;\n  }\n}\n", ".jenkins-spinner {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  font-size: var(--font-size-sm);\n  font-weight: var(--font-bold-weight);\n  margin: 0;\n\n  &::before,\n  &::after {\n    content: \"\";\n    display: inline-block;\n    width: 0.9lh;\n    height: 0.9lh;\n    border-radius: 100%;\n    border: 0.125lh solid currentColor;\n  }\n\n  &::before {\n    position: relative;\n    margin-right: 0.5lh;\n    opacity: 0.3;\n    border-color: var(--text-color-secondary);\n  }\n\n  &::after {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    translate: 0 -0.45lh;\n    clip-path: inset(0 0 50% 50%);\n    animation: loading-spinner 1s infinite linear;\n\n    @media (prefers-reduced-motion) {\n      animation-duration: 2s;\n    }\n  }\n\n  &:empty {\n    &::before {\n      margin-right: 0;\n    }\n  }\n}\n\n@keyframes loading-spinner {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.behavior-loading {\n  position: fixed;\n  display: flex !important;\n  align-items: center;\n  justify-content: center;\n  inset: 0;\n  z-index: 999;\n  backdrop-filter: blur(15px);\n  transition: var(--standard-transition);\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    background: var(--background);\n    opacity: 0.95;\n  }\n\n  .jenkins-spinner {\n    animation: fade-in-jenkins-spinner 0.4s ease;\n  }\n\n  &--hidden {\n    opacity: 0;\n    visibility: collapse;\n    pointer-events: none;\n\n    .fade-in-jenkins-spinner {\n      opacity: 0.5;\n      transform: scale(0.75);\n    }\n  }\n}\n\n@keyframes fade-in-jenkins-spinner {\n  from {\n    opacity: 0.5;\n    transform: scale(0.75);\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n.jenkins-table {\n  --table-padding: 0.55rem;\n\n  position: relative;\n  width: 100%;\n  background: var(--table-background);\n  border-radius: calc(\n    var(--table-border-radius) + (var(--card-border-width) * 2)\n  );\n  border: calc(var(--card-border-width) * 2) solid var(--table-background);\n  border-bottom-width: var(--card-border-width);\n  border-spacing: 0 var(--card-border-width);\n  background-clip: padding-box;\n  margin-bottom: var(--section-padding);\n\n  // The '::before' pseudo-element is causing overflow issues in Firefox, so disable it for that browser\n  @supports (not (-moz-appearance: none)) {\n    &::before {\n      content: \"\";\n      position: absolute;\n      inset: -3px -3px -2px;\n      border: var(--jenkins-border--subtle);\n      border-radius: inherit;\n      pointer-events: none;\n    }\n  }\n\n  * {\n    -webkit-border-horizontal-spacing: 0;\n    -webkit-border-vertical-spacing: 0;\n  }\n\n  & > thead {\n    & > tr {\n      & > th {\n        color: var(--table-header-foreground);\n        text-align: left;\n        padding-top: calc(var(--table-padding) * 0.9);\n        padding-bottom: calc((var(--table-padding) * 0.9) + 2px);\n        padding-left: 1.6rem;\n        font-size: var(--font-size-sm);\n        font-weight: var(--font-bold-weight);\n\n        &[align=\"center\"] {\n          text-align: center;\n        }\n\n        &[align=\"right\"] {\n          text-align: right;\n        }\n\n        &:first-of-type {\n          padding-left: calc(var(--table-padding) * 2);\n        }\n\n        &:last-of-type {\n          padding-right: var(--table-padding);\n        }\n\n        a {\n          color: var(--table-header-foreground);\n          font-weight: 700;\n\n          span.sortarrow {\n            width: 24px;\n          }\n        }\n\n        svg {\n          vertical-align: middle;\n          width: 0.8rem;\n          height: 0.8rem;\n        }\n      }\n    }\n  }\n\n  & > tbody {\n    & > tr {\n      color: var(--table-body-foreground);\n\n      & > td {\n        background: var(--table-body-background);\n        vertical-align: middle;\n        padding: var(--table-padding) 0 var(--table-padding) 1.6rem;\n        height: 3rem;\n\n        &:first-of-type {\n          padding-left: calc(var(--table-padding) * 2);\n        }\n\n        &:last-of-type {\n          padding-right: var(--table-padding);\n        }\n      }\n\n      // Style the rows so that the first and last have a larger border radius\n      & > td:first-of-type {\n        border-top-left-radius: var(--table-row-border-radius);\n        border-bottom-left-radius: var(--table-row-border-radius);\n      }\n\n      & > td:last-of-type {\n        border-top-right-radius: var(--table-row-border-radius);\n        border-bottom-right-radius: var(--table-row-border-radius);\n      }\n\n      // First row\n      &:first-of-type {\n        & > td:first-of-type {\n          border-top-left-radius: var(--table-border-radius);\n        }\n\n        & > td:last-of-type {\n          border-top-right-radius: var(--table-border-radius);\n        }\n      }\n\n      // Last row\n      &:last-of-type {\n        & > td:first-of-type {\n          border-bottom-left-radius: var(--table-border-radius);\n        }\n\n        & > td:last-of-type {\n          border-bottom-right-radius: var(--table-border-radius);\n        }\n      }\n    }\n  }\n\n  &__cell__button-wrapper {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n\n  &__cell--tight {\n    padding-left: 0 !important;\n    text-align: right !important;\n    white-space: nowrap;\n    width: 4rem;\n  }\n\n  &__cell--checkbox {\n    padding: calc(var(--table-padding) * 2) !important;\n    vertical-align: top !important;\n    width: calc((var(--table-padding) * 2) + 22px);\n  }\n\n  &__cell--no-wrap {\n    white-space: nowrap;\n  }\n\n  &__button {\n    display: inline-flex;\n  }\n\n  &__link,\n  .sortheader {\n    display: inline-flex;\n  }\n\n  &__link {\n    .jenkins-menu-dropdown-chevron {\n      top: 0;\n    }\n  }\n\n  .jenkins-button {\n    margin: -10px 0;\n    padding: 0.5rem 0.75rem;\n    min-height: 1.75rem;\n\n    // Increase the size of symbols compared to regular buttons\n    svg {\n      width: 1.5rem !important;\n      height: 1.5rem !important;\n    }\n  }\n\n  &__button,\n  &__icon {\n    svg,\n    .build-status-icon__wrapper,\n    img {\n      width: 1.5rem !important;\n      height: 1.5rem !important;\n    }\n  }\n\n  &--medium {\n    --table-padding: 0.4rem !important;\n\n    tbody > tr > td {\n      height: 40px;\n    }\n\n    .jenkins-table__button,\n    .jenkins-table__link,\n    .jenkins-table__icon {\n      svg,\n      .build-status-icon__wrapper,\n      img {\n        width: 1.3rem !important;\n        height: 1.3rem !important;\n      }\n    }\n\n    .jenkins-button {\n      padding: 0.4rem 0.6rem;\n\n      svg {\n        width: 1.3rem !important;\n        height: 1.3rem !important;\n      }\n    }\n  }\n\n  &--auto-width {\n    width: auto !important;\n  }\n\n  &--small {\n    --table-padding: 0.2rem;\n\n    tbody > tr > td {\n      height: 34px;\n    }\n\n    .jenkins-table__button,\n    .jenkins-table__link,\n    .jenkins-table__icon {\n      svg,\n      .build-status-icon__wrapper,\n      img {\n        width: 1rem !important;\n        height: 1rem !important;\n      }\n    }\n\n    .jenkins-button {\n      padding: 0.3rem 0.5rem;\n\n      svg {\n        width: 1rem !important;\n        height: 1rem !important;\n      }\n    }\n  }\n\n  &__button,\n  &__link {\n    color: var(--link-color);\n  }\n\n  &__button,\n  .sortheader,\n  &__link {\n    @include mixins.item($border: false);\n\n    align-items: center;\n    justify-content: center;\n    appearance: none;\n    outline: none;\n    border: none;\n    margin: 0;\n    padding: 0;\n    cursor: pointer;\n    color: inherit;\n    font-weight: inherit;\n    font-size: inherit;\n    background: transparent;\n    text-decoration: none !important;\n    line-height: 1 !important;\n    transition: var(--standard-transition);\n\n    &::before,\n    &::after {\n      inset: -7px -10px;\n      border-radius: 6px;\n    }\n  }\n\n  &__badge {\n    @include mixins.item;\n\n    --item-background: color-mix(in sRGB, currentColor 5%, transparent);\n    --item-background--hover: color-mix(in sRGB, currentColor 10%, transparent);\n    --item-background--active: color-mix(\n      in sRGB,\n      currentColor 15%,\n      transparent\n    );\n    --item-box-shadow--focus: color-mix(in sRGB, currentColor 5%, transparent);\n\n    margin-left: 1rem !important;\n    border-radius: 13px;\n\n    &::before,\n    &::after {\n      inset: -5px -8px;\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n$border-radius: 19px;\n\n.tabBarFrame {\n  position: relative;\n}\n\n.tabBar {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  flex-wrap: wrap;\n  background: var(--button-background);\n  border-radius: $border-radius;\n  padding: 2px;\n  margin-bottom: var(--section-padding);\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    border: var(--jenkins-border--subtle);\n    border-radius: inherit;\n    pointer-events: none;\n  }\n\n  .tab {\n    float: left;\n  }\n}\n\n.tabBar .tab a {\n  @include mixins.item($border: false);\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 34px;\n  padding: 0 1.2rem;\n  border-radius: $border-radius;\n  color: var(--tabs-item-foreground);\n  font-weight: normal;\n  font-size: var(--font-size-sm);\n  min-width: 3.75rem;\n\n  &::before,\n  &::after {\n    inset: 2px;\n  }\n}\n\n.tabBar .tab .addTab svg {\n  width: 1.125rem;\n  height: 1.125rem;\n}\n\n.tabBar .tab [type=\"radio\"] {\n  display: none;\n}\n\n.tabBar .tab.active a {\n  z-index: 2;\n  cursor: default;\n  font-weight: 450;\n  color: var(--tabs-item-foreground--active);\n\n  &::before {\n    background-clip: padding-box;\n    background-color: var(--tabs-item-background--selected) !important;\n    border: var(--jenkins-border--subtle) !important;\n  }\n\n  &::after {\n    display: none;\n  }\n\n  &::before,\n  &::after {\n    inset: 0;\n  }\n}\n\n.jenkins-tab-pane__title {\n  font-size: 1.6rem;\n  margin: 2.2rem 0 1.4rem;\n  padding: 0;\n}\n", ".tippy-box[data-theme~=\"tooltip\"] {\n  color: var(--tooltip-color);\n  padding: 0.45rem 0.8rem;\n  border-radius: 0.66rem;\n  box-shadow: var(--tooltip-box-shadow);\n  font-size: 0.75rem;\n  line-height: 1.6;\n  max-width: min(50vw, 1000px) !important;\n  white-space: pre-line;\n  z-index: 0;\n  background: color-mix(in sRGB, var(--card-background) 85%, transparent);\n  backdrop-filter: var(--tooltip-backdrop-filter);\n\n  .tippy-content {\n    padding: 0;\n  }\n\n  // We style tables as they have additional margin/border radius when in tooltips\n  .jenkins-tooltip--table-wrapper {\n    background-color: rgba(black, 0.05);\n    margin: -0.45rem -0.8rem;\n    border-radius: 0.6rem;\n  }\n\n  .jenkins-table {\n    --table-background: transparent;\n    --table-border-radius: 8px;\n\n    margin: 0;\n    width: 450px;\n  }\n\n  .jenkins-keyboard-shortcut {\n    &::after {\n      content: \"\";\n      position: absolute;\n      inset: 0;\n      border-radius: 0.375rem;\n      border: var(--jenkins-border-width) solid var(--text-color-secondary);\n      opacity: 0.3;\n      mask-image: linear-gradient(\n        -45deg,\n        transparent 40%,\n        white,\n        transparent 60%\n      );\n      mask-size: 200% 200%;\n      animation: shortcut-glow-animation 1.25s forwards linear;\n    }\n\n    @keyframes shortcut-glow-animation {\n      0% {\n        opacity: 0;\n        mask-position: 100% 100%;\n      }\n\n      50% {\n        opacity: 1;\n      }\n\n      100% {\n        opacity: 0;\n        mask-position: 0;\n      }\n    }\n  }\n}\n\n.tippy-box[data-animation=\"tooltip\"][data-state=\"hidden\"] {\n  $translate: 2px;\n  $scale: 0.99;\n\n  opacity: 0;\n  transform: scale($scale);\n\n  &[data-placement^=\"top\"] {\n    transform-origin: bottom;\n    transform: translateY($translate) scale($scale);\n  }\n\n  &[data-placement^=\"bottom\"] {\n    transform-origin: top;\n    transform: translateY(-$translate) scale($scale);\n  }\n}\n\n// Workaround for NG Warnings which supports modern Tippy tooltips and a custom solution,\n// hide the custom solution\n.jenkins-table .healthReportDetails {\n  display: none !important;\n}\n\n.jenkins-keyboard-shortcut {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 1.7em;\n  min-height: 1.7em;\n  padding-inline: 0.55ch;\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    border-radius: 0.3rem;\n    border: var(--jenkins-border-width) solid var(--input-border);\n  }\n\n  svg {\n    width: 1em;\n    height: 1em;\n  }\n}\n\n.jenkins-keyboard-shortcut__tooltip {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.6ch;\n}\n", ".jenkins-checkbox-help-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n}\n\n.jenkins-checkbox + a.jenkins-help-button {\n  vertical-align: top;\n}\n\n.jenkins-checkbox {\n  position: relative;\n  display: inline-flex;\n}\n\n.jenkins-checkbox input {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n\n  // If margin is set to a negative value it can cause text to be announced in\n  // the wrong order in VoiceOver for OSX\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  clip-path: inset(50%);\n\n  &:not(:disabled) {\n    &:active,\n    &:focus {\n      & + label {\n        &::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 5px var(--focus-input-border);\n        }\n      }\n    }\n\n    &:checked {\n      &:active,\n      &:focus {\n        & + label {\n          &::before {\n            box-shadow:\n              var(--form-input-glow--focus),\n              inset 0 0 0 12px var(--focus-input-border);\n          }\n        }\n      }\n    }\n  }\n\n  &:checked {\n    &:active,\n    &:focus {\n      & + label {\n        &::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 12px var(--focus-input-border);\n        }\n      }\n    }\n  }\n\n  &:checked {\n    & + label {\n      &:active,\n      &:focus {\n        &::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 12px var(--focus-input-border);\n        }\n      }\n    }\n\n    & + label {\n      &::before {\n        box-shadow:\n          var(--form-input-glow),\n          inset 0 0 0 12px var(--focus-input-border);\n      }\n\n      &::after {\n        transform: scale(1);\n      }\n    }\n  }\n\n  &:disabled {\n    & + label {\n      cursor: not-allowed;\n\n      &::before {\n        opacity: 0.35 !important;\n        box-shadow:\n          var(--form-input-glow),\n          inset 0 0 0 2px var(--input-border) !important;\n      }\n    }\n\n    &:checked {\n      & + label {\n        &::before {\n          box-shadow:\n            var(--form-input-glow),\n            inset 0 0 0 12px var(--focus-input-border) !important;\n        }\n\n        &::after {\n          transform: scale(1) !important;\n        }\n      }\n    }\n  }\n}\n\n.jenkins-checkbox label {\n  position: relative;\n  display: inline-flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n  margin: 0;\n  cursor: pointer;\n  line-height: 22px;\n  font-weight: var(--form-label-font-weight);\n\n  &::before {\n    content: \"\";\n    display: inline-block;\n    position: relative;\n    min-width: 22px;\n    min-height: 22px;\n    border-radius: 6px;\n    transition: var(--standard-transition);\n    margin-right: 11px;\n    box-shadow:\n      var(--form-input-glow),\n      inset 0 0 0 var(--jenkins-border-width) var(--input-border);\n    background: var(--input-color);\n  }\n\n  &::after {\n    content: \"\";\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 22px;\n    height: 22px;\n    background: var(--background);\n    mask-image: url(\"data:image/svg+xml;charset=UTF-8,%3c?xml version='1.0' encoding='UTF-8'?%3e%3csvg width='384px' height='320px' viewBox='0 0 384 320' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3ePath%3c/title%3e%3cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3e%3cpath d='M327.917546,10.9278525 C339.555371,-2.37251966 359.771775,-3.72027991 373.072147,7.91754577 C386.239516,19.4389932 387.692129,39.368305 376.427694,52.671077 L376.082454,53.0721475 L152.082454,309.072147 C140.014868,322.863675 118.889432,323.700972 105.767743,311.015951 L105.372583,310.627417 L9.372583,214.627417 C-3.12419433,202.13064 -3.12419433,181.86936 9.372583,169.372583 C21.7443926,157.000773 41.7261905,156.877055 54.2501999,169.001429 L54.627417,169.372583 L126.441,241.186 L327.917546,10.9278525 Z' id='Path' fill='%23FF0000' fill-rule='nonzero'%3e%3c/path%3e%3c/g%3e%3c/svg%3e\");\n    mask-size: 10px 10px;\n    mask-repeat: no-repeat;\n    mask-position: center;\n    transition: var(--elastic-transition);\n    transform: scale(0);\n  }\n\n  &:empty {\n    &::before {\n      margin-right: 0;\n    }\n  }\n\n  &:hover {\n    &::before {\n      box-shadow:\n        var(--form-input-glow),\n        inset 0 0 0 5px var(--input-border-hover);\n    }\n  }\n\n  &:active,\n  &:focus {\n    &::before {\n      box-shadow:\n        var(--form-input-glow--focus),\n        inset 0 0 0 5px var(--focus-input-border);\n    }\n  }\n}\n\n.jenkins-checkbox__description {\n  margin-left: 34px;\n  color: var(--text-color-secondary);\n  line-height: 1.66;\n}\n", ".CodeMirror {\n  display: block;\n  background: var(--input-color);\n  border: var(--jenkins-border-width) solid var(--input-border);\n  border-radius: var(--form-input-border-radius);\n  width: 100%;\n  box-shadow: var(--form-input-glow);\n  transition:\n    var(--standard-transition),\n    height 0s;\n  cursor: text;\n  margin-bottom: var(--section-padding);\n\n  // Override the defaults of codemirror.css\n  pre {\n    font-family: var(--font-family-mono) !important;\n    font-weight: 500 !important;\n    line-height: 1.66 !important;\n  }\n\n  &:hover {\n    border-color: var(--input-border-hover);\n  }\n\n  &:active,\n  &:focus-within {\n    outline: none;\n    border-color: var(--focus-input-border);\n    box-shadow: var(--form-input-glow--focus);\n  }\n\n  textarea {\n    background: transparent;\n    border: none;\n    outline: none;\n  }\n\n  .cm-variable {\n    color: var(--text-color) !important;\n  }\n\n  .CodeMirror-selected {\n    background-color: var(--selection-color) !important;\n  }\n\n  .CodeMirror-lines {\n    padding: var(--form-input-padding);\n  }\n\n  .CodeMirror-gutter-text {\n    padding: 0.5rem calc(0.625rem * 0.5) 0.625rem 0.5rem;\n    color: var(--text-color-secondary);\n  }\n}\n\n.jenkins-codemirror-resizer {\n  position: relative;\n  width: 10px;\n  height: 10px;\n  margin: calc(-2rem - 11px) 3px 2rem auto;\n  z-index: 10;\n  cursor: ns-resize;\n  background: currentColor;\n  mask-image: url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='20px' height='20px' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cline x1='19' y1='12' x2='12' y2='19' id='Path' stroke='%23979797' stroke-width='2'%3E%3C/line%3E%3Cline x1='1' y1='18' x2='17.9705627' y2='1.02943725' id='Path-2' stroke='%23979797' stroke-width='2'%3E%3C/line%3E%3C/g%3E%3C/svg%3E\");\n  mask-size: 7px 7px;\n  mask-position: 3px 3px;\n  mask-repeat: no-repeat;\n  opacity: 0.75;\n}\n", ".jenkins-file-upload {\n  position: relative;\n  width: 100%;\n  margin: -10px 0 0 -10px;\n  padding: 10px 0 10px 10px;\n  outline: none;\n  background: transparent;\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    display: block;\n    left: calc(10px + 0.9rem);\n    width: 1rem;\n    height: 36px;\n    background: currentColor;\n    mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M320 367.79h76c55 0 100-29.21 100-83.6s-53-81.47-96-83.6c-8.89-85.06-71-136.8-144-136.8-69 0-113.44 45.79-128 91.2-60 5.7-112 43.88-112 106.4s54 106.4 120 106.4h56' fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='40'/%3E%3Cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='40' d='M320 255.79l-64-64-64 64M256 448.21V207.79'/%3E%3C/svg%3E\");\n    mask-position: center;\n    mask-repeat: no-repeat;\n    pointer-events: none;\n  }\n\n  &::file-selector-button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    outline: none;\n    margin: 0 1rem 0 0;\n    padding: 0.5rem 0.85rem 0.5rem 2.5rem;\n\n    // Firefox doesn't support pseudo elements on inputs so don't increase padding to accommodate\n    @supports (-moz-appearance: none) {\n      padding: 0.5rem 0.85rem;\n    }\n\n    font-size: var(--font-size-sm);\n    font-weight: normal;\n    color: var(--text-color);\n    border-radius: var(--form-input-border-radius);\n    cursor: pointer;\n    min-height: 36px;\n    white-space: nowrap;\n    background: var(--button-background);\n    transition: var(--standard-transition);\n    box-shadow: var(--form-input-glow);\n    border: var(--jenkins-border--subtle);\n\n    &:hover {\n      background: var(--button-background--hover);\n    }\n\n    &:active {\n      background: var(--button-background--active);\n      box-shadow: 0 0 0 4px var(--button-box-shadow--focus);\n    }\n  }\n\n  &:focus-visible {\n    &::file-selector-button {\n      box-shadow: 0 0 0 0.2rem var(--text-color) !important;\n    }\n  }\n}\n", ".jenkins-input {\n  display: block;\n  background: var(--input-color);\n  border: var(--jenkins-border-width) solid var(--input-border);\n  padding: var(--form-input-padding);\n  border-radius: var(--form-input-border-radius);\n  width: 100%;\n  min-height: 2.375rem;\n  box-shadow: var(--form-input-glow);\n\n  // Set height transition to 0s as vertical resizing has a delay/lag otherwise\n  transition:\n    all var(--standard-transition),\n    height 0s,\n    padding 0s;\n\n  &:not(:disabled) {\n    &:hover {\n      border-color: var(--input-border-hover);\n    }\n\n    &:active,\n    &:focus {\n      outline: none;\n      border-color: var(--focus-input-border);\n      box-shadow: var(--form-input-glow--focus);\n    }\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n}\n\ninput,\ntextarea,\nselect {\n  color: var(--text-color);\n  background-color: var(--input-color);\n  font-family: inherit;\n}\n", "@use \"../abstracts/mixins\";\n\n.jenkins-form {\n  max-width: var(--form-item-max-width);\n}\n\n.jenkins-page-description {\n  font-size: 0.9375rem;\n  color: var(--text-color-secondary);\n  margin: calc(var(--section-padding) * -0.25) 0 var(--section-padding) 0;\n  line-height: 1.66;\n}\n\n.jenkins-fieldset {\n  border: none;\n  margin: 0;\n  padding: 0;\n\n  .jenkins-form-item:last-of-type {\n    margin-bottom: 0;\n  }\n}\n\n.jenkins-form-item {\n  max-width: var(--form-item-max-width);\n  margin-bottom: var(--section-padding);\n\n  // Workaround for float:right button controls\n  // (eg Global Credentials' Verify Configuration button being hidden by the floating submit bar)\n  &::after {\n    content: \" \"; /* Older browser do not support empty content */\n    visibility: hidden;\n    display: block;\n    height: 0;\n    clear: both;\n  }\n\n  &--tight + .jenkins-form-item--tight {\n    margin-top: -0.9rem;\n  }\n\n  &--small {\n    max-width: var(--form-item-max-width--small);\n  }\n\n  &--medium {\n    max-width: var(--form-item-max-width--medium);\n  }\n}\n\n.jenkins-form-label {\n  display: flex;\n  align-items: center;\n  font-weight: var(--form-label-font-weight);\n  margin-top: 0;\n  margin-bottom: 0.5rem;\n  padding-inline: 0;\n}\n\n.jenkins-form-description {\n  display: block;\n\n  // Tweaked margin so that it appears visually centred when placed next to `.jenkins-form-label`\n  margin: -0.2rem 0 0.5rem;\n  color: var(--text-color-secondary);\n  line-height: 1.66;\n}\n\n.jenkins-quote {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  background-color: var(--button-background);\n  border-radius: var(--form-input-border-radius);\n  outline: var(--jenkins-border--subtle);\n  outline-offset: calc(var(--jenkins-border-width) * -1);\n  padding: 0 1rem;\n  gap: 1.5rem;\n\n  .jenkins-copy-button {\n    margin-top: 0.25rem;\n    margin-bottom: 0.25rem;\n    margin-right: -0.75rem;\n    padding: 0;\n    width: 2.5rem;\n    height: 2.5rem;\n    border-radius: calc(var(--form-input-border-radius) - 0.25rem);\n  }\n\n  &--monospace {\n    font-family: var(--font-family-mono);\n  }\n}\n\n.jenkins-help-button {\n  @include mixins.item;\n\n  --item-background: var(--button-background);\n\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  margin-left: 1ch;\n  border-radius: 100%;\n\n  span {\n    display: inline-block;\n    width: 20px;\n    height: 20px;\n    min-width: 20px;\n    min-height: 20px;\n    background: var(--text-color);\n    color: transparent;\n    mask-image: url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='262px' height='482px' viewBox='0 0 262 482' version='1.1' xmlns='http://www.w3.org/2000/svg'%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpath d='M69.9217381,109.622622 L69.9347383,109.509526 L69.9838606,109.158807 L70.0463523,108.766768 C70.2927563,107.286617 70.7336,105.509397 71.3554148,103.600821 C73.4488365,97.1753443 76.974163,91.0239622 81.9750299,85.3670497 C84.5508811,82.453282 87.4853512,79.7201177 90.8001215,77.1662697 C100.73686,69.5111158 114.003751,65.6584363 131.476234,65.4354882 C133.731084,65.4096204 135.996257,65.4453704 138.232058,65.5407008 L139.478148,65.6000218 C140.992957,65.679511 142.536904,65.7906863 144.04662,65.9294921 L145.210323,66.0424992 C145.886163,66.1116384 146.60564,66.1925244 147.313561,66.2795642 L148.414194,66.420922 L149.377674,66.5545415 L150.375686,66.7050246 L151.398071,66.8714663 L152.049055,66.9841252 L152.731845,67.1083183 L153.642724,67.2845248 L154.549432,67.4714994 C155.036905,67.5756456 155.574038,67.6978797 156.090422,67.8232778 L156.917997,68.0311744 C158.666031,68.484436 160.136789,68.9816723 161.141416,69.4394425 C170.146558,73.5618171 177.044937,78.1235016 182.224862,83.4639805 C188.857993,90.3027083 191.999176,97.8308192 191.999176,107.499034 C191.999176,119.466025 188.796962,127.918279 181.370643,136.250976 C181.093904,136.561492 180.816698,136.867422 180.538309,137.169526 L180.089688,137.652102 L179.736214,138.026258 L179.002023,138.784568 L178.579132,139.213445 L178.134441,139.655368 L177.707007,140.071619 L177.021999,140.726828 L176.298991,141.400731 L175.788688,141.867317 L175.318979,142.286805 L174.54297,142.970055 L173.990783,143.446079 L173.448394,143.905722 L172.625948,144.588916 L171.799887,145.262466 L170.811596,146.050172 L169.864089,146.789794 L169.222049,147.28309 L168.549302,147.793886 L167.493519,148.583956 L166.782836,149.108414 L165.65246,149.931951 L164.484527,150.770371 L163.266904,151.632649 L161.555386,152.827104 L160.682666,153.429152 L159.298283,154.375903 L157.864408,155.346877 L155.333592,157.040637 L153.750968,158.088827 L151.524574,159.551762 L148.580787,161.468911 L144.800696,163.908618 C105.775292,188.991858 87.997951,218.376069 87.997951,257.047472 L87.997951,283.286279 L88.0016284,283.748414 C88.3121212,301.66652 103.883328,316 122.998363,316 C142.328557,316 157.998775,301.353568 157.998775,283.286279 L157.998775,257.047472 L158.001383,256.432449 C158.064604,248.820487 159.375708,243.378146 162.208358,238.456376 C165.925109,231.998468 172.860638,225.357973 183.865352,218.206756 L186.747913,216.348707 L189.667785,214.457346 L192.470459,212.628123 L195.158954,210.857753 L197.739048,209.141153 L199.612339,207.881962 L201.424999,206.651521 L203.184757,205.444126 L204.893987,204.257626 L206.014818,203.471391 L207.63679,202.320739 L208.708154,201.551674 L209.751591,200.795045 L210.776652,200.043916 L211.783987,199.29767 L212.774236,198.555696 L213.748026,197.81739 L214.705977,197.082156 L215.983535,196.08689 L217.035512,195.253661 L217.94273,194.524595 L218.836751,193.796023 L219.718133,193.067399 L220.587422,192.33819 L221.445157,191.607874 L222.291869,190.87594 L223.128081,190.141889 L224.349696,189.048475 L225.176062,188.294328 L225.979581,187.549096 L227.15568,186.436436 L227.953672,185.666523 L228.73078,184.904536 L229.501513,184.136777 L230.633743,182.986693 L231.403964,182.189294 L232.15663,181.397931 L232.905389,180.598844 L233.650817,179.791592 L234.393505,178.975725 L235.134055,178.150785 C253.063009,158.033696 262,134.778085 262,107.499034 C262,81.4013251 252.315995,58.3143983 234.08637,39.5197386 C222.737894,27.8195118 209.350637,18.7642961 192.813373,11.0878005 L191.857565,10.6472715 C176.144833,3.48753386 154.016249,-0.255558118 130.568693,0.0135609187 C97.385459,0.43694412 69.212387,9.00946009 46.2762841,26.6791815 C39.5211589,31.8836333 33.4271839,37.5663685 28.0119541,43.6920026 C17.038175,56.105392 9.16079603,69.8997291 4.39056696,84.5413054 C2.80350137,89.4125897 1.62984552,94.1870463 0.865737279,98.781619 L0.782196688,99.2931802 L0.614828552,100.383438 L0.559658337,100.765066 L0.426811303,101.749189 L0.318522027,102.638537 L0.226704214,103.487005 L0.123646908,104.617718 L0.0652556787,105.43714 L0.0342145373,105.994277 C-0.81250758,124.122996 14.1596716,139.394013 33.4714881,140.181654 L33.9656162,140.198367 L34.5332314,140.20872 C53.0445487,140.409453 68.5022596,127.170521 69.8964399,109.989591 L69.9217381,109.622622 Z' fill='currentColor' fill-rule='nonzero'%3E%3C/path%3E%3Ccircle fill='currentColor' cx='123' cy='434' r='48'%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n    mask-size: 45% 45%;\n    mask-position: center;\n    mask-repeat: no-repeat;\n  }\n}\n\n.jenkins-select-help {\n  position: relative;\n}\n\n.jenkins-select-help a.jenkins-help-button {\n  position: absolute;\n  top: -3.7rem;\n  right: -2rem;\n}\n\n.jenkins-edited-section-label {\n  color: var(--text-color-secondary);\n  display: inline-flex;\n  align-items: center;\n  gap: 0.45rem;\n  margin-left: 0.4rem;\n  font-size: 0.8rem;\n  cursor: default;\n\n  svg {\n    width: 1rem;\n    height: 1rem;\n  }\n}\n\n.jenkins-instructions {\n  display: flex;\n  flex-direction: column;\n  list-style-type: none;\n  padding: 0;\n  margin: 0 0 var(--section-padding);\n  gap: var(--section-padding);\n  counter-reset: section;\n\n  li {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    align-items: start;\n    gap: 0.5rem;\n    padding-left: 3rem;\n    padding-top: 0.35rem;\n\n    .jenkins-instructions__label {\n      font-weight: var(--font-bold-weight);\n      margin: 0;\n    }\n\n    &::before {\n      counter-increment: section;\n      content: counters(section, \"\");\n      position: absolute;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      top: 0;\n      left: 0;\n      background: var(--text-color);\n      width: 2rem;\n      height: 2rem;\n      color: var(--background);\n      border-radius: 100px;\n      text-align: center;\n      font-weight: var(--font-bold-weight);\n    }\n  }\n}\n", "$jenkins-radio-size: 1.375rem;\n$jenkins-radio-border-size: 1.5px;\n$jenkins-radio-border-hover-size: 0.3125rem;\n$jenkins-radio-border-active-size: 0.5rem;\n$jenkins-radio-border-checked-size: 0.4rem;\n$jenkins-radio-glow-size: 0.5rem;\n\n.jenkins-radio-help-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n}\n\n.jenkins-radio {\n  &:not(:last-of-type) {\n    margin-bottom: 0.875rem;\n  }\n\n  &__input {\n    position: absolute;\n    opacity: 0;\n    margin-top: 0.625rem;\n\n    &:not(:checked) {\n      &:hover {\n        & + label::before {\n          box-shadow:\n            0 0 0 $jenkins-radio-glow-size transparent,\n            inset 0 0 0 $jenkins-radio-border-hover-size\n              var(--input-border-hover);\n        }\n      }\n\n      &:focus-visible,\n      &:focus,\n      &:active {\n        & + label::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 $jenkins-radio-border-active-size\n              var(--focus-input-border);\n        }\n      }\n    }\n\n    &:checked {\n      & + label {\n        cursor: default;\n\n        &::before {\n          box-shadow:\n            0 0 0 $jenkins-radio-glow-size transparent,\n            inset 0 0 0 $jenkins-radio-border-checked-size\n              var(--focus-input-border);\n        }\n      }\n\n      &:not(:disabled) {\n        &:focus-visible,\n        &:focus,\n        &:active {\n          & + label::before {\n            box-shadow:\n              var(--form-input-glow--focus),\n              inset 0 0 0 $jenkins-radio-border-active-size\n                var(--focus-input-border);\n          }\n        }\n      }\n    }\n\n    &:disabled {\n      & + label {\n        cursor: not-allowed;\n\n        &::before {\n          opacity: 0.35;\n        }\n      }\n    }\n  }\n\n  &__label {\n    position: relative;\n    display: inline-block;\n    margin-bottom: 0;\n    padding: 0 0 0 2rem;\n    cursor: pointer;\n    font-weight: var(--form-label-font-weight);\n\n    // remove 300ms pause on mobile\n    touch-action: manipulation;\n\n    &::before {\n      content: \"\";\n      box-sizing: border-box;\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: $jenkins-radio-size;\n      height: $jenkins-radio-size;\n      border-radius: 50%;\n      background: var(--input-color);\n      box-shadow:\n        0 0 0 $jenkins-radio-glow-size transparent,\n        inset 0 0 0 $jenkins-radio-border-size var(--input-border);\n      transition: box-shadow var(--standard-transition);\n    }\n  }\n\n  &__description {\n    margin: 0 0 0 2rem;\n    color: var(--text-color-secondary);\n    line-height: 1.66;\n  }\n\n  &__input:not(:checked) + &__label + &__children {\n    display: none;\n  }\n}\n", "/* ========================= repeatable elements ========================= */\n\n.repeated-chunk {\n  position: relative;\n  border: 2px dashed var(--input-border);\n  padding: 1rem;\n  border-radius: var(--form-input-border-radius);\n  margin-bottom: 1rem;\n  margin-top: 1rem;\n  transition:\n    opacity 0.2s ease-in,\n    max-height 0.2s ease-in;\n}\n\n.repeated-chunk.fade-in,\n.repeated-chunk.fade-out {\n  opacity: 0;\n}\n\n.repeated-chunk .show-if-last {\n  visibility: hidden;\n}\n\n.repeated-chunk.last .show-if-last {\n  visibility: visible;\n}\n\n.repeated-chunk .show-if-not-last {\n  visibility: visible;\n}\n\n.repeated-chunk.last .show-if-not-last {\n  visibility: hidden;\n}\n\n.repeated-chunk .show-if-not-only {\n  visibility: visible;\n}\n\n.repeated-chunk.first.last .show-if-not-only {\n  visibility: hidden;\n}\n\n/* == nested repeatable elements / 2 deep == */\n.repeated-chunk .repeated-chunk .show-if-last {\n  visibility: hidden;\n}\n\n.repeated-chunk .repeated-chunk.last .show-if-last {\n  visibility: visible;\n}\n\n.repeated-chunk .repeated-chunk .show-if-not-last {\n  visibility: visible;\n}\n\n.repeated-chunk .repeated-chunk.last .show-if-not-last {\n  visibility: hidden;\n}\n\n.repeated-chunk .repeated-chunk .show-if-not-only {\n  visibility: visible;\n}\n\n.repeated-chunk .repeated-chunk.first.last .show-if-not-only {\n  visibility: hidden;\n}\n\n/* == nested repeatable elements / 3 deep == */\n.repeated-chunk .repeated-chunk .repeated-chunk .show-if-last {\n  visibility: hidden;\n}\n\n.repeated-chunk .repeated-chunk .repeated-chunk.last .show-if-last {\n  visibility: visible;\n}\n\n.repeated-chunk .repeated-chunk .repeated-chunk .show-if-not-last {\n  visibility: visible;\n}\n\n.repeated-chunk .repeated-chunk .repeated-chunk.last .show-if-not-last {\n  visibility: hidden;\n}\n\n.repeated-chunk .repeated-chunk .repeated-chunk .show-if-not-only {\n  visibility: visible;\n}\n\n.repeated-chunk .repeated-chunk .repeated-chunk.first.last .show-if-not-only {\n  visibility: hidden;\n}\n\n/*\n    <DIV>s marked with to-be-removed is used in conjunction with repeatable.jelly and hetero-list.jelly\n    and represents a master copy that gets pulled out from HTML, then inserted later upon demand multiple times\n    when the user does \"Add\".\n*/\ndiv.to-be-removed {\n  display: none;\n}\n\n/* ========================= D&D support in heterogenous/repeatable lists = */\n\n.hetero-list-container.with-drag-drop .repeated-chunk,\n.repeated-container.with-drag-drop .repeated-chunk {\n  margin-bottom: 1rem;\n}\n\n// SortableJS drag & drop classes\n.repeated-chunk--sortable-ghost {\n  height: 100px;\n  width: 100%;\n  overflow: hidden;\n}\n\n.repeated-chunk--sortable-chosen {\n  width: 100%;\n  height: 100px;\n  background-color: transparent;\n  border: 2px solid var(--accent-color);\n\n  & > * {\n    display: none;\n  }\n}\n\n.repeated-chunk {\n  & > div > *:last-of-type {\n    margin-bottom: 0;\n  }\n\n  &__header {\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n    font-weight: bold;\n    margin-top: -0.4rem;\n    margin-bottom: 0.75rem;\n\n    .dd-handle {\n      position: relative;\n      width: 30px;\n      height: 30px;\n      overflow: hidden;\n      margin-right: 0.75rem;\n      cursor: move;\n      margin-left: -6px;\n\n      &::before {\n        content: \"\";\n        position: absolute;\n        inset: 0;\n        background: var(--text-color);\n        border-radius: var(--form-input-border-radius);\n        opacity: 0;\n        transition: var(--standard-transition);\n      }\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        inset: 0 4px;\n        background-color: var(--text-color);\n        mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'%3E%3Ctitle%3EReorder Three%3C/title%3E%3Cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='32' d='M96 256h320M96 176h320M96 336h320'/%3E%3C/svg%3E\");\n        mask-position: center;\n        mask-repeat: no-repeat;\n        mask-size: contain;\n      }\n\n      &:hover {\n        &::before {\n          opacity: 0.1;\n        }\n      }\n    }\n  }\n\n  // TODO: Update/remove when .jenkins-button PR is merged\n  .repeatable-delete {\n    position: absolute;\n    top: 0.6rem;\n    right: 0.6rem;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 28px;\n    height: 28px;\n    border: none;\n    outline: none;\n    margin-left: auto;\n    color: var(--red);\n    z-index: 0;\n    background: transparent;\n    cursor: pointer;\n\n    &::before {\n      content: \"\";\n      position: absolute;\n      inset: 0;\n      background: currentColor;\n      border-radius: 100px;\n      z-index: -1;\n      opacity: 0.075;\n      transition: var(--standard-transition);\n    }\n\n    &::after {\n      content: \"\";\n      position: absolute;\n      inset: 0;\n      box-shadow: 0 0 0 10px transparent;\n      border-radius: 100px;\n      z-index: -1;\n      opacity: 0.075;\n      transition: var(--standard-transition);\n    }\n\n    svg {\n      width: 18px;\n      height: 18px;\n    }\n\n    &:hover {\n      &::before {\n        opacity: 0.1;\n      }\n    }\n\n    &:active,\n    &:focus {\n      &::before {\n        opacity: 0.15;\n      }\n\n      &::after {\n        box-shadow: 0 0 0 5px var(--red);\n      }\n    }\n  }\n}\n", "@use \"../base/breakpoints\";\n\n.jenkins-search-container {\n  position: relative;\n}\n\n.jenkins-search {\n  --search-bar-height: 2.375rem;\n\n  position: relative;\n\n  &__input {\n    padding: 0 0.25rem 0 calc(var(--search-bar-height) * 0.9);\n    height: var(--search-bar-height);\n\n    &::placeholder {\n      color: var(--text-color-secondary);\n    }\n\n    // <PERSON><PERSON> adds unwanted padding - let's remove it\n    &::-webkit-search-decoration {\n      -webkit-appearance: none;\n    }\n\n    &::-webkit-search-cancel-button {\n      -webkit-appearance: none;\n      height: 45%;\n      min-height: 1.1rem;\n      aspect-ratio: 1;\n      margin-right: 0.2rem;\n      background: var(--text-color-secondary);\n      mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/%3E%3C/svg%3E\");\n      mask-size: contain;\n      mask-repeat: no-repeat;\n      opacity: 0;\n      pointer-events: none;\n      transform: scale(0.8);\n      transition: var(--standard-transition);\n      cursor: pointer;\n\n      &:hover {\n        opacity: 0.85 !important;\n      }\n\n      &:active {\n        opacity: 0.7 !important;\n      }\n    }\n\n    &:not(:disabled) {\n      &:active,\n      &:focus {\n        &::-webkit-search-cancel-button {\n          opacity: 0.5;\n          pointer-events: all;\n          transform: scale(1);\n        }\n      }\n    }\n\n    &:active,\n    &:focus {\n      &::-webkit-search-cancel-button {\n        opacity: 1;\n        pointer-events: all;\n        transform: scale(1);\n      }\n    }\n  }\n\n  &__icon {\n    position: absolute;\n    aspect-ratio: 1 / 1;\n    top: 0;\n    left: 0;\n    bottom: 0;\n    display: grid;\n    pointer-events: none;\n\n    svg {\n      width: 1rem;\n      height: 1rem;\n      max-width: 1rem;\n      max-height: 1rem;\n      grid-column-start: 1;\n      grid-row-start: 1;\n      place-self: center center;\n      transition: var(--standard-transition);\n    }\n\n    &::before,\n    &::after {\n      content: \"\";\n      width: 45%;\n      height: 45%;\n      max-width: 1.1rem;\n      max-height: 1.1rem;\n      border: 0.125rem solid var(--text-color-secondary);\n      border-radius: 100%;\n      transition: var(--standard-transition);\n      grid-column-start: 1;\n      grid-row-start: 1;\n      place-self: center center;\n      opacity: 0;\n      scale: 0;\n      filter: blur(2.5px);\n    }\n\n    &::after {\n      border-color: currentColor;\n      clip-path: inset(0 0 50% 50%);\n      animation: loading-spinner 1s infinite linear;\n\n      @media (prefers-reduced-motion) {\n        animation-duration: 2s;\n      }\n    }\n  }\n\n  .jenkins-keyboard-shortcut {\n    position: absolute;\n    top: calc(50% - 0.6875rem);\n    right: 0.5rem;\n    min-width: 1.375rem;\n    min-height: 1.375rem;\n    padding: 0;\n    color: var(--text-color-secondary);\n  }\n\n  &--loading {\n    .jenkins-search__icon {\n      svg {\n        opacity: 0;\n        scale: 0;\n        filter: blur(5px);\n      }\n\n      &::before {\n        opacity: 0.5;\n        scale: 1;\n        filter: blur(0);\n      }\n\n      &::after {\n        opacity: 1;\n        scale: 1;\n        filter: blur(0);\n      }\n    }\n  }\n\n  &--app-bar {\n    --search-bar-height: 3rem;\n\n    width: 100%;\n    margin-block: -5px;\n\n    @media (min-width: breakpoints.$tablet-breakpoint) {\n      max-width: 50vw;\n    }\n\n    .jenkins-keyboard-shortcut {\n      right: 0.8125rem;\n    }\n\n    &::before {\n      content: \"\";\n      position: absolute;\n      inset: 0;\n      border-radius: var(--form-input-border-radius);\n      z-index: -1;\n      backdrop-filter: blur(20px);\n      box-shadow: 0 0 var(--section-padding) var(--background);\n\n      @supports not (backdrop-filter: blur(20px)) {\n        background: var(--background);\n      }\n    }\n  }\n\n  &:focus-within {\n    .jenkins-search__icon {\n      fill: var(--focus-input-border);\n    }\n\n    .jenkins-keyboard-shortcut {\n      opacity: 0;\n      transform: scale(0.9);\n      pointer-events: none;\n    }\n  }\n\n  &--disabled {\n    color: var(--text-color-secondary);\n    opacity: 0.5;\n\n    .jenkins-keyboard-shortcut {\n      display: none;\n    }\n  }\n}\n\n.jenkins-search__results-container {\n  position: absolute;\n  width: 100%;\n  border-radius: 1rem;\n  box-shadow: var(--dropdown-box-shadow);\n  overflow: hidden;\n  z-index: 10;\n  height: 1px; // Setting to 0 caused the items not to render initially in Chrome\n  opacity: 0;\n  transition: var(--standard-transition);\n  background: color-mix(in sRGB, var(--card-background) 85%, transparent);\n  backdrop-filter: var(--dropdown-backdrop-filter);\n  visibility: collapse;\n  scale: 95%;\n  translate: 0 -0.3125rem;\n  will-change: height, scale, opacity;\n\n  &--visible {\n    opacity: 1;\n    scale: 100%;\n    visibility: visible;\n    translate: 0 0.3125rem;\n  }\n}\n\n.jenkins-search__results__no-results-label {\n  text-align: center;\n  margin: 2rem;\n  padding: 0;\n  color: var(--text-color-secondary);\n  font-weight: var(--font-bold-weight);\n}\n", ".jenkins-select {\n  position: relative;\n  width: 100%;\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    right: 13px;\n    bottom: 0;\n    width: 0.625rem;\n    background-color: currentColor;\n    mask-image: url(\"data:image/svg+xml;charset=UTF-8,%3c?xml version='1.0' encoding='UTF-8'?%3e%3csvg width='336px' height='192px' viewBox='0 0 336 192' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3ePath%3c/title%3e%3cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3e%3cg id='arrow' transform='translate(0.000000, 0.000000)' fill='%23FF0000' fill-rule='nonzero'%3e%3cpath d='M7.02943725,7.02943725 C16.3053957,-2.24652118 31.2852799,-2.34214962 40.6788451,6.74255194 L40.9705627,7.02943725 L168,134.059 L295.029437,7.02943725 C304.305396,-2.24652118 319.28528,-2.34214962 328.678845,6.74255194 L328.970563,7.02943725 C338.246521,16.3053957 338.34215,31.2852799 329.257448,40.6788451 L328.970563,40.9705627 L184.970563,184.970563 C175.694604,194.246521 160.71472,194.34215 151.321155,185.257448 L151.029437,184.970563 L7.02943725,40.9705627 C-2.34314575,31.5979797 -2.34314575,16.4020203 7.02943725,7.02943725 Z' id='Path'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/svg%3e\");\n    mask-size: contain;\n    mask-repeat: no-repeat;\n    mask-position: center;\n    pointer-events: none;\n    transition: translate var(--elastic-transition);\n  }\n\n  &__input {\n    appearance: none;\n    display: block;\n    border: var(--jenkins-border-width) solid var(--input-border);\n    padding: var(--form-input-padding);\n    width: 100% !important; // TODO remove important after https://github.com/jenkinsci/credentials-plugin/pull/255\n    max-width: 100% !important; // TODO remove important after https://github.com/jenkinsci/credentials-plugin/pull/255\n    border-radius: var(--form-input-border-radius);\n    box-shadow: var(--form-input-glow);\n    transition: var(--standard-transition);\n    min-height: 38px;\n    cursor: pointer;\n\n    &:hover {\n      border-color: var(--input-border-hover);\n    }\n\n    &:active,\n    &:focus {\n      outline: none;\n      border-color: var(--focus-input-border);\n      box-shadow: var(--form-input-glow--focus);\n    }\n\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n\n  &:hover {\n    &::after {\n      translate: 0 1px;\n    }\n  }\n}\n\n.jenkins-multi-select {\n  position: relative;\n  width: 100%;\n  border: var(--jenkins-border-width) solid var(--input-border);\n  border-radius: var(--form-input-border-radius);\n  box-shadow: var(--form-input-glow);\n  transition: var(--standard-transition);\n  outline: none;\n\n  &:focus {\n    border-color: var(--focus-input-border);\n    box-shadow: var(--form-input-glow--focus);\n  }\n\n  &:disabled {\n    pointer-events: none;\n  }\n}\n", ".textarea-preview-container {\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 0.5rem;\n  color: var(--text-color-secondary);\n}\n\n.textarea-preview {\n  background-color: var(--button-background);\n  border: var(--jenkins-border--subtle);\n  padding: var(--form-input-padding);\n  margin-top: 0.5rem;\n  border-radius: var(--form-input-border-radius);\n}\n", ".jenkins-toggle-switch {\n  position: relative;\n  display: inline-block;\n\n  &--invert-label {\n    input {\n      &:checked {\n        & + label {\n          &::after {\n            left: unset !important;\n            right: 5px !important;\n          }\n        }\n      }\n    }\n\n    label {\n      flex-direction: row-reverse;\n\n      &::before {\n        margin-left: 1rem;\n        margin-right: 0 !important;\n      }\n\n      &::after {\n        left: unset !important;\n        right: 25px;\n      }\n\n      &:active,\n      &:focus {\n        &::after {\n          right: 20px !important;\n        }\n      }\n\n      .jenkins-toggle-switch__label__checked-title,\n      .jenkins-toggle-switch__label__unchecked-title {\n        left: unset !important;\n        right: calc(50px + 1rem) !important;\n      }\n    }\n  }\n}\n\n.jenkins-toggle-switch input {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n\n  // If margin is set to a negative value it can cause text to be announced in\n  // the wrong order in VoiceOver for OSX\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  clip-path: inset(50%);\n\n  &:checked + label::before {\n    background-color: var(--focus-input-border);\n  }\n\n  &:checked {\n    & + label {\n      &::after {\n        left: 25px;\n        mask-image: url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='66px' height='66px' viewBox='0 0 66 66' version='1.1' xmlns='http://www.w3.org/2000/svg'%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpath d='M66,0 L66,66 L0,66 L0,0 L66,0 Z M37.7154094,29.2061836 C37.3690459,28.9031155 36.8425777,28.9382134 36.5395097,29.2845769 L36.5395097,29.2845769 L31.2924962,35.2799905 L29.4225874,33.410737 L29.3440813,33.3414133 C29.0171724,33.0872262 28.5444804,33.1103341 28.2440774,33.410737 C27.9186409,33.7361736 27.9186409,34.2638104 28.2440774,34.589247 L28.2440774,34.589247 L30.7440745,37.0892441 L30.8202748,37.15679 C31.1634387,37.4256962 31.6657159,37.3856111 31.9604761,37.0487424 L31.9604761,37.0487424 L37.7938027,30.3820833 L37.8577508,30.2991398 C38.0896293,29.9560466 38.0351295,29.4859387 37.7154094,29.2061836 Z' fill='%23000000'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");\n      }\n\n      &:active,\n      &:focus {\n        &::before {\n          box-shadow:\n            inset 0 0 0 1.5px\n              color-mix(in sRGB, var(--text-color-secondary) 9%, transparent),\n            var(--form-input-glow--focus);\n        }\n\n        &::after {\n          left: 20px;\n          width: 25px;\n        }\n      }\n\n      .jenkins-toggle-switch__label__checked-title {\n        opacity: 1;\n      }\n\n      .jenkins-toggle-switch__label__unchecked-title {\n        opacity: 0;\n      }\n    }\n  }\n\n  &:disabled {\n    & + label {\n      cursor: not-allowed;\n    }\n  }\n}\n\n.jenkins-toggle-switch label {\n  position: relative;\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n  margin: 0;\n  cursor: pointer;\n  line-height: 30px;\n  font-weight: var(--form-label-font-weight);\n  user-select: none;\n\n  &::before {\n    display: inline-block;\n    content: \"\";\n    position: relative;\n    min-width: 50px;\n    min-height: 30px;\n    background: var(--input-border);\n    border-radius: 19px;\n    transition: var(--standard-transition);\n    margin-right: 1rem;\n    box-shadow:\n      inset 0 0 0 1.5px\n        color-mix(in sRGB, var(--text-color-secondary) 9%, transparent),\n      var(--form-input-glow);\n  }\n\n  &::after {\n    content: \"\";\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: absolute;\n    top: 5px;\n    left: 5px;\n    width: 20px;\n    height: 20px;\n    background: var(--background);\n    mask-size: 60px 60px;\n    mask-position: center;\n    border-radius: 100px;\n    transition: var(--standard-transition);\n    box-shadow: 0 0 0 1.5px\n      color-mix(in sRGB, var(--text-color-secondary) 9%, transparent);\n  }\n\n  &:hover::before {\n    background-color: var(--input-border-hover);\n  }\n\n  &:active,\n  &:focus {\n    &::before {\n      box-shadow:\n        inset 0 0 0 1.5px\n          color-mix(in sRGB, var(--text-color-secondary) 9%, transparent),\n        0 0 0 4px\n          color-mix(in sRGB, var(--text-color-secondary) 25%, transparent);\n    }\n\n    &::after {\n      left: 5px;\n      width: 25px;\n    }\n  }\n\n  .jenkins-toggle-switch__label__checked-title {\n    opacity: 0;\n  }\n\n  .jenkins-toggle-switch__label__checked-title,\n  .jenkins-toggle-switch__label__unchecked-title {\n    position: absolute;\n    left: calc(50px + 1rem);\n    transition: opacity 0.4s;\n    user-select: none;\n  }\n}\n", ".validation-error-area {\n  transition: var(--standard-transition);\n  opacity: 0;\n  height: 0;\n  overflow: hidden;\n}\n\n.validation-error-area--visible {\n  margin-top: 0.75rem;\n  opacity: 1;\n\n  & > * {\n    animation: animate-validation-error-area var(--standard-transition);\n  }\n}\n\n@keyframes animate-validation-error-area {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n.error,\n.warning,\n.info {\n  position: relative;\n  padding-left: calc(22px + 0.4rem);\n  font-weight: var(--font-bold-weight);\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    left: 0;\n    bottom: 0;\n    width: 22px;\n    height: 22px;\n    background-color: currentColor;\n    mask-position: top center;\n    mask-repeat: no-repeat;\n    mask-size: contain;\n  }\n}\n\n.ok {\n  color: var(--text-color-secondary);\n}\n\n.error {\n  color: var(--red);\n\n  &::before {\n    mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='512' height='512' viewBox='0 0 512 512'%3E%3Ctitle%3Eionicons-v5-a%3C/title%3E%3Cpath d='M256,48C141.31,48,48,141.31,48,256s93.31,208,208,208,208-93.31,208-208S370.69,48,256,48Zm0,319.91a20,20,0,1,1,20-20A20,20,0,0,1,256,367.91Zm21.72-201.15-5.74,122a16,16,0,0,1-32,0l-5.74-121.94v-.05a21.74,21.74,0,1,1,43.44,0Z'/%3E%3C/svg%3E\");\n  }\n}\n\n.error-inline {\n  color: var(--red);\n  font-weight: var(--font-bold-weight);\n}\n\n.warning {\n  color: var(--orange);\n\n  &::before {\n    mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='512' height='512' viewBox='0 0 512 512'%3E%3Ctitle%3Eionicons-v5-r%3C/title%3E%3Cpath d='M449.07,399.08,278.64,82.58c-12.08-22.44-44.26-22.44-56.35,0L51.87,399.08A32,32,0,0,0,80,446.25H420.89A32,32,0,0,0,449.07,399.08Zm-198.6-1.83a20,20,0,1,1,20-20A20,20,0,0,1,250.47,397.25ZM272.19,196.1l-5.74,122a16,16,0,0,1-32,0l-5.74-121.95v0a21.73,21.73,0,0,1,21.5-22.69h.21a21.74,21.74,0,0,1,21.73,22.7Z'/%3E%3C/svg%3E\");\n  }\n}\n\n.warning-inline {\n  color: var(--orange);\n  font-weight: var(--font-bold-weight);\n}\n\n.info {\n  color: var(--text-color);\n\n  &::before {\n    mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'%3E%3Ctitle%3EArrow Forward%3C/title%3E%3Cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='32' d='M268 112l144 144-144 144M392 256H100'/%3E%3C/svg%3E\");\n  }\n}\n", ".app-about-branding {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: calc(var(--section-padding) * 2);\n  pointer-events: none;\n  margin-bottom: var(--section-padding);\n  overflow: hidden;\n  border-radius: 1rem;\n\n  &::before,\n  &::after {\n    content: \"\";\n    position: absolute;\n    z-index: 1;\n  }\n\n  &::before {\n    width: 120%;\n    aspect-ratio: 1;\n    background: repeating-conic-gradient(\n      var(--background) 0deg,\n      rgb(100 100 100 / 0.25) 20deg\n    );\n    animation: app-about-starburst 100s linear infinite;\n    opacity: 0.25;\n  }\n\n  &::after {\n    inset: 0;\n    border: var(--jenkins-border--subtle);\n    border-radius: inherit;\n  }\n\n  img {\n    height: 7.5rem;\n    z-index: 1;\n  }\n}\n\n.app-about-branding__aurora {\n  position: absolute;\n  width: 120%;\n  aspect-ratio: 1;\n\n  &::before,\n  &::after {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    z-index: -1;\n    border-radius: 100%;\n  }\n\n  &::before {\n    background-color: var(--color);\n    background-image:\n      radial-gradient(at 40% 20%, hsl(28 100% 74% / 1) 0, transparent 50%),\n      radial-gradient(at 80% 0%, hsl(189 100% 56% / 1) 0, transparent 50%),\n      radial-gradient(at 0% 50%, hsl(355 85% 93% / 1) 0, transparent 50%),\n      radial-gradient(at 80% 50%, hsl(359 70% 46%) 0, transparent 50%),\n      radial-gradient(at 0% 100%, hsl(22 100% 77% / 1) 0, transparent 50%),\n      radial-gradient(at 80% 100%, hsl(242 100% 70% / 1) 0, transparent 50%),\n      radial-gradient(at 0% 0%, hsl(343 100% 76% / 1) 0, transparent 50%);\n    opacity: 0.4;\n    animation: app-about-aurora-one 7s linear infinite;\n  }\n\n  &::after {\n    background-image:\n      radial-gradient(at 40% 20%, hsl(212 100% 74% / 1) 0, transparent 50%),\n      radial-gradient(at 80% 0%, hsl(13 100% 56% / 1) 0, transparent 50%),\n      radial-gradient(at 0% 50%, hsl(179 85% 93% / 1) 0, transparent 50%),\n      radial-gradient(at 80% 50%, hsl(164 100% 76% / 1) 0, transparent 50%),\n      radial-gradient(at 0% 100%, hsl(206 100% 77% / 1) 0, transparent 50%),\n      radial-gradient(at 80% 100%, hsl(66 100% 70% / 1) 0, transparent 50%),\n      radial-gradient(at 0% 0%, hsl(167 100% 76% / 1) 0, transparent 50%);\n    opacity: 0.2;\n    animation: app-about-aurora-two 14s linear infinite;\n  }\n}\n\n@keyframes app-about-aurora-one {\n  0% {\n    opacity: 0.4;\n  }\n\n  50% {\n    opacity: 0.2;\n    transform: rotate(-180deg);\n  }\n\n  100% {\n    opacity: 0.4;\n    transform: rotate(-360deg);\n  }\n}\n\n@keyframes app-about-aurora-two {\n  0% {\n    opacity: 0.2;\n  }\n\n  50% {\n    opacity: 0.55;\n    transform: rotate(180deg);\n  }\n\n  100% {\n    opacity: 0.2;\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes app-about-starburst {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.app-about-heading {\n  font-weight: 600;\n  font-family: Georgia, serif;\n  font-size: 1.5rem;\n  margin-bottom: 0.5rem !important;\n}\n\n.app-about-version {\n  font-weight: var(--font-bold-weight);\n  color: var(--text-color-secondary);\n  margin: 0;\n}\n\n.app-about-paragraph {\n  font-size: 1rem;\n  margin-bottom: var(--section-padding);\n  font-weight: var(--font-bold-weight);\n}\n", "@use \"../base/breakpoints\";\n\n.build-caption-progress-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-wrap: nowrap;\n  gap: 10px;\n}\n\n.app-build__grid {\n  gap: calc(var(--section-padding) / 2);\n  margin-bottom: var(--section-padding);\n\n  .jenkins-card {\n    margin-bottom: 0; // Remove when .jenkins-card no longer has a default margin\n  }\n\n  @media (width < 1000px) {\n    display: flex;\n    flex-direction: column;\n    grid-template-columns: 1fr 1fr;\n\n    .jenkins-card__content {\n      max-height: 300px;\n    }\n  }\n\n  @media (width >= 1000px) {\n    display: grid;\n    justify-content: stretch;\n    grid-template-columns: 1fr 1fr;\n    margin-top: calc(var(--section-padding) * -0.25);\n\n    .jenkins-card {\n      overflow: clip;\n      margin-bottom: 0; // Remove when .jenkins-card no longer has a default margin\n\n      &:first-of-type {\n        grid-column: span 2;\n      }\n\n      &:last-of-type {\n        grid-column: span 2;\n      }\n    }\n\n    .jenkins-card__content {\n      height: 300px;\n    }\n  }\n\n  @media (width >= 1300px) {\n    grid-template-columns: 1fr 1fr 1fr;\n\n    .jenkins-card {\n      &:first-of-type {\n        grid-column: span 2;\n      }\n\n      &:last-of-type {\n        grid-column: span 3;\n      }\n    }\n  }\n\n  @media (width >= 1600px) {\n    grid-template-columns: 1fr 1fr 1fr 1fr;\n\n    .jenkins-card {\n      &:first-of-type {\n        grid-column: span 3;\n      }\n\n      &:last-of-type {\n        grid-column: span 4;\n      }\n    }\n  }\n\n  @media (width >= 1900px) {\n    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;\n\n    .jenkins-card {\n      &:first-of-type {\n        grid-column: span 3;\n      }\n\n      &:last-of-type {\n        grid-column: span 5;\n      }\n    }\n  }\n}\n\n.app-build__clock {\n  position: relative;\n  width: 1.5rem;\n  height: 1.5rem;\n  flex-shrink: 0;\n  scale: 0.75;\n\n  &__hours,\n  &__minutes {\n    position: absolute;\n    inset: 0;\n    border-radius: 100%;\n\n    &::after {\n      content: \"\";\n      position: absolute;\n      bottom: 11px;\n      left: 11px;\n      width: 2px;\n      background: currentColor;\n      border-radius: 2px;\n    }\n  }\n\n  &__hours {\n    rotate: var(--hours);\n\n    &::after {\n      height: 6px;\n    }\n  }\n\n  &__minutes {\n    rotate: var(--minutes);\n\n    &::after {\n      height: 8px;\n    }\n  }\n\n  &::after {\n    position: absolute;\n    content: \"\";\n    inset: 0;\n    border-radius: 100%;\n    border: 2px solid currentColor;\n  }\n}\n\n.app-console-output-widget {\n  overflow-y: auto;\n  margin: 0 -1rem -1rem;\n  padding: 0 1rem 1rem;\n\n  pre {\n    background: transparent;\n    border: none;\n    margin: 0;\n    padding: 0;\n    line-height: 1.75;\n    font-size: var(--font-size-sm);\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n$min-button-size: 2.375rem;\n\n.jenkins-icon-size {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin: var(--section-padding) 0;\n\n  &__items {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n\n    ol {\n      position: relative;\n      display: flex;\n      align-items: center;\n      margin: 0;\n      padding: 0;\n      list-style-type: none;\n      gap: 0.25rem;\n\n      &::before {\n        content: \"\";\n        position: absolute;\n        inset: 0;\n        border-radius: 0.8rem;\n        background: var(--button-background);\n        opacity: 0;\n        transition: var(--standard-transition);\n      }\n\n      &:hover {\n        &::before {\n          inset: -0.2rem;\n          opacity: 1;\n        }\n      }\n    }\n\n    li {\n      .jenkins-button {\n        margin: 0 !important;\n        padding: 0 !important;\n        min-width: 0 !important;\n        aspect-ratio: 1;\n        height: $min-button-size;\n      }\n    }\n\n    .jenkins-icon-size__items-item {\n      position: relative;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      aspect-ratio: 1;\n      height: $min-button-size;\n      border-radius: var(--form-input-border-radius);\n      font-weight: var(--btn-link-font-weight);\n      font-size: var(--btn-font-size);\n      background: var(--button-background--hover);\n      cursor: default;\n    }\n  }\n}\n\n.jenkins-jobs-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n\n  &__item {\n    @include mixins.item;\n\n    display: grid;\n    grid-template-columns: 1fr auto;\n    gap: 1rem;\n    padding: calc(1rem - var(--card-border-width, 0));\n    align-items: center;\n    border-radius: 1rem;\n    background: var(--card-background);\n    border: var(--card-border-width) solid var(--card-border-color);\n    transition: var(--standard-transition);\n\n    &::before {\n      content: none;\n    }\n\n    &__icons {\n      pointer-events: none;\n      color: var(--text-color);\n    }\n\n    &__details {\n      display: grid;\n      grid-template-columns: auto 1fr;\n      gap: 1rem;\n      text-decoration: none !important;\n      overflow: hidden;\n    }\n\n    &__details__text {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n      overflow: hidden;\n    }\n\n    &__actions {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n      margin-right: 0.5rem;\n\n      &:empty {\n        display: none;\n      }\n    }\n\n    &__label {\n      color: var(--link-color);\n      font-weight: var(--font-bold-weight);\n      margin: 0;\n      font-size: 1rem;\n    }\n\n    &__description {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n      color: var(--text-color-secondary);\n      margin: 0;\n      font-size: 1rem;\n      white-space: nowrap;\n      mask-image: linear-gradient(90deg, black calc(100% - 1rem), transparent);\n\n      svg {\n        width: 1rem;\n        height: 1rem;\n      }\n\n      &:empty {\n        display: none;\n      }\n    }\n\n    &__compact-column {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 0.5rem;\n    }\n\n    &:hover {\n      --link-color: var(--link-color--hover);\n\n      background: var(--card-background--hover);\n      border-color: var(--card-border-color--hover);\n    }\n\n    &:active,\n    &:focus {\n      --link-color: var(--link-color--active);\n\n      background: var(--card-background--active);\n      border-color: var(--card-border-color--active);\n    }\n  }\n}\n\n.build-status-link {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n\n  svg {\n    width: 1rem;\n    height: 1rem;\n  }\n}\n\ndiv.listview-jobs {\n  display: flex;\n  flex-direction: column;\n  gap: 0.3rem;\n\n  &--nested {\n    display: flex;\n    flex-direction: column;\n    border-left: 2px solid color-mix(in sRGB, var(--input-border), transparent);\n    margin-left: 10px;\n    padding-left: 22px;\n  }\n}\n", ".app-icon-legend {\n  display: grid;\n  grid-template-columns: auto 1fr;\n  gap: 1rem;\n  align-items: center;\n  margin: 0;\n  padding: 0;\n\n  &:not(:last-of-type) {\n    margin-bottom: var(--section-padding);\n  }\n\n  dt {\n    display: flex;\n    place-items: center center;\n\n    svg {\n      width: 2rem;\n      height: 2rem;\n    }\n  }\n\n  dd {\n    margin: 0;\n    padding: 0;\n    font-size: 0.9375rem;\n    font-weight: var(--font-bold-weight);\n    line-height: 1.6;\n  }\n}\n", "@use \"../abstracts/mixins\";\n\n#buildHistoryPage {\n  margin: 10px 0 10px 10px;\n\n  .jenkins-search {\n    margin-inline: -0.25rem;\n    margin-bottom: 5px;\n  }\n}\n\n.app-builds-container {\n  transition: opacity var(--standard-transition);\n\n  &__items {\n    margin-bottom: -0.5rem;\n  }\n\n  &__placeholder {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    text-align: center;\n    padding: 3rem;\n    animation: fade-in-builds-placeholder var(--standard-transition);\n\n    @keyframes fade-in-builds-placeholder {\n      from {\n        opacity: 0;\n      }\n    }\n  }\n\n  &__heading {\n    display: flex;\n    font-size: 0.75rem;\n    color: var(--text-color-secondary);\n    margin-top: 10px;\n    font-weight: var(--font-bold-weight);\n    margin-bottom: 4px;\n  }\n\n  &__controls {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 15px;\n    margin: 0 -0.35rem;\n    margin-top: 1rem;\n    margin-bottom: -0.5rem;\n\n    .jenkins-button {\n      padding: 10px;\n\n      svg {\n        transition: translate var(--standard-transition);\n      }\n\n      &:first-of-type {\n        justify-content: start;\n\n        &:hover {\n          translate: -2px 0;\n\n          svg {\n            translate: -4px 0;\n          }\n        }\n      }\n\n      &:last-of-type {\n        justify-content: end;\n\n        &:hover {\n          translate: 2px 0;\n\n          svg {\n            translate: 4px 0;\n          }\n        }\n      }\n    }\n\n    .app-builds-container__button--disabled {\n      color: var(--text-color-secondary) !important;\n      opacity: 0.25;\n      pointer-events: none;\n    }\n  }\n\n  &--loading {\n    opacity: 0.4;\n    filter: blur(0.5px);\n  }\n}\n\n.app-builds-container__item {\n  @include mixins.item($border: false);\n\n  display: grid;\n  grid-template-columns: auto 1fr auto;\n  gap: 0.5rem 0.65rem;\n  padding: 0 0 0.25rem;\n  margin: 0 -0.5rem;\n  font-size: 0.8125rem !important;\n  min-height: 2rem;\n\n  &__icon {\n    display: inline-flex;\n    justify-content: center;\n    padding: 0 0 0 0.5rem;\n    margin-top: 0.385rem;\n\n    svg {\n      width: 1.25rem;\n      height: 1.25rem;\n    }\n  }\n\n  .app-builds-container__item__inner {\n    display: flex;\n    align-items: stretch;\n    flex-wrap: wrap;\n\n    &__link {\n      display: flex;\n      color: var(--text-color);\n      gap: 0.5rem;\n      text-decoration: none;\n      font-weight: var(--font-bold-weight);\n      flex-grow: 1;\n      padding: 0.45rem 0 0;\n      word-break: normal;\n      overflow-wrap: anywhere;\n\n      .app-builds-container__item__time {\n        color: var(--text-color-secondary);\n        white-space: nowrap;\n      }\n    }\n\n    &__controls {\n      display: flex;\n      align-items: center;\n      justify-content: start;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n      margin-top: 0.3rem;\n    }\n  }\n\n  &--not-interactable {\n    cursor: default;\n\n    &::before,\n    &::after {\n      display: none;\n    }\n\n    .app-builds-container__item__description {\n      margin-bottom: 0;\n    }\n  }\n\n  .jenkins-jumplist-link {\n    margin-top: 0.2rem;\n    padding-right: 0.8rem;\n  }\n\n  &__description {\n    color: var(--text-color-secondary);\n    padding-left: 2.25rem;\n    margin-top: -2px;\n    grid-column: 1 / span 2;\n    word-break: normal;\n    overflow-wrap: anywhere;\n\n    &::before {\n      content: \"\";\n      position: absolute;\n      left: 17px;\n      top: 34px;\n      bottom: 6px;\n      width: 2px;\n      background: var(--text-color-secondary);\n      border-radius: var(--form-input-border-radius);\n      opacity: 0.3;\n    }\n  }\n}\n", ".manage-messages a,\n.manage-messages a:visited {\n  color: inherit !important;\n  text-decoration: none;\n}\n\n.manage-messages a:hover,\n.manage-messages a:focus {\n  text-decoration: underline;\n}\n\n.manage-messages dl:first-child {\n  margin-top: 0;\n}\n\n.manage-messages dl dt:first-child {\n  margin-top: 0;\n}\n\n.manage-messages dl dt {\n  margin-top: 10px;\n  font-weight: normal;\n}\n\n.manage-messages dl dd {\n  margin-left: 15px;\n}\n\n.manage-messages dl:last-child {\n  margin-bottom: 0;\n}\n\n.manage-messages dl dt::after {\n  content: \": \";\n}\n\n.manage-messages .alert,\n.manage-messages .jenkins-alert {\n  &:last-of-type {\n    margin-bottom: 30px;\n  }\n\n  a {\n    text-decoration: underline;\n  }\n\n  form {\n    position: relative;\n    float: right;\n    margin: -6px 0 0 !important;\n    display: flex;\n    gap: 0.5rem;\n\n    & > div {\n      display: contents;\n    }\n\n    span {\n      margin: 0 0 0 4px !important;\n    }\n  }\n}\n", "#plugins {\n  .app-plugin-manager__categories {\n    margin: 0.5rem 0;\n\n    a {\n      margin: 0 1.25rem 0 0 !important;\n      font-size: 0.75rem;\n    }\n  }\n\n  tr.already-upgraded {\n    background-color: var(--plugin-manager-bg-color-already-upgraded);\n  }\n\n  tr.all-dependents-disabled .enable input,\n  tr.all-dependents-disabled .enable button,\n  tr.all-dependents-disabled .enable .jenkins-toggle-switch label {\n    pointer-events: auto !important;\n    opacity: 1 !important;\n    visibility: visible;\n  }\n\n  tr.has-dependents input,\n  tr.has-disabled-dependency .enable input,\n  tr.has-disabled-dependency .enable .jenkins-toggle-switch label,\n  tr.has-dependents button,\n  tr.has-dependents .enable button,\n  tr.has-dependents .enable .jenkins-toggle-switch label {\n    pointer-events: none;\n    opacity: 0.25;\n  }\n\n  tr.has-dependents-but-disabled .enable input,\n  tr.has-dependents-but-disabled .enable button,\n  tr.has-dependents-but-disabled .enable .jenkins-toggle-switch label {\n    pointer-events: auto;\n    opacity: 1;\n    visibility: visible;\n  }\n\n  tr.has-disabled-dependency .enable input,\n  tr.has-disabled-dependency .enable button,\n  tr.has-disabled-dependency .enable .jenkins-toggle-switch label {\n    opacity: 0.4;\n  }\n\n  tr.deleted input,\n  tr.deleted button,\n  tr.deleted .jenkins-toggle-switch label {\n    visibility: hidden !important;\n  }\n\n  .dependent-list,\n  .dependency-list {\n    display: none;\n  }\n\n  .enable-state-info,\n  .uninstall-state-info {\n    padding: 5px 20px;\n    max-width: 70%;\n    border: solid 1px var(--warning-color);\n    border-radius: 3px;\n    text-align: center;\n  }\n\n  .enable-state-info {\n    float: left;\n  }\n\n  .uninstall-state-info {\n    float: right;\n  }\n\n  .plugin-dependency-info .title,\n  .plugin-dependency-info .subtitle {\n    opacity: 0.7;\n  }\n\n  .plugin-dependency-info .title {\n    margin-bottom: 5px;\n    font-size: larger;\n    font-weight: bolder;\n  }\n\n  .plugin-dependency-info span {\n    margin: 5px 5px 0 0;\n    background-color: var(--accent-color);\n    display: inline-block;\n    padding: 0.2em 0.6em 0.3em;\n    font-size: var(--font-size-xs);\n    font-weight: 700;\n    line-height: 1;\n    color: var(--white);\n    text-align: center;\n    white-space: nowrap;\n    vertical-align: baseline;\n    border-radius: 0.25em;\n  }\n}\n\n.app-plugin-manager__sidebar {\n  position: sticky;\n  top: calc(40px + 1.6rem);\n\n  h1 {\n    margin-top: 1rem !important;\n    margin-bottom: 0.1rem !important;\n  }\n}\n", ".create-admin-user {\n  padding: 20px 100px;\n  margin: 8px;\n}\n\n.create-admin-user form > div {\n  margin: 0 !important;\n}\n\n.create-admin-user h1 {\n  font-size: 48px;\n  line-height: 48px;\n  margin-top: 30px;\n  font-weight: 500;\n}\n\n.create-admin-user tr td {\n  padding-bottom: 2px;\n}\n\n.create-admin-user tr td,\n.create-admin-user {\n  line-height: 25px;\n  margin-bottom: 6px;\n}\n", ".configure-instance {\n  padding: 20px 100px;\n  margin: 8px;\n}\n\n.configure-instance form > div {\n  margin: 0 !important;\n}\n\n.configure-instance h1 {\n  font-size: 48px;\n  font-weight: 500;\n}\n\n.configure-instance tr td {\n  padding-bottom: 2px;\n  line-height: 25px;\n  margin-bottom: 6px;\n}\n\n.configure-instance tr.has-error label {\n  color: #c00;\n}\n\n.configure-instance tr.has-error td input[type=\"text\"],\n.configure-instance tr.has-error td input[type=\"password\"],\n.configure-instance tr.has-error td textarea {\n  border: 1px solid;\n  border-color: var(--danger-color, #c4000a);\n}\n\n.configure-instance tr td .help-text {\n  color: #999;\n  font-size: 12px;\n}\n\n.configure-instance tr td .error-panel {\n  display: none;\n}\n\n.configure-instance tr.has-error td .error-panel {\n  display: block;\n}\n"], "names": [], "sourceRoot": ""}