<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='commons-text-api' version='1.13.1-176.v74d88f22034b_'><l:dependency name='commons-text API Plugin' groupId='io.jenkins.plugins' artifactId='commons-text-api' version='1.13.1-176.v74d88f22034b_' url='https://github.com/jenkinsci/commons-text-api-plugin'><l:description>Jenkins Api Plugin to provide org.apache.commons:commons-text:1.13.1.</l:description><l:license name='Apache-2.0' url='https://opensource.org/licenses/Apache-2.0'/></l:dependency><l:dependency name='Apache Commons Text' groupId='org.apache.commons' artifactId='commons-text' version='1.13.1' url='https://commons.apache.org/proper/commons-text'><l:description>Apache Commons Text is a set of utility functions and reusable components for the purpose of processing
    and manipulating text that should be of use in a Java environment.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>