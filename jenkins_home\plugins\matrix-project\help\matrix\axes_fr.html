﻿<div>
  Si votre matrice de configuration a besoin d'axes supplémentaires,
  vous pouvez les spécifiez ici.
  <p>
  Par exemple, supposons que vous voulez lancer un test pour une
  application de base de données et que vous devez la tester avec trois
  bases&nbsp;: MySQL, PostgreSQL et Oracle.
  Votre script de build est conçu de façon à spécifier la base à tester
  ainsi&nbsp;:
  <tt>ant -Ddatabase=mysql</tt>.
  <p>
  Un axe, c'est ça. Vous pouvez avoir une variable "database",
  qui prend trois valeurs possibles. Quand vous configurez l'axe, Jenkins
  lancera 3 builds, avec les 3 valeurs possibles assignées à la variable
  "database" afin de couvrir toutes combinaisons de configurations
  possibles (la matrice des configurations).
  <p>
  Les variables spécifiées ici sont disponibles pour le build comme
  variables d'environnement.
  De plus, pour Ant et Maven, les variables sont également exposées en
  tant que propriétés, comme si elles étaient passées avec
  <tt>-D<i>nomVariable</i>=<i>valeur</i></tt> en ligne de commande.
  <p>
  Quand plusieurs axes sont spécifiés, toutes les combinaisons d'axe
  possibles sont construites de façon exhaustive. Les valeurs multiples
  pour les labels et les JDKs sont traitées de la même façon. Par exemple,
  si vous spécifiez jdk=[JDK5,JDK6], database=[mysql,postgresql,oracle],
  container=[jetty,tomcat], alors chaque build exhaustif consistera en
  2x3x2=12 sous-builds différents.
</div>