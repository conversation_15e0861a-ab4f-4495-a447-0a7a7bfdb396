import{r as d,j as o}from"./PipelineGraphModel-BDP0LgOb.js";import{a as j,i as x,f as w}from"./PipelineGraph-DjLGI_5P.js";function v({items:e,tooltip:n="More actions",disabled:c,className:h,icon:p}){const[a,i]=d.useState(!1),u=()=>i(!0),l=()=>i(!1);return o.jsx(j,{content:n,children:o.jsx(x,{visible:a,onClickOutside:l,...k,content:o.jsx("div",{className:"jenkins-dropdown",children:e.map((r,t)=>{if(r==="separator")return o.jsx("div",{className:"jenkins-dropdown__separator"},`separator-${t}`);if(d.isValidElement(r))return o.jsx("div",{className:"jenkins-dropdown__custom-item",children:r},t);const s=r;return o.jsxs("a",{className:"jenkins-dropdown__item",href:s.href,target:s.target,download:s.download,children:[o.jsx("div",{className:"jenkins-dropdown__item__icon",children:s.icon}),s.text]},t)})}),children:o.jsxs("button",{className:"jenkins-button "+h,type:"button",disabled:c,onClick:a?l:u,children:[o.jsx("span",{className:"jenkins-visually-hidden",children:n}),p||o.jsxs("div",{className:"jenkins-overflow-button__ellipsis",children:[o.jsx("span",{}),o.jsx("span",{}),o.jsx("span",{})]})]})})})}const k={theme:"dropdown",duration:250,touch:!0,animation:"dropdown",interactive:!0,offset:[0,0],placement:"bottom-start",arrow:!1};function g({children:e,container:n}){return n?w.createPortal(e,n):(console.error("DropdownPortal: Target container not found!"),null)}const _=o.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:[o.jsx("path",{d:"M416 221.25V416a48 48 0 01-48 48H144a48 48 0 01-48-48V96a48 48 0 0148-48h98.75a32 32 0 0122.62 9.37l141.26 141.26a32 32 0 019.37 22.62z",fill:"none",stroke:"currentColor",strokeLinejoin:"round",strokeWidth:"32"}),o.jsx("path",{d:"M256 56v120a32 32 0 0032 32h120M176 288h160M176 368h160",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32"})]}),N=o.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:[o.jsx("rect",{x:"32",y:"48",width:"448",height:"416",rx:"48",ry:"48",fill:"none",stroke:"currentColor",strokeLinejoin:"round",strokeWidth:"32"}),o.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M96 112l80 64-80 64M192 240h64"})]}),C=o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512","aria-hidden":!0,children:o.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M262.29 192.31a64 64 0 1057.4 57.4 64.13 64.13 0 00-57.4-57.4zM416.39 256a154.34 154.34 0 01-1.53 20.79l45.21 35.46a10.81 10.81 0 012.45 13.75l-42.77 74a10.81 10.81 0 01-13.14 4.59l-44.9-18.08a16.11 16.11 0 00-15.17 1.75A164.48 164.48 0 01325 400.8a15.94 15.94 0 00-8.82 12.14l-6.73 47.89a11.08 11.08 0 01-10.68 9.17h-85.54a11.11 11.11 0 01-10.69-8.87l-6.72-47.82a16.07 16.07 0 00-9-12.22 155.3 155.3 0 01-21.46-12.57 16 16 0 00-15.11-1.71l-44.89 18.07a10.81 10.81 0 01-13.14-4.58l-42.77-74a10.8 10.8 0 012.45-13.75l38.21-30a16.05 16.05 0 006-14.08c-.36-4.17-.58-8.33-.58-12.5s.21-8.27.58-12.35a16 16 0 00-6.07-13.94l-38.19-30A10.81 10.81 0 0149.48 186l42.77-74a10.81 10.81 0 0113.14-4.59l44.9 18.08a16.11 16.11 0 0015.17-1.75A164.48 164.48 0 01187 111.2a15.94 15.94 0 008.82-12.14l6.73-47.89A11.08 11.08 0 01213.23 42h85.54a11.11 11.11 0 0110.69 8.87l6.72 47.82a16.07 16.07 0 009 12.22 155.3 155.3 0 0121.46 12.57 16 16 0 0015.11 1.71l44.89-18.07a10.81 10.81 0 0113.14 4.58l42.77 74a10.8 10.8 0 01-2.45 13.75l-38.21 30a16.05 16.05 0 00-6.05 14.08c.33 4.14.55 8.3.55 12.47z"})});export{N as C,g as D,C as S,v as a,k as b,_ as c};
//# sourceMappingURL=symbols-DCjwlVtW.js.map
