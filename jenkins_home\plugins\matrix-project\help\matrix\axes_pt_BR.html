<div>
  Se sua matriz de configura&#231;&#227;o precisa de eixos adicionais, voc&#234; pode especific&#225;-los aqui.
  <p>
  Por exemplo, vamos dizer que voc&#234; est&#225; tentando executar um teste para sua aplica&#231;&#227;o de banco de dados,
  e voc&#234; precisa test&#225;-la com tr&#234;s bancos de dados: MySQL, PostgreSQL, e Oracle. Seu
  script de constru&#231;&#227;o est&#225; projetado tal que voc&#234; possa testar com um banco de dados particular usando
  <tt>ant -Ddatabase=mysql</tt>.
  <p>
  Este &#233; o conceito de um eixo. Voc&#234; pode ter uma vari&#225;vel chamada "database",
  que recebe tr&#234;s valores. Quando voc&#234; configur&#225;-<PERSON>, <PERSON> executar&#225; 3 constru&#231;&#245;es, cada uma com
  valores diferentes para a vari&#225;vel "database" para exaustivamente cobrir 
  a matriz de configura&#231;&#227;o.
  <p>
  Vari&#225;veis especificadas aqui se tornam dispon&#237;veis para a constru&#231;&#227;o como vari&#225;veis de ambiente.
  Em adi&#231;&#227;o, para Ant e Maven, as vari&#225;veis tamb&#233;m s&#227;o expostas como propriedades, como se
  <tt>-D<i>nomeDaVariavel</i>=<i>valor</i></tt> fossem especificadas na linha de comando.
  <p>
  Quando m&#250;ltiplos eixos s&#227;o especificados, todas as poss&#237;veis combina&#231;&#245;es dos eixos s&#227;o constru&#237;das
  exaustivamente. Valores m&#250;ltiplos em labels e JDKs s&#227;o tratados da mesma maneira. Assim
  se voc&#234; especificar jdk=[JDK5,JDK6], database=[mysql,postgresql,oracle], container=[jetty,tomcat],
  ent&#227;o cada constru&#231;&#227;o consistir&#225; de 2x3x2=12 diferentes sub-constru&#231;&#245;es.
</div>
