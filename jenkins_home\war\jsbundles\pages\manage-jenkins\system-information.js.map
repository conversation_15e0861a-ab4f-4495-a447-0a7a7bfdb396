{"version": 3, "file": "pages/manage-jenkins/system-information.js", "mappings": ";AAAA,MAAMA,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,WAAW,GAAG,EAAE;AACzE,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,aAAa,CAAC,aAAa,CAAC;AACvD,MAAMC,cAAc,GAAGN,QAAQ,CAACK,aAAa,CAAC,kBAAkB,CAAC;;AAEjE;AACAD,SAAS,CAACG,KAAK,CAACC,WAAW,GAAG,GAAGT,UAAU,MAAMI,WAAW,EAAE;;AAE9D;AACAG,cAAc,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAM;EAC9C,MAAMC,OAAO,GAAGV,QAAQ,CAACW,IAAI,CAACC,OAAO,CAACC,OAAO;EAC7C,MAAMC,IAAI,GAAGR,cAAc,CAACS,KAAK;EACjCX,SAAS,CAACY,SAAS,GAAG,aAAaN,OAAO,+DAA+DI,IAAI,UAAUf,UAAU,WAAWI,WAAW,aAAaO,OAAO,+DAA+DI,IAAI,UAAUf,UAAU,WAAWI,WAAW,4EAA4E;AACtW,CAAC,CAAC;;AAEF;AACAG,cAAc,CAACW,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC,C", "sources": ["webpack://jenkins-ui/./src/main/js/pages/manage-jenkins/system-information/index.js"], "sourcesContent": ["const imageWidth = document.getElementById(\"main-panel\").offsetWidth - 30;\nconst imageHeight = 500;\nconst graphHost = document.querySelector(\"#graph-host\");\nconst timespanSelect = document.querySelector(\"#timespan-select\");\n\n// Set the aspect ratio of the graph host so it doesn't resize when new graphs load\ngraphHost.style.aspectRatio = `${imageWidth} / ${imageHeight}`;\n\n// On select change load a new graph\ntimespanSelect.addEventListener(\"change\", () => {\n  const rootURL = document.head.dataset.rooturl;\n  const type = timespanSelect.value;\n  graphHost.innerHTML = `<img src=\"${rootURL}/jenkins.diagnosis.MemoryUsageMonitorAction/heap/graph?type=${type}&width=${imageWidth}&height=${imageHeight}\" srcset=\"${rootURL}/jenkins.diagnosis.MemoryUsageMonitorAction/heap/graph?type=${type}&width=${imageWidth}&height=${imageHeight}&scale=2 2x\" loading=\"lazy\" style=\"width: 100%\" alt=\"Memory usage graph\"/>`;\n});\n\n// Dispatch a change event to insert a graph on page load\ntimespanSelect.dispatchEvent(new Event(\"change\"));\n"], "names": ["imageWidth", "document", "getElementById", "offsetWidth", "imageHeight", "graphHost", "querySelector", "timespanSelect", "style", "aspectRatio", "addEventListener", "rootURL", "head", "dataset", "rooturl", "type", "value", "innerHTML", "dispatchEvent", "Event"], "sourceRoot": ""}