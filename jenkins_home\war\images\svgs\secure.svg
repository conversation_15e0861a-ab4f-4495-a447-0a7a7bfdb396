<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="48.000000px"
   height="48.000000px"
   id="svg2327"
   sodipodi:version="0.32"
   inkscape:version="0.44.1"
   sodipodi:docbase="C:\kohsuke\My Projects\hudson\hudson\main\war\images"
   sodipodi:docname="secure.svg">
  <defs
     id="defs3">
    <linearGradient
       id="linearGradient21644">
      <stop
         style="stop-color:black;stop-opacity:0.52577317;"
         offset="0"
         id="stop21646" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop21648" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient12071">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop12073" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop12075" />
    </linearGradient>
    <linearGradient
       id="linearGradient9845">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop9847" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0.49484536;"
         offset="1.0000000"
         id="stop9849" />
    </linearGradient>
    <linearGradient
       id="linearGradient11327">
      <stop
         style="stop-color:#7d6400;stop-opacity:1;"
         offset="0"
         id="stop11329" />
      <stop
         style="stop-color:#be9700;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop11331" />
    </linearGradient>
    <linearGradient
       id="linearGradient2092">
      <stop
         id="stop2094"
         offset="0"
         style="stop-color:#fff7b0;stop-opacity:1;" />
      <stop
         style="stop-color:#ffec41;stop-opacity:1.0000000;"
         offset="0.20999999"
         id="stop2098" />
      <stop
         id="stop2293"
         offset="0.83999997"
         style="stop-color:#e2cc00;stop-opacity:1;" />
      <stop
         id="stop2100"
         offset="1"
         style="stop-color:#c3af00;stop-opacity:1;" />
    </linearGradient>
    <linearGradient
       id="linearGradient11335">
      <stop
         style="stop-color:#6f716d;stop-opacity:1;"
         offset="0"
         id="stop11337" />
      <stop
         style="stop-color:#9ea09c;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop11339" />
    </linearGradient>
    <linearGradient
       id="linearGradient10591">
      <stop
         style="stop-color:#cad0c6;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop10593" />
      <stop
         id="stop10599"
         offset="0.50000000"
         style="stop-color:#eaece9;stop-opacity:1.0000000;" />
      <stop
         style="stop-color:#c5cbc0;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop10595" />
    </linearGradient>
    <linearGradient
       id="linearGradient2329">
      <stop
         id="stop2331"
         offset="0.0000000"
         style="stop-color:#ffffff;stop-opacity:1.0000000;" />
      <stop
         id="stop2333"
         offset="1.0000000"
         style="stop-color:#ffffff;stop-opacity:0.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient2711">
      <stop
         id="stop2713"
         offset="0.0000000"
         style="stop-color:#909090;stop-opacity:1.0000000;" />
      <stop
         id="stop2715"
         offset="1.0000000"
         style="stop-color:#bebebe;stop-opacity:0.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient2701">
      <stop
         id="stop2703"
         offset="0.0000000"
         style="stop-color:#585956;stop-opacity:1.0000000;" />
      <stop
         id="stop2705"
         offset="1.0000000"
         style="stop-color:#bbbeb8;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient2675">
      <stop
         id="stop2677"
         offset="0.0000000"
         style="stop-color:#5b5b97;stop-opacity:1.0000000;" />
      <stop
         id="stop2679"
         offset="1.0000000"
         style="stop-color:#1b1b43;stop-opacity:1.0000000;" />
    </linearGradient>
    <linearGradient
       id="linearGradient2667">
      <stop
         id="stop2669"
         offset="0.0000000"
         style="stop-color:#ffffff;stop-opacity:1.0000000;" />
      <stop
         id="stop2671"
         offset="1.0000000"
         style="stop-color:#fcfcff;stop-opacity:0.0000000;" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient2454">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop2456" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop2458" />
    </linearGradient>
    <linearGradient
       id="linearGradient2253">
      <stop
         style="stop-color:#8f8f8f;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop2255" />
      <stop
         style="stop-color:#494949;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop2257" />
    </linearGradient>
    <linearGradient
       id="linearGradient2245">
      <stop
         style="stop-color:#dde1d9;stop-opacity:1.0000000;"
         offset="0.0000000"
         id="stop2247" />
      <stop
         style="stop-color:#cacdc6;stop-opacity:1.0000000;"
         offset="1.0000000"
         id="stop2249" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient10591"
       id="linearGradient1886"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.480246,0,0,0.497322,29.22711,23.01153)"
       x1="12.886660"
       y1="4.3602757"
       x2="20.087339"
       y2="18.414022" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient11335"
       id="linearGradient1888"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.480246,0,0,0.497322,29.22711,23.01153)"
       x1="19.250618"
       y1="9.6635771"
       x2="16.198252"
       y2="6.0396547" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2092"
       id="linearGradient1890"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.490236,0,0,0.534297,29.28263,21.67589)"
       x1="6.7268200"
       y1="32.161697"
       x2="40.938126"
       y2="32.161697" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient11327"
       id="linearGradient1892"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.490236,0,0,0.534297,29.28263,22.22637)"
       x1="31.630468"
       y1="41.791817"
       x2="8.6713638"
       y2="25.793524" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient9845"
       id="linearGradient1894"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.453445,0,0,0.470026,30.17248,24.3894)"
       x1="10.907269"
       y1="25.002281"
       x2="30.875446"
       y2="36.127281" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient12071"
       id="linearGradient1896"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.980472,0,0,0.461806,23.89003,23.47875)"
       x1="14.217941"
       y1="6.8795347"
       x2="17.859085"
       y2="3.9566603" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient12071"
       id="linearGradient1898"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.980472,0,0,0.490236,34.67523,22.83397)"
       x1="11.500000"
       y1="20.579729"
       x2="11.779029"
       y2="14.259961" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2454"
       id="radialGradient2318"
       gradientUnits="userSpaceOnUse"
       gradientTransform="scale(1.925808,0.519262)"
       cx="12.575710"
       cy="67.501709"
       fx="12.575710"
       fy="67.501709"
       r="8.7662794" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient21644"
       id="radialGradient2444"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1,0,0,0.595238,0,14.875)"
       cx="25.125"
       cy="36.75"
       fx="25.125"
       fy="36.75"
       r="15.75" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="0.12156863"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="8"
     inkscape:cx="30.615385"
     inkscape:cy="20.25"
     inkscape:current-layer="layer1"
     showgrid="true"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:window-width="872"
     inkscape:window-height="756"
     inkscape:window-x="285"
     inkscape:window-y="36"
     inkscape:showpageshadow="false" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Lock Screen</dc:title>
        <dc:date />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>video</rdf:li>
            <rdf:li>display</rdf:li>
            <rdf:li>lock</rdf:li>
            <rdf:li>screen</rdf:li>
            <rdf:li>password</rdf:li>
            <rdf:li>session</rdf:li>
            <rdf:li>screensaver</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:source>http://jimmac.musichall.cz/</dc:source>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <path
       sodipodi:type="arc"
       style="opacity:0.63068183;color:black;fill:url(#radialGradient2444);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       id="path21642"
       sodipodi:cx="25.125"
       sodipodi:cy="36.75"
       sodipodi:rx="15.75"
       sodipodi:ry="9.375"
       d="M 40.875 36.75 A 15.75 9.375 0 1 1  9.375,36.75 A 15.75 9.375 0 1 1  40.875 36.75 z"
       transform="matrix(1.173803,0,0,0.6,-6.751416,17.575)" />
    <g
       id="g1879"
       transform="matrix(2.09913,0,0,1.833331,-62.64934,-43.70413)">
      <path
         sodipodi:nodetypes="cczcccczccc"
         id="path2086"
         d="M 34.238513,34.181365 L 34.238513,30.359668 C 34.238513,26.445675 36.861875,24.661287 40.762635,24.710167 C 44.684619,24.759046 47.274012,26.461946 47.274012,30.421985 L 47.267528,34.181365 L 44.874632,34.181365 L 44.874632,31.406199 C 44.810387,30.442875 45.141632,27.216102 40.790111,27.216102 C 36.408575,27.216102 36.666117,30.454534 36.681818,31.425378 L 36.681818,34.181365 L 34.238513,34.181365 z "
         style="fill:url(#linearGradient1886);fill-opacity:1;fill-rule:evenodd;stroke:url(#linearGradient1888);stroke-width:0.50975204;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <rect
         ry="1.0606657"
         rx="1.0647131"
         y="34.231865"
         x="32.468109"
         height="11.769073"
         width="17.156261"
         id="rect1314"
         style="fill:url(#linearGradient1890);fill-opacity:1;fill-rule:evenodd;stroke:url(#linearGradient1892);stroke-width:0.50975209;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
      <rect
         ry="0.66253376"
         rx="0.57864058"
         y="35.387321"
         x="33.559612"
         height="9.4392996"
         width="14.977587"
         id="rect6903"
         style="fill:none;fill-opacity:1;fill-rule:evenodd;stroke:url(#linearGradient1894);stroke-width:0.50975257;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:0.60109289" />
      <path
         sodipodi:nodetypes="ccsccc"
         id="rect11343"
         d="M 34.675226,30.571517 C 34.805219,27.673419 35.610937,25.490973 40.985429,25.305958 C 37.505396,25.7964 35.612515,26.812487 35.612515,29.842371 C 35.612515,29.842371 35.525705,33.597665 35.525705,33.597665 L 34.675226,33.597665 L 34.675226,30.571517 z "
         style="fill:url(#linearGradient1896);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1" />
      <rect
         ry="0"
         rx="0"
         y="28.716803"
         x="45.460419"
         height="4.90236"
         width="0.98047203"
         id="rect1345"
         style="fill:url(#linearGradient1898);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    </g>
  </g>
</svg>
