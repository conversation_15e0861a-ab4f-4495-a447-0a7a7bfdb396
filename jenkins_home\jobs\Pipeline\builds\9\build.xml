<?xml version='1.1' encoding='UTF-8'?>
<flow-build plugin="workflow-job@1532.va_9a_d244074a_3">
  <actions>
    <hudson.model.CauseAction>
      <causeBag class="linked-hash-map">
        <entry>
          <hudson.model.Cause_-UserIdCause>
            <userId>admin</userId>
          </hudson.model.Cause_-UserIdCause>
          <int>1</int>
        </entry>
      </causeBag>
    </hudson.model.CauseAction>
    <jenkins.metrics.impl.TimeInQueueAction plugin="metrics@4.2.32-476.v5042e1c1edd7">
      <queuingDurationMillis>4</queuingDurationMillis>
      <blockedDurationMillis>0</blockedDurationMillis>
      <buildableDurationMillis>0</buildableDurationMillis>
      <waitingDurationMillis>1</waitingDurationMillis>
    </jenkins.metrics.impl.TimeInQueueAction>
    <org.jenkinsci.plugins.workflow.libs.LibrariesAction plugin="pipeline-groovy-lib@752.vdddedf804e72">
      <libraries/>
    </org.jenkinsci.plugins.workflow.libs.LibrariesAction>
    <org.jenkinsci.plugins.pipeline.modeldefinition.actions.ExecutionModelAction plugin="pipeline-model-definition@2.2255.v56a_15e805f12">
      <stagesUUID>bdd2828e-ffa3-4b5a-baaf-e9dbb0083df6</stagesUUID>
      <pipelineDefs>
        <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTPipelineDef plugin="pipeline-model-api@2.2255.v56a_15e805f12">
          <stages>
            <stages>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
                <name>Checkout</name>
                <branches>
                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                    <name>default</name>
                    <steps>
                      <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                        <name>checkout</name>
                        <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                          <arguments class="linked-hash-map">
                            <entry>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                <key>scm</key>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-GStringValue>
                                <value class="string">${scm}</value>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-GStringValue>
                            </entry>
                          </arguments>
                        </args>
                      </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                    </steps>
                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                </branches>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
                <name>Build</name>
                <branches>
                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                    <name>default</name>
                    <steps>
                      <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                        <name>bat</name>
                        <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                          <arguments class="linked-hash-map">
                            <entry>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                <key>script</key>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                                <value class="string">mvnw clean package -DskipTests</value>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                            </entry>
                          </arguments>
                        </args>
                      </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                    </steps>
                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                </branches>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
                <post>
                  <conditions>
                    <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
                      <condition>always</condition>
                      <branch>
                        <name>default</name>
                        <steps>
                          <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                            <name>junit</name>
                            <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                              <arguments class="linked-hash-map">
                                <entry>
                                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                    <key>testResults</key>
                                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                                    <value class="string">**/target/surefire-reports/*.xml</value>
                                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                                </entry>
                              </arguments>
                            </args>
                          </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                        </steps>
                      </branch>
                    </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
                  </conditions>
                </post>
                <name>Test</name>
                <branches>
                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                    <name>default</name>
                    <steps>
                      <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                        <name>bat</name>
                        <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                          <arguments class="linked-hash-map">
                            <entry>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                <key>script</key>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                                <value class="string">mvnw test</value>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                            </entry>
                          </arguments>
                        </args>
                      </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                    </steps>
                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                </branches>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
                <name>Docker Build</name>
                <branches>
                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                    <name>default</name>
                    <steps>
                      <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                        <name>bat</name>
                        <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                          <arguments class="linked-hash-map">
                            <entry>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                <key>script</key>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                                <value class="string">docker build -t casecashback:latest .</value>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                            </entry>
                          </arguments>
                        </args>
                      </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                    </steps>
                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                </branches>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
                <name>Deploy</name>
                <branches>
                  <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                    <name>default</name>
                    <steps>
                      <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                        <name>bat</name>
                        <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                          <arguments class="linked-hash-map">
                            <entry>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                                <key>script</key>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                                <value class="string">docker-compose up -d</value>
                              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                            </entry>
                          </arguments>
                        </args>
                      </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                    </steps>
                  </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBranch>
                </branches>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStage>
            </stages>
            <uuid>bdd2828e-ffa3-4b5a-baaf-e9dbb0083df6</uuid>
          </stages>
          <postBuild>
            <conditions>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
                <condition>always</condition>
                <branch>
                  <name>default</name>
                  <steps>
                    <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                      <name>cleanWs</name>
                      <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                        <arguments class="linked-hash-map"/>
                      </args>
                    </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                  </steps>
                </branch>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
                <condition>success</condition>
                <branch>
                  <name>default</name>
                  <steps>
                    <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                      <name>echo</name>
                      <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                        <arguments class="linked-hash-map">
                          <entry>
                            <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <key>message</key>
                            </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                            <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                              <value class="string">Pipeline completed successfully!</value>
                            </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                          </entry>
                        </arguments>
                      </args>
                    </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                  </steps>
                </branch>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
              <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
                <condition>failure</condition>
                <branch>
                  <name>default</name>
                  <steps>
                    <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                      <name>echo</name>
                      <args class="org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTNamedArgumentList">
                        <arguments class="linked-hash-map">
                          <entry>
                            <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                              <key>message</key>
                            </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                            <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                              <value class="string">Pipeline failed!</value>
                            </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                          </entry>
                        </arguments>
                      </args>
                    </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTStep>
                  </steps>
                </branch>
              </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTBuildCondition>
            </conditions>
          </postBuild>
          <agent>
            <agentType>
              <key>any</key>
            </agentType>
          </agent>
          <tools>
            <tools class="linked-hash-map">
              <entry>
                <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                  <key>jdk</key>
                </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                  <value class="string">jdk-17</value>
                </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
              </entry>
              <entry>
                <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                  <key>maven</key>
                </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTKey>
                <org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
                  <value class="string">Maven</value>
                </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTValue_-ConstantValue>
              </entry>
            </tools>
          </tools>
        </org.jenkinsci.plugins.pipeline.modeldefinition.ast.ModelASTPipelineDef>
      </pipelineDefs>
    </org.jenkinsci.plugins.pipeline.modeldefinition.actions.ExecutionModelAction>
    <com.cloudbees.plugins.credentials.builds.CredentialsParameterBinder plugin="credentials@1415.v831096eb_5534">
      <boundCredentials class="concurrent-hash-map"/>
    </com.cloudbees.plugins.credentials.builds.CredentialsParameterBinder>
    <hudson.plugins.git.util.BuildData plugin="git@5.7.0">
      <buildsByBranchName>
        <entry>
          <string>refs/remotes/origin/main</string>
          <hudson.plugins.git.util.Build>
            <marked plugin="git-client@6.1.3">
              <sha1>b7aa19f56af69ae93f9e34f332f2f2a9b83c4861</sha1>
              <branches class="list">
                <hudson.plugins.git.Branch>
                  <sha1 reference="../../../sha1"/>
                  <name>refs/remotes/origin/main</name>
                </hudson.plugins.git.Branch>
              </branches>
            </marked>
            <revision reference="../marked"/>
            <hudsonBuildNumber>9</hudsonBuildNumber>
          </hudson.plugins.git.util.Build>
        </entry>
      </buildsByBranchName>
      <lastBuild reference="../buildsByBranchName/entry/hudson.plugins.git.util.Build"/>
      <remoteUrls>
        <string>https://github.com/CHTSaif/CaseCashBack.git</string>
      </remoteUrls>
    </hudson.plugins.git.util.BuildData>
    <org.jenkinsci.plugins.workflow.steps.scm.MultiSCMRevisionState plugin="workflow-scm-step@437.v05a_f66b_e5ef8">
      <revisionStates>
        <entry>
          <string>git https://github.com/CHTSaif/CaseCashBack.git</string>
          <hudson.scm.SCMRevisionState_-None/>
        </entry>
      </revisionStates>
    </org.jenkinsci.plugins.workflow.steps.scm.MultiSCMRevisionState>
    <jenkins.metrics.impl.SubTaskTimeInQueueAction plugin="metrics@4.2.32-476.v5042e1c1edd7">
      <queuingDurationMillis>48</queuingDurationMillis>
      <blockedDurationMillis>0</blockedDurationMillis>
      <buildableDurationMillis>43</buildableDurationMillis>
      <waitingDurationMillis>0</waitingDurationMillis>
      <executingDurationMillis>152914</executingDurationMillis>
      <workUnitCount>1</workUnitCount>
    </jenkins.metrics.impl.SubTaskTimeInQueueAction>
  </actions>
  <queueId>22</queueId>
  <timestamp>1750106468907</timestamp>
  <startTime>1750106468972</startTime>
  <result>FAILURE</result>
  <duration>161536</duration>
  <charset>UTF-8</charset>
  <keepLog>false</keepLog>
  <execution class="org.jenkinsci.plugins.workflow.cps.CpsFlowExecution">
    <result>FAILURE</result>
    <script>pipeline {
    agent any
    
    tools {
        jdk &apos;jdk-17&apos;
        maven &apos;Maven&apos;
    }
    
    stages {
        stage(&apos;Checkout&apos;) {
            steps {
                checkout scm
            }
        }
        
        stage(&apos;Build&apos;) {
            steps {
                bat &apos;mvnw clean package -DskipTests&apos;
            }
        }
        
        stage(&apos;Test&apos;) {
            steps {
                bat &apos;mvnw test&apos;
            }
            post {
                always {
                    junit &apos;**/target/surefire-reports/*.xml&apos;
                }
            }
        }
        
        stage(&apos;Docker Build&apos;) {
            steps {
                bat &apos;docker build -t casecashback:latest .&apos;
            }
        }
        
        stage(&apos;Deploy&apos;) {
            steps {
                bat &apos;docker-compose up -d&apos;
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            echo &apos;Pipeline completed successfully!&apos;
        }
        failure {
            echo &apos;Pipeline failed!&apos;
        }
    }
}</script>
    <loadedScripts class="linked-hash-map"/>
    <durabilityHint>MAX_SURVIVABILITY</durabilityHint>
    <timings class="map">
      <entry>
        <string>flowNode</string>
        <long>4632518253</long>
      </entry>
      <entry>
        <string>classLoad</string>
        <long>6243365339</long>
      </entry>
      <entry>
        <string>runQueue</string>
        <long>35305831531</long>
      </entry>
      <entry>
        <string>run</string>
        <long>23477410398</long>
      </entry>
      <entry>
        <string>parse</string>
        <long>853159678</long>
      </entry>
      <entry>
        <string>saveProgram</string>
        <long>11071495027</long>
      </entry>
    </timings>
    <internalCalls class="sorted-set">
      <string>hudson.model.Result.fromString</string>
      <string>org.jenkinsci.plugins.pipeline.StageStatus.TAG_NAME</string>
      <string>org.jenkinsci.plugins.workflow.job.WorkflowRun.result</string>
    </internalCalls>
    <sandbox>true</sandbox>
    <iota>75</iota>
    <head>1:75</head>
    <done>true</done>
    <resumeBlocked>false</resumeBlocked>
    <storageDir>workflow-completed</storageDir>
  </execution>
  <completed>true</completed>
  <checkouts class="hudson.util.PersistedList">
    <org.jenkinsci.plugins.workflow.job.WorkflowRun_-SCMCheckout>
      <scm class="hudson.plugins.git.GitSCM" plugin="git@5.7.0">
        <configVersion>2</configVersion>
        <userRemoteConfigs>
          <hudson.plugins.git.UserRemoteConfig>
            <url>https://github.com/CHTSaif/CaseCashBack.git</url>
            <credentialsId>github</credentialsId>
          </hudson.plugins.git.UserRemoteConfig>
        </userRemoteConfigs>
        <branches>
          <hudson.plugins.git.BranchSpec>
            <name>*/main</name>
          </hudson.plugins.git.BranchSpec>
        </branches>
        <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
        <submoduleCfg class="empty-list"/>
        <extensions/>
      </scm>
      <node></node>
      <workspace>/var/jenkins_home/workspace/Pipeline</workspace>
      <changelogFile>/var/jenkins_home/jobs/Pipeline/builds/9/changelog12688623705573481908.xml</changelogFile>
      <pollingBaseline class="hudson.scm.SCMRevisionState$None" reference="../../../actions/org.jenkinsci.plugins.workflow.steps.scm.MultiSCMRevisionState/revisionStates/entry/hudson.scm.SCMRevisionState_-None"/>
    </org.jenkinsci.plugins.workflow.job.WorkflowRun_-SCMCheckout>
    <org.jenkinsci.plugins.workflow.job.WorkflowRun_-SCMCheckout>
      <scm class="hudson.plugins.git.GitSCM" reference="../../org.jenkinsci.plugins.workflow.job.WorkflowRun_-SCMCheckout/scm"/>
      <node></node>
      <workspace>/var/jenkins_home/workspace/Pipeline</workspace>
      <pollingBaseline class="hudson.scm.SCMRevisionState$None" reference="../../../actions/org.jenkinsci.plugins.workflow.steps.scm.MultiSCMRevisionState/revisionStates/entry/hudson.scm.SCMRevisionState_-None"/>
    </org.jenkinsci.plugins.workflow.job.WorkflowRun_-SCMCheckout>
  </checkouts>
</flow-build>