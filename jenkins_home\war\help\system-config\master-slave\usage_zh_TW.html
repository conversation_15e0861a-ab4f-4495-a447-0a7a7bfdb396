<div>
  控制 Jenkins 怎麼在這部機器上排程建置。

  <dl>
    <dt><b>盡可能使用這個節點</b></dt>
    <dd>
      預設值，也是最常用的設定。 這個模式下，Jenkins 會不受拘束的使用
      Agent。一旦有建置作業可以在這個 Agent 上完成，Jenkins 就會用它。
    </dd>

    <dt><b>只保留給限定節點的作業</b></dt>
    <dd>
      這個模式下，Jenkins 只會在這部機器上建置那些明確指定只能在這裡跑的專案。
      將節點保留給特定種類的作業。 例如讓 Jenkins
      持續執行效能測試，您還可以同時將執行程式數設成 1，一次只執行一項效能測試。
      這個執行程式也不會因為要執行能在別的節點建置的作業而塞住。
    </dd>
  </dl>
</div>
