{"version": 3, "file": "simple-page.css", "mappings": "AAAA,KAEE,sBADA,YAEA,0CACA,wBAGF,KAKE,mCAFA,aACA,sBAHA,SACA,gBAGA,CAGF,iBAGE,mBAGF,iBACE,kCADF,YACE,kCCtBF,iBAGE,mBADA,oBAEA,8BACA,oCACA,QAAO,CALP,iBAKA,CAEA,+CAOE,oBADA,mBAJA,WACA,qBAEA,YADA,UAGA,CAGF,wBAIE,yCAFA,kBACA,WAFA,iBAGA,CAGF,uBAME,6CADA,6BAFA,OAFA,kBACA,QAEA,kBAEA,CAEA,+BARF,uBASI,uBAKF,8BACE,eAKN,2BACE,GACE,yBAIJ,kBAGE,mBAIA,8DALA,uBAGA,QADA,uBAHA,eAOA,sCAFA,WAEA,CAEA,yBAIE,6BAHA,WAEA,QAEA,YAHA,iBAGA,CAGF,mCACE,2CAGF,0BACE,UAEA,oBADA,mBACA,CAEA,mDACE,WACA,qBAKN,mCACE,GACE,WACA,sBC1DJ,iDAGE,qMAIA,wFAEA,qBAAsB,CACtB,uBAAwB,CACxB,sBAAuB,CACvB,yBAA0B,CAC1B,sBAAuB,CAGvB,sBAAuB,CACvB,yBAA0B,CAG1B,yBAA0B,CAC1B,oBAAgC,CAChC,qBAAsB,CACtB,mBAAoB,CAGpB,4CAA6C,CAC7C,wCAAyC,CACzC,yEAA4E,CAG5E,mCAAoC,CACpC,oCAAqC,CAGrC,wCAAyC,CACzC,yCAA0C,CAG1C,yBAA0B,CAG1B,mCAAoC,CACpC,gCAAiC,CACjC,gCAAiC,CACjC,sCAAuC,CACvC,uCAAwC,CAGxC,6EAKA,wFAOA,+CAAgD,CAChD,iFAKA,oFAKA,mCAAoC,CACpC,oEAAuE,CACvE,uEAA0E,CAC1E,yFAKA,iFAKA,oFAKA,4CAA6C,CAC7C,8EAKA,iFAOA,uCAAwC,CAGxC,4BAA6B,CAC7B,iGAKA,gFAOA,+EAIA,+FAEA,gFAcA,sEAAyE,CACzE,2CAA4C,CAC5C,yCAA0C,CAC1C,yCAA0C,CAC1C,6BAA8B,CAC9B,mCAAoC,CAGpC,uCAAwC,CACxC,gDAAiD,CACjD,qCAAsC,CACtC,kCAAmC,CACnC,yCAA0C,CAC1C,8CAA+C,CAC/C,iCAAkC,CAClC,gCAAiC,CACjC,8CAA+C,CAC/C,2CAA4C,CAG5C,gCAAiC,CACjC,sCAAuC,CACvC,qCAAsC,CACtC,sCAAuC,CACvC,2BAA4B,CAC5B,uCAAwC,CACxC,wCAAyC,CACzC,0CAA2C,CAG3C,iEAAkE,CAClE,wJAcA,8DAA+D,CAC/D,iCAAkC,CAClC,yJAKA,+DAAgE,CAChE,yJASA,yJAKA,mCAAoC,CACpC,gDAAiD,CACjD,6CAA8C,CAC9C,+CAAgD,CAChD,gCAAiC,CACjC,4CAA6C,CAC7C,6CAA8C,CAC9C,+CAAgD,CAGhD,uBAAwB,CACxB,0CAA2C,CAC3C,kCAAmC,CACnC,4CAA6C,CAC7C,8BAA+B,CAC/B,qCAAsC,CACtC,mCAAoC,CACpC,sBAAwB,CACxB,+BAAiC,CAGjC,mCAAoC,CACpC,oCAAqC,CACrC,qCAAsC,CACtC,sEAAyE,CACzE,6EAGA,8EAGA,+CAAgD,CAOhD,mEAAsE,CACtE,kCAAmC,CACnC,wFAKA,6CAAgD,CAChD,+CAAgD,CAChD,6CAAgD,CAChD,gDAAiD,CACjD,qCAAuC,CACvC,kDAAmD,CACnD,yBAA6C,CAG7C,wBAAyB,CACzB,yCAA0C,CAC1C,sCAAuC,CAGvC,0BAA2B,CAC3B,oFAKA,8EAKA,oFAKA,wCAAyC,CACzC,sCAAwC,CACxC,8CAAgD,CAChD,6CAA+C,CAS/C,gDAAiD,CACjD,oCAAqC,CACrC,mCAAoC,CACpC,0CAA2C,CAC3C,8DAA+D,CAC/D,yCAA0C,CAC1C,6BAA8B,CAC9B,2DAA8D,CAU9D,gCAAiC,CACjC,sDAA0D,CAG1D,4DAA6D,CAG7D,uEAA0E,CAC1E,+EAGA,gFAGA,6EAGA,yCAA0C,CAG1C,0EAA6E,CAC7E,6EAGA,0EAA6E,CAG7E,6BAA8B,CAC9B,sBAAuB,CACvB,mBAAoB,CACpB,uBAAwB,CAItB,mCAGE,yCACA,wCAJF,mCAGE,yCACA,wCAJF,mCAGE,yCACA,wCAJF,iCAGE,uCACA,sCAJF,oCAGE,0CACA,yCAJF,oCAGE,0CACA,yCAJF,kCAGE,wCACA,uCAJF,oCAGE,0CACA,yCAJF,8BAGE,oCACA,mCAJF,+BAGE,sCACA,oCAJF,mCAGE,yCACA,wCAJF,gEASA,sPAjOF,6DAhIF,iDAiII,0BAA2B,CAA3B,CAGF,8BApIF,iDAqII,sCAAuC,CACvC,wCAAyC,CACzC,gDAAiD,CAAjD,CAwCF,uEACE,wFAqBF,uEACE,8CAAoD,CAyCtD,8BA/OF,iDAgPI,qCAAsC,CAAtC,CA8CF,oCA9RF,iDA+RI,yBAA0B,CAC1B,0BAA2B,CAC3B,kCAAmC,CACnC,iCAAkC,CAAlC,CAYF,8BA9SF,iDA+SI,0CAA4C,CAC5C,gDAAkD,CAClD,qFC5UJ,OAKE,wBAHA,oCACA,8BACA,mCACA,CAGF,OACE,oBACA,kBAUF,uCAGE,8BAEA,wBALF,sBAMI,iCAKJ,qBAKE,qCAOF,0CAcE,cADA,gBADA,uCAIA,qCADA,YACA,CAGF,OAEE,iBAGF,OAEE,mBAGF,OAEE,oBAGF,OAEE,eAGF,OAEE,mBAGF,OAEE,kBAGF,qBACE,kCACA,iBAEA,qCADA,YACA,CAIA,yBACE,kCACA,WAIJ,EC3GE,oCADA,gGAGA,8BADA,yBACA,CAEA,OACE,wBAGF,UACE,gCAGF,gBAEE,+BACA,8GAGF,SACE,gCACA,gHAGF,8BDoFF,ECnFI,0BAEA,QACE,+BDoFN,yBAEE,mBADA,oBAEA,2BAEA,6BAGE,kCADA,YADA,UAEA,CEzHJ,+BAEE,mBADA,aAEA,2BAGF,wCACE,mBAGF,kBAEE,oBADA,iBACA,CAGF,wBAGE,WAIA,SAEA,gBADA,UAPA,kBACA,UAQA,mBACA,qBAMM,qHACE,kFACE,CAqCJ,mVACE,mFACE,CAON,6CACE,4EACE,CAIJ,4CACE,mBAMJ,uCACE,mBAEA,8CAEE,gFADA,qBAEE,CAOF,sDACE,sFACE,CAIJ,qDACE,6BAOV,wBAGE,uBAGA,eAJA,oBAMA,0CAJA,2BAGA,iBAFA,SAJA,iBAOA,CAEA,+BAYE,8BANA,kBAGA,6FACE,CATF,WACA,qBAMA,kBAHA,gBADA,eADA,kBAIA,qCAKA,CAGF,8BAGE,mBAOA,6BATA,WACA,aAOA,YALA,uBAGA,OAIA,4lCC1IE,qlCFwDF,CAOF,4BACE,sBALA,6BACA,sBAGF,CAPE,2BAEA,oBACA,CCyEA,kBACA,MDpEA,kBAGF,CAJE,oCACA,CCsEA,UDnEF,sCACE,cAGF,sCACE,2EAQE,8JACE,gCAIJ,iCACE,iBACA,CAFF,gBAIE,6CACE,6DAGF,CACE,6CAKF,kCACE,CAXF,cAUA,oBANA,iCACE,CAMA,8DGlGN,CHiGI,UGjGJ,qCAEE,sCAIA,0EAOF,uCACA,yCADA,YACA,2CACA,0DACA,CADA,wBACA,8EAEA,yDAMA,2DACA,yDACA,CAQA,mBARA,4CACA,iBAMA,kCACA,CALA,6CAGF,CAGE,iCACA,CAJF,cACE,CACA,oBACA,8BARA,eAEA,CAOA,OAEA,CAJA,+BACA,CACA,oBAHA,aAEA,mBATA,kBACA,+BASA,mBATA,SAWA,8CAIA,qBASE,CATF,mBASE,oBAVJ,iBACE,CASE,sCATF,UASE,2HACE,kCAMN,+BAEE,aACA,kNAHF,8CAsBI,uCACE,sBAMJ,wDAEE,+CAEA,CACA,4CAEA,qDAEA,oDAGE,kDAEA,oBACA,qBAEA,wDAKF,uCAJI,UAIJ,mFAGE,qEAGA,sEAEA,kHAQF,qFAEE,+DAUF,gEACE,gEAKF,6BAEA,0FAKA,8DACA,+DAGE,gGAIE,2BAGF,kPAqBF,8CACE,kBAOF,CARA,YACE,CAOF,gEACE,CAIJ,oFACE,gBACA,yBAGF,4CACE,sCAMF,WACE,kDAOE,0EALF,iBAKE,sCAOF,CAPE,cAOF,qDAYF,2BAHA,gBAGA,CAZE,uBACE,CADF,UACE,oCAWJ,uDAEE,iBAGF,8GAKE,kBAOJ,oBACE,CAVE,UAEA,CAUF,cAVE,iCAQF,qJAEA,CAVE,cAUF,wDACA,sEHxNA,CGwNA,mBHxNA,uDAGA,kBACA,iEAEA,sFGwNA,oDAGA,qDHvNA,8HAWA,gBACE,CADF,UAHE,UAIA,6DAIF,cACE,CADF,UAHE,UAIA,8DAGF,kBASI,6NACE,8FAKF,qBAGA,CAHA,YAGA,qDACE,sDAGF,cACE,uCADF,aACE,6GAMA,mBACA,mFG4KJ,oCC7RJ,oBACE,oCAKF,gCAEE,CALA,6BAMA,oDASA,+BAEA,CALA,4BAGA,CAJA,aAMA,wDAIA,YAXF,CAWE,WAXF,kFAeE,uEAIE,wEAIA,qCAGF,CACE,yRADF,iBACE,2HAUF,CAVE,mBAWA,oBAXA,kBAUF,qCACE,CAXA,UAWA,udAUF,yCAGE,sBACA,yGACA,4MAQA,oBACA,uBAMJ,8BALI,WAKJ,CACE,wBACA,iBACA,qBACA,gBAEA,mBANF,sCAUE,qBAVF,CAUE,UAHE,WAPJ,iCAgBE,kBACA,CAFF,qBACE,CACA,sBACA,CAHF,OAGE,eAEA,CATE,cAIJ,iBAKE,4DACA,YAEA,+EAQA,eAJA,0BACE,eAGF,oBACE,iBACA,CANF,kBAME,kDAEA,0bAwBF,uCACE,qcAuBA,4CJ1KF,qFACA,CADA,QACA,yGI8KI,kGAIA,CATF,iBJ1KA,CAAF,SImLI,qCAEA,WACA,iBJjLJ,CI+KI,gBACA,CJhLJ,2DACE,CADF,aACE,sBAGF,yEACE,2CAGF,+IAEE,wEACA,yEAGF,yEACE,yCACA,2IIqJA,6DJjJA,6CAEA,kBI+IA,oBJ/IA,yBACE,2CI4JA,uFAEE,aACA,iBACA,oCAIF,CAJE,mBAIF,6CACE,gBACA,CAFF,kBAEE,2CAEA,gBACA,sEAKF,uDAHE,iBAGF,6EAUJ,qCANQ,6CAMR,CAPM,qBACE,CADF,yCAON,wEA2CF,uBACE,CALE,gBAIJ,CAtBE,mCAEE,CAcF,mCAEE,CAIJ,SAKE,aAdE,gBAYF,iBAEA,CA3CE,mDAQA,CAGF,2CAKA,CAwBA,6BACA,CAFA,yBACA,CAZE,SAeF,6EAMI,uBAKN,gFAKA,+BACE,2JAYA,8BACE,2DCvQF,mDAIF,+EAMA,+BAEE,4DAEA,oDACA,+BACA,uEAGE,uGAIA,6BAIJ,gFAUE,kCAIA,CARA,WAGF,cACE,CAJA,iBAGF,CAKE,wFAWA,mBAGF,qCAIA,CAXE,UAGF,eACE,CAWF,mBACE,CAbF,kBAQA,qCAQA,sFAEE,oBACA,4FAKA,wCAGF,wDAEE,gBAGF,gDAGI,YACA,uBAIJ,aACE,gBACA,0BAEA,+DAKA,6BACE,yBAMF,eAEA,wBACE,+BAKJ,wBACE,CALE,gBAIJ,CACE", "sources": ["webpack://jenkins-ui/./src/main/scss/base/_core.scss", "webpack://jenkins-ui/./src/main/scss/components/_spinner.scss", "webpack://jenkins-ui/./src/main/scss/abstracts/_theme.scss", "webpack://jenkins-ui/./src/main/scss/base/_typography.scss", "webpack://jenkins-ui/./src/main/scss/abstracts/_mixins.scss", "webpack://jenkins-ui/./src/main/scss/form/_checkbox.scss", "webpack://jenkins-ui/./src/main/scss/form/_input.scss", "webpack://jenkins-ui/./src/main/scss/components/_buttons.scss", "webpack://jenkins-ui/./src/main/scss/pages/_sign-in-register.scss", "webpack://jenkins-ui/./src/main/scss/simple-page.scss"], "sourcesContent": ["html {\n  height: 100%;\n  box-sizing: border-box;\n  -webkit-tap-highlight-color: transparent;\n  color: var(--text-color);\n}\n\nbody {\n  margin: 0;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--background);\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\n::selection {\n  background: var(--selection-color);\n}\n", ".jenkins-spinner {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  font-size: var(--font-size-sm);\n  font-weight: var(--font-bold-weight);\n  margin: 0;\n\n  &::before,\n  &::after {\n    content: \"\";\n    display: inline-block;\n    width: 0.9lh;\n    height: 0.9lh;\n    border-radius: 100%;\n    border: 0.125lh solid currentColor;\n  }\n\n  &::before {\n    position: relative;\n    margin-right: 0.5lh;\n    opacity: 0.3;\n    border-color: var(--text-color-secondary);\n  }\n\n  &::after {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    translate: 0 -0.45lh;\n    clip-path: inset(0 0 50% 50%);\n    animation: loading-spinner 1s infinite linear;\n\n    @media (prefers-reduced-motion) {\n      animation-duration: 2s;\n    }\n  }\n\n  &:empty {\n    &::before {\n      margin-right: 0;\n    }\n  }\n}\n\n@keyframes loading-spinner {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.behavior-loading {\n  position: fixed;\n  display: flex !important;\n  align-items: center;\n  justify-content: center;\n  inset: 0;\n  z-index: 999;\n  backdrop-filter: blur(15px);\n  transition: var(--standard-transition);\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    inset: 0;\n    background: var(--background);\n    opacity: 0.95;\n  }\n\n  .jenkins-spinner {\n    animation: fade-in-jenkins-spinner 0.4s ease;\n  }\n\n  &--hidden {\n    opacity: 0;\n    visibility: collapse;\n    pointer-events: none;\n\n    .fade-in-jenkins-spinner {\n      opacity: 0.5;\n      transform: scale(0.75);\n    }\n  }\n}\n\n@keyframes fade-in-jenkins-spinner {\n  from {\n    opacity: 0.5;\n    transform: scale(0.75);\n  }\n}\n", "@use \"sass:color\";\n@use \"../base/breakpoints\";\n\n$colors: (\n  \"blue\": oklch(55% 0.2308 256.91),\n  \"brown\": oklch(60% 0.0941 72.67),\n  \"cyan\": oklch(60% 0.1497 234.48),\n  \"green\": oklch(70% 0.2155 150),\n  \"indigo\": oklch(60% 0.191 278.34),\n  \"orange\": oklch(70% 0.2001 50.74),\n  \"pink\": oklch(60% 0.2601 12.28),\n  \"purple\": oklch(60% 0.2308 314.6),\n  \"red\": oklch(60% 0.2671 30),\n  \"yellow\": oklch(80% 0.17 76),\n  \"teal\": oklch(60% 0.1122 216.72),\n  \"white\": #fff,\n  \"black\": oklch(from var(--accent-color) 2% 0.075 h),\n);\n$semantics: (\n  \"accent\": var(--blue),\n  \"text\": var(--black),\n  \"error\": var(--red),\n  \"warning\": var(--orange),\n  \"success\": var(--green),\n  \"destructive\": var(--red),\n  \"build\": var(--green),\n  \"danger\": var(--red),\n  \"info\": var(--blue),\n);\n\n:root,\n.app-theme-picker__picker[data-theme=\"none\"] {\n  // Font related properties\n  --font-family-sans:\n    system-ui, \"Segoe UI\", roboto, \"Noto Sans\", oxygen, ubuntu, cantarell,\n    \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", arial, sans-serif,\n    \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n  --font-family-mono:\n    ui-monospace, sfmono-regular, sf mono, jetbrainsmono, consolas, monospace;\n  --font-size-base: 1rem; // 16px\n  --font-size-sm: 0.875rem; // 14px\n  --font-size-xs: 0.75rem; // 12px\n  --font-size-monospace: 1em;\n  --font-bold-weight: 450;\n\n  // Line height\n  --line-height-base: 1.5;\n  --line-height-heading: 1.2;\n\n  // Color palette\n  --very-light-grey: #f8f8f8;\n  --light-grey: hsl(240 20% 96.5%);\n  --medium-grey: #9ba7af;\n  --dark-grey: #4d545d;\n\n  // branding\n  --secondary: oklch(from var(--black) 60% c h);\n  --focus-input-border: var(--accent-color);\n  --focus-input-glow: color-mix(in sRGB, var(--accent-color) 15%, transparent);\n\n  // State colors\n  --primary-hover: var(--accent-color);\n  --primary-active: var(--accent-color);\n\n  // Status icon colors\n  --weather-icon-color: var(--accent-color);\n  --unstable-build-icon-color: var(--orange);\n\n  // Background colors\n  --background: var(--white);\n\n  // Header\n  --brand-link-color: var(--secondary);\n  --header-link-color: var(--white);\n  --header-bg-classic: var(--black);\n  --header-link-bg-classic-hover: #404040;\n  --header-link-bg-classic-active: #404040;\n\n  // Breadcrumbs bar\n  --breadcrumbs-bar-background: oklch(\n    from var(--text-color) 96.8% 0.005 h / 0.8\n  );\n\n  // App bar\n  --bottom-app-bar-shadow: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 7.5%,\n    transparent\n  );\n\n  // Alert call outs\n  --alert-success-text-color: var(--success-color);\n  --alert-success-bg-color: color-mix(\n    in sRGB,\n    var(--success-color) 10%,\n    transparent\n  );\n  --alert-success-border-color: color-mix(\n    in sRGB,\n    var(--success-color) 5%,\n    transparent\n  );\n  --alert-info-text-color: var(--blue);\n  --alert-info-bg-color: color-mix(in sRGB, var(--blue) 10%, transparent);\n  --alert-info-border-color: color-mix(in sRGB, var(--blue) 5%, transparent);\n  --alert-warning-text-color: color-mix(\n    in sRGB,\n    var(--warning-color) 80%,\n    var(--text-color)\n  );\n  --alert-warning-bg-color: color-mix(\n    in sRGB,\n    var(--warning-color) 10%,\n    transparent\n  );\n  --alert-warning-border-color: color-mix(\n    in sRGB,\n    var(--warning-color) 5%,\n    transparent\n  );\n  --alert-danger-text-color: var(--error-color);\n  --alert-danger-bg-color: color-mix(\n    in sRGB,\n    var(--error-color) 10%,\n    transparent\n  );\n  --alert-danger-border-color: color-mix(\n    in sRGB,\n    var(--error-color) 5%,\n    transparent\n  );\n\n  // Typography\n  --text-color-secondary: var(--secondary);\n\n  // Borders\n  --jenkins-border-width: 1.5px;\n  --jenkins-border-color: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 15%,\n    var(--card-background)\n  );\n  --jenkins-border-color--subtle: color-mix(\n    in sRGB,\n    currentColor 1.5%,\n    transparent\n  );\n\n  /* This is a harsher border - for dividers, content blocks and more */\n  --jenkins-border: var(--jenkins-border-width) solid\n    var(--jenkins-border-color);\n\n  /* This is a subtle border - for increasing contrast on elements, such as buttons, menu and more */\n  --jenkins-border--subtle: var(--jenkins-border-width) solid\n    var(--jenkins-border-color--subtle);\n  --jenkins-border--subtle-shadow: 0 0 0 1.5px\n    var(--jenkins-border-color--subtle);\n\n  @media (resolution <= 1x) {\n    --jenkins-border-width: 2px;\n  }\n\n  @media (prefers-contrast: more) {\n    --focus-input-border: var(--text-color);\n    --jenkins-border-color: var(--text-color);\n    --jenkins-border-color--subtle: var(--text-color);\n  }\n\n  // Table\n  --table-background: oklch(from var(--text-color-secondary) l c h / 0.075);\n  --table-header-foreground: var(--text-color);\n  --table-body-background: var(--background);\n  --table-body-foreground: var(--text-color);\n  --table-border-radius: 0.75rem;\n  --table-row-border-radius: 0.3125rem;\n\n  // Deprecated\n  --even-row-color: var(--very-light-grey);\n  --bigtable-border-width: var(--pane-border-width);\n  --bigtable-header-bg: var(--dark-grey);\n  --bigtable-header-font-weight: bold; // Does specifying this make sense\n  --bigtable-header-text-color: var(--white);\n  --bigtable-row-border-color: var(--medium-grey);\n  --bigtable-cell-padding-x: 0.75rem;\n  --bigtable-cell-padding-y: 0.5rem;\n  --table-parameters-bg--hover: var(--light-grey);\n  --table-striped-bg--hover: var(--light-grey);\n\n  // Link\n  --link-color: var(--accent-color);\n  --link-visited-color: var(--link-color);\n  --link-color--hover: var(--link-color);\n  --link-color--active: var(--link-color);\n  --link-text-decoration: none;\n  --link-text-decoration--hover: underline;\n  --link-text-decoration--active: underline;\n  --link-font-weight: var(--font-bold-weight);\n\n  // Command Palette\n  --command-palette-results-backdrop-filter: saturate(1.5) blur(5px);\n  --command-palette-inset-shadow:\n    inset 0 0 2px 2px rgb(255 255 255 / 0.1),\n    var(--jenkins-border--subtle-shadow),\n    0 5px 10px var(--jenkins-border-color--subtle);\n\n  ::backdrop {\n    --command-palette-backdrop-background: color-mix(\n      in sRGB,\n      var(--black) 17.5%,\n      transparent\n    );\n  }\n\n  // Tooltips\n  --tooltip-backdrop-filter: contrast(1.1) saturate(2) blur(20px);\n  --tooltip-color: var(--text-color);\n  --tooltip-box-shadow:\n    0 0 8px 2px rgb(0 0 50 / 0.05), var(--jenkins-border--subtle-shadow),\n    0 10px 50px rgb(0 0 20 / 0.1), inset 0 -1px 2px rgb(255 255 255 / 0.025);\n\n  // Dropdowns\n  --dropdown-backdrop-filter: contrast(1.1) saturate(2) blur(20px);\n  --dropdown-box-shadow:\n    var(--jenkins-border--subtle-shadow), 0 10px 30px rgb(0 0 20 / 0.1),\n    0 2px 10px rgb(0 0 20 / 0.05), inset 0 -1px 2px rgb(255 255 255 / 0.025);\n\n  // Dialogs\n  ::backdrop {\n    --dialog-backdrop-background: hsl(240 10% 20% / 0.8);\n  }\n\n  --dialog-box-shadow:\n    var(--jenkins-border--subtle-shadow), 0 10px 40px rgb(0 0 20 / 0.15),\n    0 2px 15px rgb(0 0 20 / 0.05), inset 0 0 2px 2px rgb(255 255 255 / 0.025);\n\n  // Dark link\n  --link-dark-color: var(--text-color);\n  --link-dark-visited-color: var(--link-dark-color);\n  --link-dark-color--hover: var(--primary-hover);\n  --link-dark-color--active: var(--primary-active);\n  --link-dark-text-decoration: none;\n  --link-dark-text-decoration--hover: underline;\n  --link-dark-text-decoration--active: underline;\n  --link-dark-font-weight: var(--font-bold-weight);\n\n  // Pane\n  --pane-border-width: 1px;\n  --pane-header-text-color: var(--text-color);\n  --pane-header-bg: var(--light-grey);\n  --pane-header-border-color: var(--light-grey);\n  --pane-header-font-weight: bold;\n  --pane-border-color: var(--light-grey);\n  --pane-text-color: var(--text-color);\n  --pane-link-color: black;\n  --pane-link-color--visited: black;\n\n  // Cards\n  --card-background: var(--background);\n  --card-background--hover: transparent;\n  --card-background--active: transparent;\n  --card-border-color: oklch(from var(--text-color-secondary) l c h / 0.15);\n  --card-border-color--hover: oklch(\n    from var(--text-color-secondary) l c h / 0.3\n  );\n  --card-border-color--active: oklch(\n    from var(--text-color-secondary) l c h / 0.5\n  );\n  --card-border-width: var(--jenkins-border-width);\n\n  @media (prefers-contrast: more) {\n    --card-border-color: var(--text-color);\n  }\n\n  // Tab bar\n  --tabs-background: oklch(from var(--text-color-secondary) l c h / 0.1);\n  --tabs-item-background: transparent;\n  --tabs-item-foreground: color-mix(\n    in sRGB,\n    var(--text-color-secondary),\n    var(--text-color)\n  );\n  --tabs-item-background--hover: rgb(0 0 0 / 0.05);\n  --tabs-item-foreground--hover: var(--text-color);\n  --tabs-item-background--active: rgb(0 0 0 / 0.1);\n  --tabs-item-foreground--active: var(--text-color);\n  --tabs-item-background--selected: white;\n  --tabs-item-foreground--selected: var(--link-color);\n  --tabs-border-radius: calc((10px + 34px) / 2);\n\n  // Side panel\n  --side-panel-width: 340px;\n  --panel-header-bg-color: var(--light-grey);\n  --panel-border-color: var(--light-grey);\n\n  // Form\n  --section-padding: 1.625rem;\n  --input-color: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 1.5%,\n    var(--background)\n  );\n  --input-border: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 25%,\n    transparent\n  );\n  --input-border-hover: color-mix(\n    in sRGB,\n    var(--text-color-secondary) 50%,\n    transparent\n  );\n  --input-hidden-password-bg-color: #f9f9f9;\n  --form-item-max-width: min(65vw, 1600px);\n  --form-item-max-width--medium: min(50vw, 1400px);\n  --form-item-max-width--small: min(35vw, 1200px);\n\n  @media screen and (max-width: breakpoints.$tablet-breakpoint) {\n    --section-padding: 1.25rem;\n    --form-item-max-width: 100%;\n    --form-item-max-width--medium: 100%;\n    --form-item-max-width--small: 100%;\n  }\n\n  --form-label-font-weight: var(--font-bold-weight);\n  --form-input-padding: 0.5rem 0.625rem;\n  --form-input-border-radius: 0.625rem;\n  --form-input-glow: 0 0 0 0.5rem transparent;\n  --form-input-glow--focus: 0 0 0 0.25rem var(--focus-input-glow);\n  --pre-background: var(--button-background);\n  --pre-color: var(--text-color);\n  --selection-color: oklch(from var(--accent-color) l c h / 0.2);\n\n  @media (prefers-contrast: more) {\n    --input-border: var(--text-color) !important;\n    --input-border-hover: var(--text-color) !important;\n    --form-input-glow--focus: 0 0 0 4px\n      color-mix(in sRGB, var(--text-color), transparent);\n  }\n\n  // Animations\n  --standard-transition: 0.25s ease;\n  --elastic-transition: 0.3s cubic-bezier(0, 0.68, 0.5, 1.5);\n\n  // Plugin manager\n  --plugin-manager-bg-color-already-upgraded: var(--light-grey);\n\n  // Default button\n  --button-background: oklch(from var(--text-color-secondary) l c h / 0.075);\n  --button-background--hover: oklch(\n    from var(--text-color-secondary) l c h / 0.125\n  );\n  --button-background--active: oklch(\n    from var(--text-color-secondary) l c h / 0.175\n  );\n  --button-box-shadow--focus: oklch(\n    from var(--text-color-secondary) l c h / 0.1\n  );\n  --button-color--primary: var(--background);\n\n  // Variables for sidebar items, card items\n  --item-background--hover: oklch(from var(--text-color-secondary) l c h / 0.1);\n  --item-background--active: oklch(\n    from var(--text-color-secondary) l c h / 0.15\n  );\n  --item-box-shadow--focus: oklch(from var(--text-color-secondary) l c h / 0.1);\n\n  // Deprecated\n  --primary: var(--accent-color); // Use var(--accent-color) instead\n  --success: var(--green); // Use var(--success-color) instead\n  --danger: var(--red); // Use var(--destructive-color) instead\n  --warning: var(--orange); // Use var(--warning-color) instead\n\n  // Colors\n  @each $key, $value in $colors {\n    --#{$key}: #{$value};\n\n    @if $key != \"black\" and $key != \"white\" {\n      --light-#{$key}: #{color.adjust($value, $lightness: 20%)};\n      --dark-#{$key}: #{color.adjust($value, $lightness: -20%)};\n    }\n  }\n\n  @each $key, $value in $semantics {\n    --#{$key}-color: #{$value};\n  }\n}\n", "@use \"../abstracts/mixins\";\n@use \"../base/breakpoints\";\n\nbody,\np {\n  font-family: var(--font-family-sans);\n  font-size: var(--font-size-sm);\n  line-height: var(--line-height-base);\n  color: var(--text-color);\n}\n\nbutton {\n  font-family: inherit;\n  font-size: inherit;\n}\n\ntable,\ntd,\nth,\nform {\n  font-size: var(--font-size-sm);\n}\n\ninput,\ntextarea,\nselect {\n  font-size: var(--font-size-sm);\n\n  @media (max-width: breakpoints.$tablet-breakpoint) {\n    font-size: var(--font-size-base);\n  }\n}\n\n// Reset monospaced font-size, because browsers reduce it by default to ~81%\npre,\ncode,\nkbd,\nsamp,\ntt {\n  font-size: var(--font-size-monospace);\n}\n\n/*\n * Headings\n */\n\nh1,\n.h1,\nh2,\n.h2,\nh3,\n.h3,\nh4,\n.h4,\nh5,\n.h5,\nh6,\n.h6 {\n  line-height: var(--line-height-heading);\n  font-weight: 600;\n  display: block;\n  margin-top: 0;\n  margin-bottom: var(--section-padding);\n}\n\nh1,\n.h1 {\n  font-size: 1.5rem;\n}\n\nh2,\n.h2 {\n  font-size: 1.375rem;\n}\n\nh3,\n.h3 {\n  font-size: 1.1875rem;\n}\n\nh4,\n.h4 {\n  font-size: 1rem;\n}\n\nh5,\n.h5 {\n  font-size: 0.8125rem;\n}\n\nh6,\n.h6 {\n  font-size: 0.625rem;\n}\n\n.jenkins-description {\n  color: var(--text-color-secondary);\n  line-height: 1.66;\n  margin-top: 0;\n  margin-bottom: var(--section-padding);\n}\n\n.jenkins-label {\n  &--tertiary {\n    color: var(--text-color-secondary);\n    opacity: 0.7;\n  }\n}\n\na {\n  @include mixins.link;\n}\n\n.jenkins-link--with-icon {\n  display: inline-flex;\n  align-items: center;\n  justify-content: flex-start;\n\n  svg {\n    width: 16px;\n    height: 16px;\n    color: var(--text-color) !important;\n  }\n}\n", "@mixin link {\n  text-decoration: var(--link-text-decoration);\n  font-weight: var(--link-font-weight);\n  text-underline-offset: 2px;\n  text-decoration-thickness: 2px;\n\n  &:link {\n    color: var(--link-color);\n  }\n\n  &:visited {\n    color: var(--link-visited-color);\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--link-color--hover);\n    text-decoration: var(--link-text-decoration--hover);\n  }\n\n  &:active {\n    color: var(--link-color--active);\n    text-decoration: var(--link-text-decoration--active);\n  }\n\n  @media (prefers-contrast: more) {\n    text-decoration: underline;\n\n    &:hover {\n      text-decoration-thickness: 3px;\n    }\n  }\n}\n\n@mixin link-dark {\n  text-decoration: var(--link-dark-text-decoration);\n  font-weight: var(--link-dark-font-weight);\n\n  &:link {\n    color: var(--link-dark-color);\n  }\n\n  &:visited {\n    color: var(--link-dark-visited-color);\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--link-dark-color--hover);\n    text-decoration: var(--link-dark-text-decoration--hover);\n  }\n\n  &:active {\n    color: var(--link-dark-color--active);\n    text-decoration: var(--link-dark-text-decoration--active);\n  }\n}\n\n@mixin item($border: true) {\n  position: relative;\n  appearance: none;\n  z-index: 0;\n  text-decoration: none !important;\n  font-weight: normal;\n  border-radius: var(--form-input-border-radius);\n  cursor: pointer;\n  background: transparent;\n  outline: none;\n  border: none;\n\n  &::before,\n  &::after {\n    position: absolute;\n    content: \"\";\n    inset: 0;\n    z-index: -1;\n    border-radius: inherit;\n    transition: var(--standard-transition);\n    pointer-events: none;\n  }\n\n  &::before {\n    background-color: var(--item-background);\n    border: var(--jenkins-border--subtle);\n  }\n\n  &::after {\n    box-shadow: 0 0 0 0.5rem transparent;\n  }\n\n  &:focus-visible {\n    outline: none;\n  }\n\n  &:not(:disabled) {\n    &:hover,\n    &:focus-visible,\n    &[aria-describedby],\n    &[aria-expanded=\"true\"] {\n      &::before {\n        background-color: var(--item-background--hover);\n      }\n    }\n\n    &:active {\n      outline: none !important;\n      z-index: 1;\n\n      &::before {\n        background-color: var(--item-background--active);\n      }\n\n      &::after {\n        box-shadow: 0 0 0 0.25rem var(--item-box-shadow--focus);\n      }\n    }\n\n    &:focus-visible {\n      &::after {\n        box-shadow: 0 0 0 0.2rem var(--text-color) !important;\n        opacity: 1 !important;\n      }\n    }\n  }\n\n  @if $border == false {\n    &:not(:hover, &:active, &:focus) {\n      &::before {\n        border-color: transparent;\n      }\n    }\n  }\n}\n", ".jenkins-checkbox-help-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n}\n\n.jenkins-checkbox + a.jenkins-help-button {\n  vertical-align: top;\n}\n\n.jenkins-checkbox {\n  position: relative;\n  display: inline-flex;\n}\n\n.jenkins-checkbox input {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n\n  // If margin is set to a negative value it can cause text to be announced in\n  // the wrong order in VoiceOver for OSX\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  clip-path: inset(50%);\n\n  &:not(:disabled) {\n    &:active,\n    &:focus {\n      & + label {\n        &::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 5px var(--focus-input-border);\n        }\n      }\n    }\n\n    &:checked {\n      &:active,\n      &:focus {\n        & + label {\n          &::before {\n            box-shadow:\n              var(--form-input-glow--focus),\n              inset 0 0 0 12px var(--focus-input-border);\n          }\n        }\n      }\n    }\n  }\n\n  &:checked {\n    &:active,\n    &:focus {\n      & + label {\n        &::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 12px var(--focus-input-border);\n        }\n      }\n    }\n  }\n\n  &:checked {\n    & + label {\n      &:active,\n      &:focus {\n        &::before {\n          box-shadow:\n            var(--form-input-glow--focus),\n            inset 0 0 0 12px var(--focus-input-border);\n        }\n      }\n    }\n\n    & + label {\n      &::before {\n        box-shadow:\n          var(--form-input-glow),\n          inset 0 0 0 12px var(--focus-input-border);\n      }\n\n      &::after {\n        transform: scale(1);\n      }\n    }\n  }\n\n  &:disabled {\n    & + label {\n      cursor: not-allowed;\n\n      &::before {\n        opacity: 0.35 !important;\n        box-shadow:\n          var(--form-input-glow),\n          inset 0 0 0 2px var(--input-border) !important;\n      }\n    }\n\n    &:checked {\n      & + label {\n        &::before {\n          box-shadow:\n            var(--form-input-glow),\n            inset 0 0 0 12px var(--focus-input-border) !important;\n        }\n\n        &::after {\n          transform: scale(1) !important;\n        }\n      }\n    }\n  }\n}\n\n.jenkins-checkbox label {\n  position: relative;\n  display: inline-flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n  margin: 0;\n  cursor: pointer;\n  line-height: 22px;\n  font-weight: var(--form-label-font-weight);\n\n  &::before {\n    content: \"\";\n    display: inline-block;\n    position: relative;\n    min-width: 22px;\n    min-height: 22px;\n    border-radius: 6px;\n    transition: var(--standard-transition);\n    margin-right: 11px;\n    box-shadow:\n      var(--form-input-glow),\n      inset 0 0 0 var(--jenkins-border-width) var(--input-border);\n    background: var(--input-color);\n  }\n\n  &::after {\n    content: \"\";\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 22px;\n    height: 22px;\n    background: var(--background);\n    mask-image: url(\"data:image/svg+xml;charset=UTF-8,%3c?xml version='1.0' encoding='UTF-8'?%3e%3csvg width='384px' height='320px' viewBox='0 0 384 320' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3ePath%3c/title%3e%3cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3e%3cpath d='M327.917546,10.9278525 C339.555371,-2.37251966 359.771775,-3.72027991 373.072147,7.91754577 C386.239516,19.4389932 387.692129,39.368305 376.427694,52.671077 L376.082454,53.0721475 L152.082454,309.072147 C140.014868,322.863675 118.889432,323.700972 105.767743,311.015951 L105.372583,310.627417 L9.372583,214.627417 C-3.12419433,202.13064 -3.12419433,181.86936 9.372583,169.372583 C21.7443926,157.000773 41.7261905,156.877055 54.2501999,169.001429 L54.627417,169.372583 L126.441,241.186 L327.917546,10.9278525 Z' id='Path' fill='%23FF0000' fill-rule='nonzero'%3e%3c/path%3e%3c/g%3e%3c/svg%3e\");\n    mask-size: 10px 10px;\n    mask-repeat: no-repeat;\n    mask-position: center;\n    transition: var(--elastic-transition);\n    transform: scale(0);\n  }\n\n  &:empty {\n    &::before {\n      margin-right: 0;\n    }\n  }\n\n  &:hover {\n    &::before {\n      box-shadow:\n        var(--form-input-glow),\n        inset 0 0 0 5px var(--input-border-hover);\n    }\n  }\n\n  &:active,\n  &:focus {\n    &::before {\n      box-shadow:\n        var(--form-input-glow--focus),\n        inset 0 0 0 5px var(--focus-input-border);\n    }\n  }\n}\n\n.jenkins-checkbox__description {\n  margin-left: 34px;\n  color: var(--text-color-secondary);\n  line-height: 1.66;\n}\n", ".jenkins-input {\n  display: block;\n  background: var(--input-color);\n  border: var(--jenkins-border-width) solid var(--input-border);\n  padding: var(--form-input-padding);\n  border-radius: var(--form-input-border-radius);\n  width: 100%;\n  min-height: 2.375rem;\n  box-shadow: var(--form-input-glow);\n\n  // Set height transition to 0s as vertical resizing has a delay/lag otherwise\n  transition:\n    all var(--standard-transition),\n    height 0s,\n    padding 0s;\n\n  &:not(:disabled) {\n    &:hover {\n      border-color: var(--input-border-hover);\n    }\n\n    &:active,\n    &:focus {\n      outline: none;\n      border-color: var(--focus-input-border);\n      box-shadow: var(--form-input-glow--focus);\n    }\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n}\n\ninput,\ntextarea,\nselect {\n  color: var(--text-color);\n  background-color: var(--input-color);\n  font-family: inherit;\n}\n", "@use \"../abstracts/mixins\";\n\n.jenkins-button {\n  --item-background: var(--button-background);\n  --item-background--hover: var(--button-background--hover);\n  --item-background--active: var(--button-background--active);\n  --item-box-shadow--focus: var(--button-box-shadow--focus);\n\n  @include mixins.item;\n\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0;\n  padding: 0.5rem 1rem;\n  font-size: var(--font-size-sm);\n  color: var(--text-color) !important;\n  min-height: 2.375rem;\n  white-space: nowrap;\n  gap: 1ch;\n\n  svg {\n    width: 1.125rem;\n    height: 1.125rem;\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    filter: saturate(0.6);\n    cursor: not-allowed;\n  }\n}\n\n.jenkins-button--primary {\n  --button-background: oklch(from var(--accent-color) l c h);\n  --button-background--hover: oklch(from var(--accent-color) l c h / 0.9);\n  --button-background--active: oklch(from var(--accent-color) l c h / 0.8);\n  --button-box-shadow--focus: oklch(from var(--accent-color) l c h / 0.4);\n\n  color: var(--button-color--primary) !important;\n}\n\n// Support for custom colors\n// Modifier classes must include 'color' in name to work\n.jenkins-button[class*=\"color\"] {\n  --button-background: oklch(from currentColor l c h / 0.1);\n  --button-background--hover: oklch(from currentColor l c h / 0.15);\n  --button-background--active: oklch(from currentColor l c h / 0.25);\n  --button-box-shadow--focus: oklch(from currentColor l c h / 0.125);\n\n  color: var(--color) !important;\n}\n\n.jenkins-button--primary[class*=\"color\"] {\n  --button-background: oklch(from var(--color) l c h);\n  --button-background--hover: oklch(from var(--color) l c h / 0.9);\n  --button-background--active: oklch(from var(--color) l c h / 0.8);\n  --button-box-shadow--focus: oklch(from var(--color) l c h / 0.4);\n\n  color: var(--background) !important;\n}\n\n.jenkins-button--tertiary {\n  --button-background: transparent !important;\n\n  &:not(\n      :hover,\n      &:active,\n      &:focus,\n      &[aria-describedby],\n      &[aria-expanded=\"true\"]\n    ) {\n    &::before {\n      border-color: transparent;\n    }\n  }\n}\n\n// Additional button related classes\n.jenkins-buttons-row {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n\n  &--invert {\n    justify-content: flex-end;\n  }\n\n  &--equal-width {\n    .jenkins-button {\n      min-width: 6.5rem;\n    }\n  }\n\n  &--equal-width {\n    min-width: 6.5rem;\n  }\n\n  @media (width <= 600px) {\n    justify-content: stretch;\n\n    .jenkins-button {\n      flex-grow: 1;\n    }\n  }\n}\n\n.jenkins-copy-button {\n  .jenkins-copy-button__icon {\n    position: relative;\n    display: grid;\n    grid-template-columns: 1fr;\n    place-items: center;\n    width: 1.125rem;\n    height: 1.125rem;\n    transition: var(--standard-transition);\n\n    svg {\n      grid-area: 1 / 1;\n      scale: -0.5;\n      opacity: 0;\n      transition: var(--elastic-transition);\n      filter: blur(2px);\n      color: var(--success-color);\n\n      * {\n        stroke-width: 40px;\n      }\n    }\n\n    &::before,\n    &::after {\n      content: \"\";\n      position: relative;\n      width: 0.6875rem;\n      height: 0.875rem;\n      border: 0.1rem solid currentColor;\n      border-radius: 0.2rem;\n      transition:\n        translate var(--standard-transition),\n        scale var(--standard-transition),\n        opacity var(--standard-transition),\n        filter var(--standard-transition);\n      grid-area: 1 / 1;\n    }\n\n    &::before {\n      translate: -16% -10%;\n      clip-path: polygon(\n        100% 0,\n        100% 22.5%,\n        22.5% 22.5%,\n        32.5% 100%,\n        0 100%,\n        0 0\n      );\n    }\n\n    &::after {\n      translate: 16% 10%;\n    }\n  }\n\n  &--copied {\n    color: var(--success-color) !important;\n\n    --button-background: color-mix(\n      in sRGB,\n      var(--success-color) 10%,\n      transparent\n    ) !important;\n    --button-background--hover: var(--button-background);\n    --button-background--active: var(--button-background);\n\n    .jenkins-copy-button__icon {\n      &::before,\n      &::after {\n        scale: 1.25;\n        opacity: 0;\n        filter: blur(1px);\n      }\n\n      svg {\n        scale: 1.25;\n        opacity: 1;\n        filter: blur(0);\n      }\n    }\n  }\n\n  &:hover {\n    .jenkins-copy-button__icon {\n      &::before {\n        translate: -11% -8%;\n      }\n\n      &::after {\n        translate: 11% 8%;\n      }\n    }\n  }\n\n  &:active {\n    .jenkins-copy-button__icon {\n      transform: scale(0.85);\n    }\n  }\n}\n\n.jenkins-validate-button__container {\n  &__status {\n    .validation-error-area {\n      min-height: 36px !important;\n    }\n  }\n\n  .validation-error-area--visible {\n    margin-top: 0;\n    margin-bottom: 0.625rem;\n  }\n\n  & > .jenkins-button {\n    float: right;\n  }\n}\n\n.advanced-button,\n.hetero-list-add {\n  svg {\n    width: 0.875rem;\n    height: 0.875rem;\n    transition: var(--standard-transition);\n  }\n\n  &:not([data-expanded=\"true\"]) {\n    &:active {\n      svg {\n        translate: 0 0.125rem;\n      }\n    }\n  }\n\n  &[data-expanded=\"true\"] {\n    svg {\n      rotate: 180deg;\n    }\n  }\n}\n\n$jenkins-split-button-border-radius: 0.2rem;\n\n.jenkins-split-button {\n  display: flex;\n  gap: 1px;\n\n  & > :first-child {\n    border-top-right-radius: $jenkins-split-button-border-radius;\n    border-bottom-right-radius: $jenkins-split-button-border-radius;\n  }\n\n  & > .jenkins-button:last-of-type {\n    padding: 0 5px;\n    border-top-left-radius: $jenkins-split-button-border-radius;\n    border-bottom-left-radius: $jenkins-split-button-border-radius;\n\n    svg {\n      width: 0.8rem;\n      height: 0.8rem;\n    }\n  }\n}\n\n.stop-button-link {\n  --item-background: color-mix(in sRGB, var(--red) 15%, transparent);\n  --item-background--hover: color-mix(in sRGB, var(--red) 20%, transparent);\n  --item-background--active: color-mix(in sRGB, var(--red) 25%, transparent);\n  --item-box-shadow--focus: transparent;\n\n  @include mixins.item;\n\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 1rem;\n  height: 1rem;\n  border-radius: 0.25rem;\n\n  svg {\n    width: 87.5%;\n    height: 87.5%;\n    color: var(--red);\n\n    * {\n      stroke-width: 40px;\n    }\n  }\n}\n", "@use \"../abstracts/mixins\";\n@use \"../abstracts/theme\";\n@use \"../base/typography\";\n@use \"../form/checkbox\";\n@use \"../form/input\";\n@use \"../components/buttons\";\n\n@property --opacity {\n  syntax: \"<angle>\";\n  initial-value: 0deg;\n  inherits: false;\n}\n\n.app-sign-in-register {\n  width: 100vw;\n  height: 100vh;\n  padding: 0;\n  margin: 0;\n}\n\n.app-sign-in-register__branding {\n  position: fixed;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 40vw;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n\n  @media (width < 992px) {\n    display: none;\n  }\n\n  &::before,\n  &::after {\n    content: \"\";\n    position: absolute;\n    min-height: 130%;\n    min-width: 130%;\n    aspect-ratio: 1;\n    object-fit: cover;\n    z-index: -1;\n  }\n\n  &::before {\n    background-image:\n      radial-gradient(at 40% 20%, var(--orange) 0, transparent 50%),\n      radial-gradient(at 80% 0%, var(--cyan) 0, transparent 50%),\n      radial-gradient(at 0% 50%, var(--light-pink) 0, transparent 50%),\n      radial-gradient(at 80% 50%, var(--light-red) 0, transparent 50%),\n      radial-gradient(at 0% 100%, var(--light-yellow) 0, transparent 50%),\n      radial-gradient(at 80% 100%, var(--dark-purple) 0, transparent 50%),\n      radial-gradient(at 0% 0%, var(--pink) 0, transparent 50%);\n  }\n\n  &::after {\n    background-image:\n      radial-gradient(at 40% 20%, var(--light-cyan) 0, transparent 50%),\n      radial-gradient(at 80% 0%, var(--dark-orange) 0, transparent 50%),\n      radial-gradient(at 0% 50%, var(--light-blue) 0, transparent 50%),\n      radial-gradient(at 80% 50%, var(--light-green) 0, transparent 50%),\n      radial-gradient(at 0% 100%, var(--light-red) 0, transparent 50%),\n      radial-gradient(at 80% 100%, var(--light-yellow) 0, transparent 50%),\n      radial-gradient(at 0% 0%, var(--cyan) 0, transparent 50%);\n  }\n\n  &__starburst {\n    position: absolute;\n    inset: 0;\n    z-index: 1;\n    backdrop-filter: invert(2%) blur(100px);\n    mask-image: repeating-conic-gradient(\n      from var(--opacity),\n      var(--background) 0deg,\n      transparent 20deg\n    );\n  }\n\n  img {\n    min-height: 15rem;\n    height: 25vh;\n    max-height: 30rem;\n    z-index: 1;\n  }\n}\n\n.app-sign-in-register__content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n\n  @media (width >= 992px) {\n    margin-left: 40vw;\n  }\n\n  @media (height <= 600px) {\n    align-items: stretch;\n  }\n}\n\n.app-sign-in-register__content-inner {\n  display: flex;\n  align-items: stretch;\n  flex-direction: column;\n  gap: 1.6rem;\n  margin: calc(var(--section-padding) * 3) var(--section-padding);\n  width: 100%;\n\n  @media (width >= 430px) {\n    width: 430px;\n  }\n\n  * {\n    box-sizing: border-box;\n  }\n\n  h1 {\n    font-size: 1.8rem;\n    font-family: Georgia, serif;\n    text-align: left;\n    margin-bottom: 0;\n\n    @media (width <= 600px) {\n      font-size: 1.6rem;\n    }\n  }\n\n  input {\n    padding: 0.6rem 0.75rem;\n    border-radius: var(--form-input-border-radius);\n    font-size: 0.95rem;\n\n    @media (width <= 600px) {\n      font-size: 1rem;\n    }\n  }\n\n  .app-sign-in-register__form-label {\n    display: block;\n    font-size: 0.95rem;\n    margin-bottom: 0.5rem;\n    font-weight: var(--font-bold-weight);\n  }\n\n  button {\n    padding: 0.9rem 1rem;\n    font-size: 0.95rem;\n  }\n\n  form {\n    display: contents;\n  }\n\n  .app-sign-in-register__switcher {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    &::before {\n      content: \"\";\n      position: absolute;\n      inset: calc(50% + 1px) 0;\n      height: 2px;\n      border-radius: var(--form-input-border-radius);\n      background-color: var(--input-border);\n    }\n\n    a {\n      @include mixins.link;\n\n      z-index: 1;\n      padding: 0 0.2rem;\n      font-weight: var(--font-bold-weight);\n      font-size: 0.95rem;\n      margin: 0;\n      color: var(--link-color);\n      text-underline-offset: 2px;\n      text-decoration-thickness: 2px;\n      position: relative;\n      outline: none;\n\n      &::before {\n        content: \"\";\n        position: absolute;\n        inset: 0 -0.5rem;\n        background-color: var(--background);\n        z-index: -1;\n      }\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        inset: 0 -0.1rem;\n        border-radius: 0.4rem;\n        box-shadow: 0 0 0 0.625rem transparent;\n        transition: var(--standard-transition);\n        pointer-events: none;\n      }\n\n      &:focus-visible {\n        text-decoration: none;\n\n        &::after {\n          box-shadow: 0 0 0 0.2rem var(--text-color);\n        }\n      }\n    }\n  }\n\n  .jenkins-checkbox {\n    font-size: 0.95rem;\n  }\n\n  & > div {\n    &:empty {\n      display: none;\n    }\n  }\n}\n\n@keyframes aurora-one {\n  0% {\n    opacity: 0.3;\n  }\n\n  50% {\n    opacity: 0.15;\n    transform: rotate(-180deg);\n  }\n\n  100% {\n    opacity: 0.3;\n    transform: rotate(-360deg);\n  }\n}\n\n@keyframes aurora-two {\n  0% {\n    opacity: 0.15;\n  }\n\n  50% {\n    opacity: 0.5;\n    transform: rotate(180deg);\n  }\n\n  100% {\n    opacity: 0.15;\n    transform: rotate(360deg);\n  }\n}\n\n.app-sign-in-register__error {\n  font-size: 0.95rem;\n  color: var(--error-color);\n  font-weight: var(--font-bold-weight);\n\n  &:empty {\n    display: none;\n  }\n\n  &:not(:empty) {\n    & + input {\n      margin-top: 0.7rem;\n    }\n  }\n}\n\n.app-sign-in-register__password-notice {\n  font-size: 0.95rem;\n  color: var(--text-color-secondary);\n}\n\n.jenkins-input--error {\n  border-color: var(--error-color);\n}\n\n.inputHeader {\n  display: flex;\n  justify-content: space-between;\n}\n\n#passwordStrengthWrapper {\n  margin-top: 0.75rem;\n  font-size: 0.95rem;\n\n  #passwordStrength {\n    transition: var(--standard-transition);\n  }\n}\n", "/*\n * The MIT License\n *\n * Copyright (c) 2018-2020, CloudBees, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n@use \"./base/core\";\n@use \"./components/spinner\";\n@use \"./pages/sign-in-register\";\n\n.simple-page h1 {\n  font-size: 24px;\n  line-height: normal;\n}\n\n.simple-page h1,\n.simple-page .signupTag,\n.simple-page .restarting {\n  text-align: center;\n}\n\n.simple-page .safe-restarting {\n  padding: 10px 30px;\n  border-radius: var(--form-input-border-radius);\n  text-align: center;\n  margin-top: 2rem;\n  background-color: var(--alert-success-bg-color);\n  border: 1px solid var(--alert-success-border-color);\n\n  * {\n    color: var(--alert-success-text-color) !important;\n  }\n\n  strong {\n    font-weight: var(--font-bold-weight);\n  }\n}\n\n.simple-page .safe-restarting > p {\n  margin: 0;\n}\n\n.simple-page--description {\n  margin-top: 0;\n  margin-bottom: var(--section-padding);\n}\n\n.simple-page form {\n  width: 300px;\n}\n\n.simple-page .jenkins-form-item {\n  max-width: 100%;\n}\n\n.simple-page {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n}\n\n.simple-page .jenkins-button {\n  width: 100%;\n}\n\n.simple-page .jenkins-input {\n  width: 94%;\n}\n\n.simple-page .logo {\n  text-align: center;\n}\n\n.simple-page .logo > img {\n  height: 140px;\n}\n\n.simple-page form .submit input {\n  background-color: var(--btn-primary-bg, #0b6aa2);\n  border: solid 1px;\n  border-color: var(--btn-primary-bg, #0b6aa2);\n}\n\n.simple-page .danger {\n  border: 1px solid;\n  border-color: var(--danger-color, #c4000a);\n}\n\n.simple-page .alert,\n.simple-page .jenkins-alert {\n  color: var(--danger-color, #c4000a);\n}\n\n@keyframes fade-in-jenkins-booting {\n  from {\n    scale: 97.5%;\n    opacity: 0;\n    filter: blur(1px);\n  }\n}\n\n.app-jenkins-booting {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  animation: fade-in-jenkins-booting 0.4s both 0.2s;\n\n  // Optical compensation to visually center content\n  margin-top: -3rem;\n\n  * {\n    font-size: 1rem;\n    margin: 0;\n  }\n}\n\n.loading {\n  margin: 1rem 0 0;\n\n  p {\n    font-weight: var(--font-bold-weight);\n    font-size: 1.125rem;\n  }\n}\n\n.restarting {\n  color: var(--text-color-secondary);\n}\n"], "names": [], "sourceRoot": ""}