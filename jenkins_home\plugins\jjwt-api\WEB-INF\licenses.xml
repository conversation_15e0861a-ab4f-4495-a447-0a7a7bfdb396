<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='jjwt-api' version='0.11.5-120.v0268cf544b_89'><l:dependency name='Java JSON Web Token (JJWT) Plugin' groupId='io.jenkins.plugins' artifactId='jjwt-api' version='0.11.5-120.v0268cf544b_89' url='https://github.com/jenkinsci/jjwt-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JJWT :: API' groupId='io.jsonwebtoken' artifactId='jjwt-api' version='0.11.5' url='https://github.com/jwtk/jjwt/jjwt-api'><l:description>JSON Web Token support for the JVM and Android</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='JJWT :: Impl' groupId='io.jsonwebtoken' artifactId='jjwt-impl' version='0.11.5' url='https://github.com/jwtk/jjwt/jjwt-impl'><l:description>JSON Web Token support for the JVM and Android</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='JJWT :: Extensions :: Jackson' groupId='io.jsonwebtoken' artifactId='jjwt-jackson' version='0.11.5' url='https://github.com/jwtk/jjwt/jjwt-jackson'><l:description>JSON Web Token support for the JVM and Android</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency></l:dependencies>