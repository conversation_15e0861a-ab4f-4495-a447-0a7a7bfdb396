<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="48.000000px"
   height="48.000000px"
   id="svg1306"
   sodipodi:version="0.32"
   inkscape:version="0.42"
   sodipodi:docbase="/home/<USER>/projekt/tango/scalable"
   sodipodi:docname="edit-delete.svg"
   inkscape:export-filename="/home/<USER>/projekt/tango/22/delete.png"
   inkscape:export-xdpi="90.000000"
   inkscape:export-ydpi="90.000000">
  <defs
     id="defs1308">
    <linearGradient
       inkscape:collect="always"
       id="linearGradient2251">
      <stop
         style="stop-color:#ef2929"
         offset="0"
         id="stop2253" />
      <stop
         style="stop-color:#cc0000"
         offset="1"
         id="stop2255" />
    </linearGradient>
    <linearGradient
       id="linearGradient4126"
       inkscape:collect="always">
      <stop
         id="stop4128"
         offset="0"
         style="stop-color:#000000;stop-opacity:1;" />
      <stop
         id="stop4130"
         offset="1"
         style="stop-color:#000000;stop-opacity:0;" />
    </linearGradient>
    <radialGradient
       r="17.142857"
       fy="40.000000"
       fx="23.857143"
       cy="40.000000"
       cx="23.857143"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.500000,1.635742e-14,20.00000)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient7449"
       xlink:href="#linearGradient4126"
       inkscape:collect="always" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2251"
       id="radialGradient2257"
       cx="20.935379"
       cy="12.592707"
       fx="20.935379"
       fy="12.592707"
       r="19.967958"
       gradientTransform="matrix(-1.262871,2.796553e-2,-3.606716e-2,-1.629656,47.36662,36.49787)"
       gradientUnits="userSpaceOnUse" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4126"
       id="radialGradient2275"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.500000,1.713935e-14,20.00000)"
       cx="23.857143"
       cy="40.000000"
       fx="23.857143"
       fy="40.000000"
       r="17.142857" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2251"
       id="radialGradient2277"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-0.618549,1.369740e-2,-1.766555e-2,-0.798198,22.46266,17.62692)"
       cx="20.935379"
       cy="12.592707"
       fx="20.935379"
       fy="12.592707"
       r="19.967958" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="14.000000"
     inkscape:cx="27.981306"
     inkscape:cy="29.284234"
     inkscape:current-layer="layer1"
     showgrid="true"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     fill="#73d216"
     stroke="#a40000"
     inkscape:window-width="1280"
     inkscape:window-height="949"
     inkscape:window-x="0"
     inkscape:window-y="25"
     showguides="true"
     inkscape:guide-bbox="true" />
  <metadata
     id="metadata1311">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Delete</dc:title>
        <dc:date>2005-12-28</dc:date>
        <dc:creator>
          <cc:Agent>
            <dc:title>Andreas Nilsson</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://tango-project.org</dc:source>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>delete</rdf:li>
            <rdf:li>remove</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <path
       transform="matrix(1.225000,0.000000,0.000000,0.408334,-5.221224,25.17622)"
       d="M 41.000000 40.000000 A 17.142857 8.5714283 0 1 1  6.7142868,40.000000 A 17.142857 8.5714283 0 1 1  41.000000 40.000000 z"
       sodipodi:ry="8.5714283"
       sodipodi:rx="17.142857"
       sodipodi:cy="40.000000"
       sodipodi:cx="23.857143"
       id="path6548"
       style="opacity:0.52688169;color:#000000;fill:url(#radialGradient7449);fill-opacity:1.0000000;fill-rule:nonzero;stroke:none;stroke-width:1.0000000;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:block;overflow:visible"
       sodipodi:type="arc" />
    <path
       style="opacity:1.0000000;fill:url(#radialGradient2257);fill-opacity:1.0000000;stroke:#a40000;stroke-width:0.99999982;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4.0000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000"
       d="M 24.035816,3.5720836 C 13.289574,3.5720836 4.5678570,12.294146 4.5678580,23.040834 C 4.5678580,33.787521 13.289575,42.509583 24.035816,42.509584 C 34.782058,42.509584 43.503776,33.787520 43.503775,23.040834 C 43.503775,12.294147 34.782058,3.5720836 24.035816,3.5720836 z M 24.004517,8.4783336 C 32.049892,8.4783336 38.589837,14.990984 38.589837,23.009584 C 38.589837,25.981868 37.657973,28.737374 36.117218,31.040834 L 15.960683,10.915834 C 18.272680,9.3813936 21.022553,8.4783336 24.004517,8.4783336 z M 12.267404,14.447084 L 32.580435,34.728334 C 30.166684,36.490827 27.221538,37.540834 24.004517,37.540834 C 15.959141,37.540834 9.4191980,31.028184 9.4191980,23.009584 C 9.4191980,19.803270 10.497961,16.851741 12.267404,14.447084 z "
       id="path1314" />
    <path
       sodipodi:type="arc"
       style="opacity:0.55376345;fill:none;fill-opacity:1.0000000;stroke:#ffffff;stroke-width:0.98682600;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4.0000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000"
       id="path2242"
       sodipodi:cx="28.357143"
       sodipodi:cy="27.214285"
       sodipodi:rx="18.357143"
       sodipodi:ry="18.142857"
       d="M 46.714287 27.214285 A 18.357143 18.142857 0 1 1  10.000000,27.214285 A 18.357143 18.142857 0 1 1  46.714287 27.214285 z"
       transform="matrix(1.007576,0.000000,0.000000,1.019157,-4.568194,-4.726048)" />
    <path
       style="opacity:0.47849461;fill:none;fill-opacity:0.75000000;fill-rule:evenodd;stroke:#ffffff;stroke-width:1.0000000px;stroke-linecap:round;stroke-linejoin:miter;stroke-opacity:1.0000000"
       d="M 15.075203,11.366727 L 35.646632,31.938156"
       id="path2261"
       sodipodi:nodetypes="cc" />
    <path
       style="opacity:0.30645159;fill:#ffffff;fill-opacity:1.0000000;fill-rule:evenodd;stroke:none;stroke-width:1.0000000px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1.0000000"
       d="M 4.4323460,19.795298 C 4.4561550,21.985774 9.8371077,20.461965 9.8609167,21.652441 C 9.8132977,19.842917 11.837108,15.961965 12.289489,15.152441 L 20.932346,23.509584 C 20.884728,21.771489 27.122823,23.390537 27.003776,21.509584 L 16.718061,10.866727 C 18.241871,10.081013 21.837109,8.7952976 24.075204,8.9381546 C 32.313299,9.1524406 38.051394,16.795298 38.075204,22.009584 L 37.503775,27.009584 C 37.384727,28.866727 43.337108,26.795298 43.146632,28.295298 C 43.551394,27.152441 44.027584,24.795298 43.932346,22.081013 C 43.884727,12.438155 35.765679,3.3667266 24.003775,3.1524406 C 15.420441,3.2833936 8.4978220,8.1822026 6.1287740,14.661370 C 5.9933010,14.694830 4.6585360,16.842917 4.4323460,19.795298 z "
       id="path2263"
       sodipodi:nodetypes="cccccccccccccc" />
  </g>
</svg>
