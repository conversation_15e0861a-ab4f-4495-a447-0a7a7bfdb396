<div>
  Indien uw configuratiematrix extra dimensies benodigd, kunt U die hier opgeven.
  <p>
  Stel dat U uw databankapplicatie wenst te testen t.o.v. 3 databanken :  MySQL, PostgreSQL, en Oracle. 
  U hebt uw bouwspecifcatie  zo ontworpen dat U, door het opgeven van een optie, kunt kiezen t.o.v. welk systeem U wenst te testen, bvb.
  <tt>ant -Ddatabank=mysql</tt>.
  <p>
  Dit is het concept van een matrixdimensie.  U kunt een variabele "databank" hebben, met drie waarden.  <PERSON><PERSON> g<PERSON>on<PERSON>gu<PERSON><PERSON>, zal Jenkins 3 bouwpogingen lanceren, elk met een andere waarde voor die parameter.  Op die manier worden alle mogelijkheden in de matrix afgetoetst.
  <p>
De hier gedefinieerde variabelen worden als omgevingsparameters beschikbaar gesteld aan uw bouwpogingen.
  Meer nog, in het geval U Ant of <PERSON><PERSON> g<PERSON>, zullen de variabelen ook als parameters beschikbaar gesteld worden, alsof 
  <tt>-D<i>parameterNaam</i>=<i>waarde</i></tt> op de commandolijn meegegeven werd.
  <p>
 Indien U meerdere dimensies opgeeft, zullen alle mogelijke combinaties afgetoetst worden. Meerdere waarden in labels en JDKs worden op dezelfde manier behandeld.  Indien U dus  jdk=[JDK5,JDK6], database=[mysql,postgresql,oracle], container=[jetty,tomcat] opgeeft, zal een bouwpoging feitelijk  2x3x2=12 verschillende bouwpogingen omvatten.
</div>