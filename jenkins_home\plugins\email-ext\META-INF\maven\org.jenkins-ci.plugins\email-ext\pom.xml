<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>email-ext</artifactId>
  <version>1876.v28d8d38315b_d</version>
  <packaging>hpi</packaging>
  <name>Email Extension Plugin</name>
  <description>Allows to configure every aspect of email notifications: when an email is sent, who should receive it and what the email says</description>
  <url>https://github.com/jenkinsci/email-ext-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>https://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <contributors>
    <contributor>
      <name>Ash Lux</name>
      <email><EMAIL></email>
      <roles>
        <role>Past Maintainer</role>
      </roles>
    </contributor>
    <contributor>
      <name>Kyle Sweeney</name>
      <roles>
        <role>Past Maintainer</role>
      </roles>
    </contributor>
    <contributor>
      <name>K. R. Walker</name>
      <email><EMAIL></email>
      <roles>
        <role>Past Maintainer</role>
      </roles>
    </contributor>
    <contributor>
      <name>Seiji Sogabe</name>
      <email><EMAIL></email>
      <roles>
        <role>Past Maintainer</role>
      </roles>
    </contributor>
    <contributor>
      <name>Alex Earl</name>
      <email><EMAIL></email>
      <roles>
        <role>Past Maintainer</role>
      </roles>
    </contributor>
    <contributor>
      <name>David van Laatum</name>
      <email><EMAIL></email>
      <roles>
        <role>Past Maintainer</role>
      </roles>
      <timezone>9.5</timezone>
    </contributor>
  </contributors>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/email-ext-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/email-ext-plugin.git</developerConnection>
    <tag>28d8d38315bd64c6f2be577af21b76009ea4eae9</tag>
    <url>https://github.com/jenkinsci/email-ext-plugin</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.jenkins-ci.org/issues/?jql=project+%3D+JENKINS+AND+status+in+%28Open%2C+%22In+Progress%22%2C+Reopened%29+AND+component+%3D+email-ext-plugin</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>jakarta-mail-api</artifactId>
      <version>2.1.3-1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.htmlparser.jericho</groupId>
      <artifactId>jericho-html</artifactId>
      <version>3.4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>config-file-provider</artifactId>
      <version>980.v88956a_a_5d6a_d</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>credentials</artifactId>
      <version>1405.vb_cda_74a_f8974</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>jackson2-api</artifactId>
      <version>2.17.0-379.v02de8ec9f64c</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>junit</artifactId>
      <version>1312.v1a_235a_b_94a_31</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>mailer</artifactId>
      <version>489.vd4b_25144138f</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>matrix-project</artifactId>
      <version>845.vffd7fa_f27555</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>scm-api</artifactId>
      <version>703.v72ff4b_259600</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>script-security</artifactId>
      <version>1369.v9b_98a_4e95b_2d</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>structs</artifactId>
      <version>338.v848422169819</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>token-macro</artifactId>
      <version>400.v35420b_922dcb_</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins.workflow</groupId>
      <artifactId>workflow-job</artifactId>
      <version>1496.v5b_18defc07f2</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins.workflow</groupId>
      <artifactId>workflow-step-api</artifactId>
      <version>678.v3ee58b_469476</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.18.3</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.60</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
