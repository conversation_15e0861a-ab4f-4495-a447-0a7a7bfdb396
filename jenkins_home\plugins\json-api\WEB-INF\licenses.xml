<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='json-api' version='20250517-153.vc8a_a_d87c0ce3'><l:dependency name='JSON Api Plugin' groupId='io.jenkins.plugins' artifactId='json-api' version='20250517-153.vc8a_a_d87c0ce3' url='https://github.com/jenkinsci/json-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT No Attribution License' url='https://opensource.org/license/mit-0/'/></l:dependency><l:dependency name='JSON in Java' groupId='org.json' artifactId='json' version='20250517' url='https://github.com/douglascrockford/JSON-java'><l:description>JSON is a light-weight, language independent, data interchange format.
        See http://www.JSON.org/

        The files in this package implement JSON encoders/decoders in Java.
        It also includes the capability to convert between JSON and XML, HTTP
        headers, Cookies, and CDL.

        This is a reference implementation. There are a large number of JSON packages
        in Java. Perhaps someday the Java community will standardize on one. Until
        then, choose carefully.</l:description><l:license name='Public Domain' url='https://github.com/stleary/JSON-java/blob/master/LICENSE'/></l:dependency></l:dependencies>