<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>git-client</artifactId>
  <version>6.1.3</version>
  <packaging>hpi</packaging>
  <name>Jenkins Git client plugin</name>
  <description>The Jenkins Plugins Parent POM Project</description>
  <url>https://github.com/jenkinsci/git-client-plugin</url>
  <inceptionYear>2013</inceptionYear>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/git-client-plugin</connection>
    <developerConnection>scm:git:**************:jenkinsci/git-client-plugin.git</developerConnection>
    <tag>git-client-6.1.3</tag>
    <url>https://github.com/jenkinsci/git-client-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins</groupId>
      <artifactId>configuration-as-code</artifactId>
      <version>1932.v75cb_b_f1b_698d</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>gson-api</artifactId>
      <version>2.12.1-113.v347686d6729f</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins.mina-sshd-api</groupId>
      <artifactId>mina-sshd-api-common</artifactId>
      <version>2.14.0-143.v2b_362fc39576</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins.mina-sshd-api</groupId>
      <artifactId>mina-sshd-api-core</artifactId>
      <version>2.14.0-143.v2b_362fc39576</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit</artifactId>
      <version>7.0.0.202409031743-r</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-codec</groupId>
          <artifactId>commons-codec</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit.http.apache</artifactId>
      <version>7.0.0.202409031743-r</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpcore</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit.http.server</artifactId>
      <version>7.0.0.202409031743-r</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit.lfs</artifactId>
      <version>7.0.0.202409031743-r</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit.ssh.apache</artifactId>
      <version>7.0.0.202409031743-r</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>net.i2p.crypto</groupId>
          <artifactId>eddsa</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.sshd</groupId>
          <artifactId>sshd-osgi</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.sshd</groupId>
          <artifactId>sshd-sftp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>apache-httpcomponents-client-4-api</artifactId>
      <version>4.5.14-269.vfa_2321039a_83</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>credentials</artifactId>
      <version>1408.va_622a_b_f5b_1b_1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>script-security</artifactId>
      <version>1369.v9b_98a_4e95b_2d</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>ssh-credentials</artifactId>
      <version>349.vb_8b_6b_9709f5b_</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>structs</artifactId>
      <version>338.v848422169819</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
