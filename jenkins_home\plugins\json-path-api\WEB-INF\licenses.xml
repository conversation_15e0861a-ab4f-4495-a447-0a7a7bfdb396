<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='json-path-api' version='2.9.0-148.v22a_7ffe323ce'><l:dependency name='JSON Path API Plugin' groupId='io.jenkins.plugins' artifactId='json-path-api' version='2.9.0-148.v22a_7ffe323ce' url='https://github.com/jenkinsci/json-path-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='JSON-smart-action Small and Fast Parser' groupId='net.minidev' artifactId='json-smart-action' version='2.5.2' url='https://urielch.github.io/'><l:description>JSON (JavaScript Object Notation) is a lightweight data-interchange format. It is easy for humans to read and write. It is easy for machines to parse and generate. It is based on a subset of the JavaScript Programming Language, Standard ECMA-262 3rd Edition - December 1999. JSON is a text format that is completely language independent but uses conventions that are familiar to programmers of the C-family of languages, including C, C++, C#, Java, JavaScript, Perl, Python, and many others. These properties make JSON an ideal data-interchange language.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='json-path' groupId='com.jayway.jsonpath' artifactId='json-path' version='2.9.0' url='https://github.com/jayway/JsonPath'><l:description>A library to query and verify JSON</l:description><l:license name='The Apache Software License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JSON Small and Fast Parser' groupId='net.minidev' artifactId='json-smart' version='2.5.2' url='https://urielch.github.io/'><l:description>JSON (JavaScript Object Notation) is a lightweight data-interchange format. It is easy for humans to read and write. It is easy for machines to parse and generate. It is based on a subset of the JavaScript Programming Language, Standard ECMA-262 3rd Edition - December 1999. JSON is a text format that is completely language independent but uses conventions that are familiar to programmers of the C-family of languages, including C, C++, C#, Java, JavaScript, Perl, Python, and many others. These properties make JSON an ideal data-interchange language.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='ASM based accessors helper used by json-smart' groupId='net.minidev' artifactId='accessors-smart' version='2.5.2' url='https://urielch.github.io/'><l:description>Java reflect give poor performance on getter setter an constructor calls, accessors-smart use ASM to speed up those calls.</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>