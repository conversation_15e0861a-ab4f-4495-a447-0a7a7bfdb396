#!groovy

import jenkins.model.*
import hudson.security.*
import hudson.security.csrf.DefaultCrumbIssuer
import hudson.markup.RawHtmlMarkupFormatter
import hudson.model.*
import jenkins.security.s2m.AdminWhitelistRule

def instance = Jenkins.getInstance()

// Create admin user
def hudsonRealm = new HudsonPrivateSecurityRealm(false)
hudsonRealm.createAccount("admin", "admin123")
instance.setSecurityRealm(hudsonRealm)

// Set authorization strategy
def strategy = new FullControlOnceLoggedInAuthorizationStrategy()
strategy.setAllowAnonymousRead(false)
instance.setAuthorizationStrategy(strategy)

// Enable CSRF protection
instance.setCrumbIssuer(new DefaultCrumbIssuer(true))

// Set markup formatter
instance.setMarkupFormatter(new RawHtmlMarkupFormatter(false))

// Configure agent protocols
instance.getDescriptor("jenkins.CLI").get().setEnabled(false)
instance.getDescriptor("jenkins.security.s2m.MasterKillSwitchConfiguration").setMasterKillSwitch(false)

// Save configuration
instance.save()

println "<PERSON> initial configuration completed!"
println "Admin user created with username: admin, password: admin123"
println "Please change the default password after first login!"
