<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>json-path-api</artifactId>
  <version>2.9.0-148.v22a_7ffe323ce</version>
  <packaging>hpi</packaging>
  <name>JSON Path API Plugin</name>
  <description>The Jenkins Plugins Parent POM Project</description>
  <url>https://github.com/jenkinsci/json-path-api-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>Apache License 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <scm child.scm.connection.inherit.append.path="false" child.scm.developerConnection.inherit.append.path="false" child.scm.url.inherit.append.path="false">
    <connection>scm:git:https://github.com/jenkinsci/json-path-api-plugin</connection>
    <developerConnection>scm:git:https://github.com/jenkinsci/json-path-api-plugin</developerConnection>
    <tag>22a7ffe323ce9b079322befffa2b9e81e4d8dbc6</tag>
    <url>https://github.com/jenkinsci/json-path-api-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.jayway.jsonpath</groupId>
      <artifactId>json-path</artifactId>
      <version>2.9.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.ow2.asm</groupId>
          <artifactId>asm</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.ow2.asm</groupId>
          <artifactId>asm-analysis</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.ow2.asm</groupId>
          <artifactId>asm-commons</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.ow2.asm</groupId>
          <artifactId>asm-tree</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.ow2.asm</groupId>
          <artifactId>asm-utils</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>asm-api</artifactId>
      <version>9.7.1-97.v4cc844130d97</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.minidev</groupId>
      <artifactId>accessors-smart</artifactId>
      <version>2.5.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.minidev</groupId>
      <artifactId>json-smart</artifactId>
      <version>2.5.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.minidev</groupId>
      <artifactId>json-smart-action</artifactId>
      <version>2.5.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
