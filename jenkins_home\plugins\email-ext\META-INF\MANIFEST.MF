Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Email Extension Plugin
Specification-Version: 0.0
Implementation-Title: Email Extension Plugin
Implementation-Version: 1876.v28d8d38315b_d
Plugin-Class: hudson.plugins.emailext.EmailExtensionPlugin
Group-Id: org.jenkins-ci.plugins
Artifact-Id: email-ext
Short-Name: email-ext
Long-Name: Email Extension Plugin
Url: https://github.com/jenkinsci/email-ext-plugin
Compatible-Since-Version: 2.57.2
Plugin-Version: 1876.v28d8d38315b_d
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: jakarta-mail-api:2.1.3-1,workflow-job:1496.v5b_18de
 fc07f2;resolution:=optional,workflow-step-api:678.v3ee58b_469476;resolu
 tion:=optional,config-file-provider:980.v88956a_a_5d6a_d;resolution:=op
 tional,credentials:1405.vb_cda_74a_f8974,jackson2-api:2.17.0-379.v02de8
 ec9f64c,junit:1312.v1a_235a_b_94a_31,mailer:489.vd4b_25144138f,matrix-p
 roject:845.vffd7fa_f27555,scm-api:703.v72ff4b_259600,script-security:13
 69.v9b_98a_4e95b_2d,structs:338.v848422169819,token-macro:400.v35420b_9
 22dcb_
Plugin-Developers: 
Plugin-License-Name: The MIT License (MIT)
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/email-ext-plu
 gin.git
Plugin-ScmTag: 28d8d38315bd64c6f2be577af21b76009ea4eae9
Plugin-ScmUrl: https://github.com/jenkinsci/email-ext-plugin
Implementation-Build: 28d8d38315bd64c6f2be577af21b76009ea4eae9

