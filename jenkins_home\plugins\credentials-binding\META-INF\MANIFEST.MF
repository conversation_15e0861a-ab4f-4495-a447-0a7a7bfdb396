Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Credentials Binding Plugin
Specification-Version: 0.0
Implementation-Title: Credentials Binding Plugin
Implementation-Version: 687.v619cb_15e923f
Group-Id: org.jenkins-ci.plugins
Artifact-Id: credentials-binding
Short-Name: credentials-binding
Long-Name: Credentials Binding Plugin
Url: https://github.com/jenkinsci/credentials-binding-plugin
Plugin-Version: 687.v619cb_15e923f
Hudson-Version: 2.479
Jenkins-Version: 2.479
Plugin-Dependencies: workflow-step-api:678.v3ee58b_469476,credentials:13
 81.v2c3a_12074da_b_,plain-credentials:183.va_de8f1dd5a_2b_,ssh-credenti
 als:343.v884f71d78167,structs:338.v848422169819
Plugin-Developers: 
Plugin-License-Name: MIT
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/credentials-b
 inding-plugin
Plugin-ScmTag: 619cb15e923f30fce17888f0533091ac9e30e922
Plugin-ScmUrl: https://github.com/jenkinsci/credentials-binding-plugin
Implementation-Build: 619cb15e923f30fce17888f0533091ac9e30e922

