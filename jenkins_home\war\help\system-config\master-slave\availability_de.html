<div>
  <PERSON><PERSON><PERSON>, wann <PERSON> einen Agenten-Knoten startet und stoppt.
  <dl>
    <dt><b>Agent immer angeschaltet lassen</b></dt>
    <dd>
      Dies ist der Vorgabewert und die übliche Einstellung. In diesem Modus
      versucht Jenkins, den Agenten-Knoten online zu halten. <PERSON> Jenkins den
      Knoten ohne weiteres Zutun des Benutzers starten kann, wird <PERSON> in
      regelmäßigen Abständen versuchen, den Knoten neu zu starten, falls er
      nicht verfügbar ist. <PERSON> wird den Knoten nie offline schalten.
    </dd>

    <dt><b>Agent nur bei Bedarf anschalten, ansonsten abschalten</b></dt>
    <dd>
      Fall Jenkins den Knoten ohne weiteres Zutun des Benutzers starten kann,
      wird <PERSON> regel<PERSON>ßig versuchen, den Knoten neu zu starten, sobald
      geplante Jobs vorliegen, die folgende Kriterien erfüllen:
      <ul>
        <li>
          Die Jobs sind bereits länger als die angegebene Startzeit geplant.
        </li>
        <li>Die Jobs können auf diesem Knoten ausgeführt werden.</li>
      </ul>

      Der Knoten wird wieder offline geschaltet sobald...
      <ul>
        <li>keine aktiven Jobs auf dem Knoten ausgeführt werden und</li>
        <li>der Knoten länger als die Mindestruhezeit inaktiv war.</li>
      </ul>
    </dd>
  </dl>
</div>
