<div>
  <PERSON><PERSON> diese Option aktiviert ist, werden
  <a href="lastSuccessfulBuild/fingerprint">
    alle Builds, die von diesem Projekt referenziert werden (Ermittlung über
    Fingerabdrücke)
  </a>
  , von der Log-Rotation ausgeschlossen.

  <p>
    <PERSON><PERSON> <PERSON>hr Job von einem anderen Job innerhalb Jenkins' abhängt und Sie
    gelegentlich Ihren Arbeitsbereich markieren ("taggen"), ist es meist sehr
    praktisch oder sogar notwendig, dass Sie auch Ihre Abhängigkeiten innerhalb
    von Jenkins markieren. Das Problem dabei ist aber, dass durch die
    Log-Rotation eventuell ein Build, von dem Ihr Projekt abhängt, bereits
    verworfen wurde (etwa wenn in Ihren Abhängigkeiten in letzter Zeit sehr
    viele neue Builds stattfanden). <PERSON><PERSON> dies geschieht, sind Sie nicht mehr in
    der Lage, zuverlässig Ihre Abhängigkeiten zu markieren.
  </p>

  <p>
    Diese Funktion löst das Problem durch "Sperrung" der Builds, von denen Ihr
    Projekt abhängt. Dadurch wird garantiert, dass Sie immer Ihre kompletten
    Abhängigkeiten markieren können.
  </p>
</div>
