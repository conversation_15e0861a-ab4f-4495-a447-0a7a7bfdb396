<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="139"
   height="34"
   id="svg2"
   sodipodi:version="0.32"
   inkscape:version="0.48.3.1 r9886"
   sodipodi:docname="title.svg"
   version="1.0"
   inkscape:export-filename="/files/kohsuke/ws/jenkins/jenkins/war/src/main/webapp/images/title.png"
   inkscape:export-xdpi="180"
   inkscape:export-ydpi="180">
  <defs
     id="defs4">
    <linearGradient
       id="linearGradient3054">
      <stop
         style="stop-color:#729fcf;stop-opacity:1;"
         offset="0"
         id="stop3056" />
      <stop
         id="stop3062"
         offset="1"
         style="stop-color:#3465a4;stop-opacity:1;" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3054"
       id="linearGradient3060"
       x1="156.411"
       y1="96.022438"
       x2="156.411"
       y2="2.9507828e-07"
       gradientUnits="userSpaceOnUse"
       gradientTransform="scale(0.341615,0.322916)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3054"
       id="linearGradient2990"
       gradientUnits="userSpaceOnUse"
       gradientTransform="scale(0.43167715,0.322916)"
       x1="156.411"
       y1="96.022438"
       x2="156.411"
       y2="2.9507828e-07" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="14.277311"
     inkscape:cx="80.942949"
     inkscape:cy="18.557032"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="true"
     inkscape:window-width="1864"
     inkscape:window-height="1000"
     inkscape:window-x="639"
     inkscape:window-y="177"
     inkscape:grid-points="true"
     inkscape:grid-bbox="true"
     inkscape:guide-points="true"
     inkscape:window-maximized="0" />
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(0,-1)">
    <text
       xml:space="preserve"
       style="font-size:28.5498848px;font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;text-align:start;line-height:100%;writing-mode:lr-tb;text-anchor:start;fill:#ffffff;fill-opacity:1;stroke:none;font-family:Georgia"
       x="7.7234173"
       y="26.604568"
       id="text2184"
       sodipodi:linespacing="100%"
       transform="scale(1.0049742,0.99505038)"
       inkscape:export-xdpi="90"
       inkscape:export-ydpi="90"><tspan
         sodipodi:role="line"
         id="tspan2186"
         x="7.7234173"
         y="26.604568">Jenkins</tspan></text>
  </g>
</svg>
