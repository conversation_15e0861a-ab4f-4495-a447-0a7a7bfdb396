<div>
  デフォルトで、Jenkinsは構成軸のすべての組み合わせをビルドします。しかし、場合によってはこれでは組み合わせが多すぎるか、
  望ましくない組み合わせができてしまうことがあります。
  そのような場合、真偽値を返すGroovyの式で必要のない組み合わせをフィルタリングする事で、構成マトリックスをまばらにすることができます。

  <p>
  Groovyの式をここで指定すると、式の結果が<b>true</b>になる組み合わせのみビルドされます。
  式の評価では、軸は変数として扱われます(評価中の組み合わせに値をセットします)。

  <h4>値によるフィルタリング</h4>
  <p>
  例えば、異なるOS上で複数のコンパイラを使用してビルドすることを考えてみましょう。
  スレーブのラベルは<b>label=[linux,solaris]</b>とし、<b>compiler=[gcc,cc]</b>という構成軸を作成したとします。

  次の式は、どれも<b>linux</b>上での<b>cc</b>によるビルドを除去します。
  この制約をどう考えるかにもよって、どの式が望ましいと思うかは個人差があるでしょう。

  <center>
    <table>
        <tr>
            <td>"linuxとccの場合は無効"</td>
            <td>
                <pre>!(label=="linux" && compiler=="cc")</pre>
            </td>
        </tr>
        <tr>
            <td>"solarisかgccの場合は有効"</td>
            <td>
                <pre>label=="solaris" || compiler=="gcc"</pre>
            </td>
        </tr>
        <tr>
            <td>"solarisであればccのみ使用する"</td>
            <td>
                <pre>(label=="solaris").implies(compiler=="cc")</pre>
            </td>
        </tr>
    </table>
  </center>
  
  <h4>まばらな行列を作る</h4>
  <p>
  特定の値の組み合わせの除去に加えて、予約された<tt>index</tt>変数を使って行列を一様にまばらにすることができます。
  
  <p>
  例えば、<tt>index%2==0</tt>とすると、２つに１つの組み合わせを除去することで、ビルドする組み合わせのサイズを半分に
  することができます。同様に、<tt>index%3!=0</tt>とすると、３つに１つの組み合わせが除去されサイズは66%になります。
  indexの値は、剰余計算においてビルドが特定の値に偏らないように計算されます。  
</div>
