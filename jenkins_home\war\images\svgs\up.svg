<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   sodipodi:docname="go-up.svg"
   sodipodi:docbase="/home/<USER>/cvs/freedesktop.org/tango-icon-theme/scalable/actions"
   inkscape:version="0.43+devel"
   sodipodi:version="0.32"
   id="svg11300"
   height="48px"
   width="48px"
   inkscape:export-filename="/home/<USER>/Desktop/wi-fi.png"
   inkscape:export-xdpi="90.000000"
   inkscape:export-ydpi="90.000000"
   inkscape:output_extension="org.inkscape.output.svg.inkscape">
  <defs
     id="defs3">
    <linearGradient
       id="linearGradient2304">
      <stop
         id="stop2306"
         offset="0"
         style="stop-color:#73d216" />
      <stop
         id="stop2308"
         offset="1.0000000"
         style="stop-color:#4e9a06" />
    </linearGradient>
    <linearGradient
       id="linearGradient8662"
       inkscape:collect="always">
      <stop
         id="stop8664"
         offset="0"
         style="stop-color:#000000;stop-opacity:1;" />
      <stop
         id="stop8666"
         offset="1"
         style="stop-color:#000000;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       id="linearGradient8650"
       inkscape:collect="always">
      <stop
         id="stop8652"
         offset="0"
         style="stop-color:#ffffff;stop-opacity:1;" />
      <stop
         id="stop8654"
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0;" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient8650"
       id="radialGradient1438"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-3.749427e-16,-2.046729,1.557610,-2.853404e-16,2.767009,66.93275)"
       cx="24.53788"
       cy="0.40010813"
       fx="24.53788"
       fy="0.40010813"
       r="17.171415" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2304"
       id="radialGradient1441"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.871885e-16,-0.843022,1.020168,2.265228e-16,0.606436,42.58614)"
       cx="11.319205"
       cy="22.454971"
       fx="11.319205"
       fy="22.454971"
       r="16.956199" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient8662"
       id="radialGradient1444"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.536723,1.614716e-15,16.87306)"
       cx="24.837126"
       cy="36.421127"
       fx="24.837126"
       fy="36.421127"
       r="15.644737" />
  </defs>
  <sodipodi:namedview
     inkscape:window-y="25"
     inkscape:window-x="0"
     inkscape:window-height="885"
     inkscape:window-width="1280"
     inkscape:showpageshadow="false"
     inkscape:document-units="px"
     inkscape:grid-bbox="true"
     showgrid="false"
     inkscape:current-layer="layer1"
     inkscape:cy="25.620377"
     inkscape:cx="22.042915"
     inkscape:zoom="13.059378"
     inkscape:pageshadow="2"
     inkscape:pageopacity="0.0"
     borderopacity="0.25490196"
     bordercolor="#666666"
     pagecolor="#ffffff"
     id="base"
     fill="#73d216"
     stroke="#73d216" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://jimmac.musichall.cz</dc:source>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:title>Go Up</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>go</rdf:li>
            <rdf:li>higher</rdf:li>
            <rdf:li>up</rdf:li>
            <rdf:li>arrow</rdf:li>
            <rdf:li>pointer</rdf:li>
            <rdf:li>&gt;</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:contributor>
          <cc:Agent>
            <dc:title>Andreas Nilsson</dc:title>
          </cc:Agent>
        </dc:contributor>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:groupmode="layer"
     inkscape:label="Layer 1"
     id="layer1">
    <path
       transform="matrix(1.214466,0.000000,0.000000,0.595458,-6.163846,16.31275)"
       d="M 40.481863 36.421127 A 15.644737 8.3968935 0 1 1  9.1923885,36.421127 A 15.644737 8.3968935 0 1 1  40.481863 36.421127 z"
       sodipodi:ry="8.3968935"
       sodipodi:rx="15.644737"
       sodipodi:cy="36.421127"
       sodipodi:cx="24.837126"
       id="path8660"
       style="opacity:0.29946521;color:#000000;fill:url(#radialGradient1444);fill-opacity:1.0000000;fill-rule:evenodd;stroke:none;stroke-width:1.0000000;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible"
       sodipodi:type="arc" />
    <path
       sodipodi:nodetypes="cccccccc"
       id="path8643"
       d="M 14.491792,38.500000 L 32.469477,38.500000 L 32.469477,25.547437 L 40.500000,25.547437 L 23.374809,5.4992135 L 6.5285585,25.489471 L 14.497096,25.555762 L 14.491792,38.500000 z "
       style="opacity:1.0000000;color:#000000;fill:url(#radialGradient1441);fill-opacity:1.0000000;fill-rule:evenodd;stroke:#3a7304;stroke-width:1.0000004;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1;visibility:visible;display:inline;overflow:visible" />
    <path
       sodipodi:nodetypes="cccscc"
       id="path8645"
       d="M 7.5855237,25.03253 L 14.995821,25.03253 L 15.062422,31.594339 C 20.718034,20.593878 31.055517,22.749928 31.656768,15.966674 C 31.656768,15.966674 23.366938,6.4219692 23.366938,6.4219692 L 7.5855237,25.03253 z "
       style="opacity:0.50802141;color:#000000;fill:url(#radialGradient1438);fill-opacity:1.0000000;fill-rule:evenodd;stroke:none;stroke-width:1.0000000;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible" />
    <path
       style="opacity:0.48128340;color:#000000;fill:none;fill-opacity:1.0000000;fill-rule:evenodd;stroke:#ffffff;stroke-width:1.0000004;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10.000000;stroke-dasharray:none;stroke-dashoffset:0.0000000;stroke-opacity:1.0000000;visibility:visible;display:inline;overflow:visible"
       d="M 15.602735,37.500000 L 31.502578,37.500000 L 31.502578,24.507050 L 38.311576,24.507050 L 23.361206,7.0700896 L 8.6546798,24.550470 L 15.475049,24.528373 L 15.602735,37.500000 z "
       id="path8658"
       sodipodi:nodetypes="cccccccc" />
  </g>
</svg>
