<div>
  Wird ein Agent über JNLP gestartet, versucht dieser über einen bestimmten
  TCP-<PERSON> Jenkins-Master zu erreichen. In abgesicherten Netzwerken kann
  dieser Verbindungsaufbau jedoch scheitern. Probleme können auch dadurch
  entstehen, wenn <PERSON> hinter einem Lastverteiler (load balancer),
  <a
    href="https://www.jenkins.io/doc/book/system-administration/reverse-proxy-configuration-apache/"
  >
    einem Apache Reverse-Proxy
  </a>
  in eine
  <a href="https://en.wikipedia.org/wiki/Demilitarized_zone_(computing)">DMZ</a>
  o.ä. betrieben wird.

  <p>
    Mit dieser Tunnel-Option können Sie in diesen Situationen die Verbindung
    über einen anderen Rechner/Port leiten. Das Feld nimmt Eingaben in den
    Formaten "
    <code>HOST:PORT</code>
    ", "
    <code>:PORT</code>
    " oder "
    <code>HOST:</code>
    " entgegen.
  </p>

  <p>
    Beim ersten Format versucht der JNLP-Agent über den angegebenen Port der
    angegebenen Rechner zu erreichen und setzt voraus, dass Sie Ihr Netzwerk so
    konfiguriert haben, dass diese Anfrage auf den JNLP-Agenten-TCP-Port des
    Jenkins Masters weitergeleitet wird.
  </p>

  <p>
    Bei den beiden anderen Formaten werden fehlende Werte durch den
    Standardrechner (der Rechner, auf dem Jenkins läuft) bzw.
    Standard-JNLP-TCP-Port ergänzt. Insbesondere das
    <code>HOST:</code>
    -Format ist sehr praktisch, wenn ein HTTP-Reverse-Proxy verwendet wird und
    Jenkins auf einem anderen Rechner läuft.
  </p>
</div>
