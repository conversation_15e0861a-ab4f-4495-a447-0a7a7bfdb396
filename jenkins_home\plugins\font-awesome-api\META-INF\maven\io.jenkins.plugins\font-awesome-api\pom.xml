<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>font-awesome-api</artifactId>
  <version>6.7.2-1</version>
  <packaging>hpi</packaging>
  <name>Font Awesome API Plugin</name>
  <description>Provides the free fonts of Font Awesome for Jenkins plugins.</description>
  <url>https://github.com/jenkinsci/font-awesome-api-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT license</name>
      <comments>All source code is under the MIT license.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>uhafner</id>
      <name><PERSON><PERSON><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/font-awesome-api-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/font-awesome-api-plugin.git</developerConnection>
    <tag>v6.7.2-1</tag>
    <url>https://github.com/jenkinsci/font-awesome-api-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>plugin-util-api</artifactId>
      <version>6.0.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
