Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Jakarta Activation API
Specification-Version: 2.1
Implementation-Title: Jakarta Activation API
Implementation-Version: 2.1.3-2
Group-Id: io.jenkins.plugins
Artifact-Id: jakarta-activation-api
Short-Name: jakarta-activation-api
Long-Name: Jakarta Activation API
Url: https://github.com/jenkinsci/jakarta-activation-api-plugin
Plugin-Version: 2.1.3-2
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Developers: 
Support-Dynamic-Loading: true
Plugin-License-Name: BSD-3-Clause
Plugin-License-Url: https://opensource.org/licenses/BSD-3-Clause
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/jakarta-activ
 ation-api-plugin.git
Plugin-ScmTag: jakarta-activation-api-2.1.3-2
Plugin-ScmUrl: https://github.com/jenkinsci/jakarta-activation-api-plugi
 n
Implementation-Build: 109b2a13b0831721c348540b9cfba459becbc1ee

