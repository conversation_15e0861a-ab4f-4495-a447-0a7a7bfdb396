<div>
  Define a pattern (regular expression) to check whether or not the job name is
  valid.
  <p>
    Forcing the check on existing jobs, will allow you to enforce a naming
    convention on existing jobs - e.g. even if the user does not change the
    name, it will be validated with the given pattern at every submit and no
    updates can be made until the name conforms.
    <br />
    <i>
      This option does not affect the execution of jobs with non-compliant
      names. It just controls the validation process when saving job
      configurations.
    </i>
  </p>
</div>
