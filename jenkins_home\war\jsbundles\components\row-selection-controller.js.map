{"version": 3, "file": "components/row-selection-controller.js", "mappings": ";AAAA,MAAMA,uBAAuB,GAAGC,QAAQ,CAACC,gBAAgB,CACvD,0BACF,CAAC;AAEDF,uBAAuB,CAACG,OAAO,CAAEC,cAAc,IAAK;EAClD,MAAMC,KAAK,GAAGD,cAAc,CAACE,OAAO,CAAC,gBAAgB,CAAC;EACtD,MAAMC,aAAa,GAAGH,cAAc,CAACI,OAAO,CAACD,aAAa;EAC1D,MAAME,eAAe,GAAGJ,KAAK,CAACH,gBAAgB,CAC5C,0BAA0BK,aAAa,EACzC,CAAC;EACD,MAAMG,iBAAiB,GAAGL,KAAK,CAACM,aAAa,CAC3C,kCACF,CAAC;EACD,MAAMC,mBAAmB,GAAGP,KAAK,CAACM,aAAa,CAC7C,mCACF,CAAC;EACD,MAAME,oBAAoB,GAAGR,KAAK,CAACM,aAAa,CAAC,qBAAqB,CAAC;EACvE,MAAMG,qBAAqB,GAAGT,KAAK,CAACM,aAAa,CAAC,sBAAsB,CAAC;EAEzE,IAAIF,eAAe,CAACM,MAAM,KAAK,CAAC,EAAE;IAChCX,cAAc,CAACY,QAAQ,GAAG,IAAI;IAC9B,IAAIN,iBAAiB,EAAE;MACrBA,iBAAiB,CAACM,QAAQ,GAAG,IAAI;IACnC;EACF;EAEA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,kBAAkB,GAAGC,KAAK,CAACC,IAAI,CAACX,eAAe,CAAC,CAACY,MAAM,CAC1DC,CAAC,IAAKA,CAAC,CAACC,OACX,CAAC;IACD,OAAOd,eAAe,CAACM,MAAM,KAAKG,kBAAkB,CAACH,MAAM;EAC7D,CAAC;EAED,MAAMS,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMN,kBAAkB,GAAGC,KAAK,CAACC,IAAI,CAACX,eAAe,CAAC,CAACY,MAAM,CAC1DC,CAAC,IAAKA,CAAC,CAACC,OACX,CAAC;IACD,OAAOL,kBAAkB,CAACH,MAAM,GAAG,CAAC;EACtC,CAAC;EAEDN,eAAe,CAACN,OAAO,CAAEsB,QAAQ,IAAK;IACpCA,QAAQ,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACxCC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvB,cAAc,CAACsB,gBAAgB,CAAC,OAAO,EAAE,MAAM;IAC7C,MAAME,QAAQ,GAAG,CAACX,qBAAqB,CAAC,CAAC;IACzCR,eAAe,CAACN,OAAO,CAAEmB,CAAC,IAAMA,CAAC,CAACC,OAAO,GAAGK,QAAS,CAAC;IACtDD,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EAEF,IAAId,oBAAoB,KAAK,IAAI,EAAE;IACjCA,oBAAoB,CAACa,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACnDjB,eAAe,CAACN,OAAO,CAAEmB,CAAC,IAAMA,CAAC,CAACC,OAAO,GAAG,IAAK,CAAC;MAClDI,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ;EAEA,IAAIb,qBAAqB,KAAK,IAAI,EAAE;IAClCA,qBAAqB,CAACY,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACpDjB,eAAe,CAACN,OAAO,CAAEmB,CAAC,IAAMA,CAAC,CAACC,OAAO,GAAG,KAAM,CAAC;MACnDI,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ;EAEA,SAASA,UAAUA,CAAA,EAAG;IACpBvB,cAAc,CAACyB,SAAS,CAACC,MAAM,CAAC,8BAA8B,CAAC;IAC/D1B,cAAc,CAACyB,SAAS,CAACC,MAAM,CAAC,wCAAwC,CAAC;IACzE,IAAIlB,mBAAmB,KAAK,IAAI,EAAE;MAChCA,mBAAmB,CAACiB,SAAS,CAACC,MAAM,CAClC,2CACF,CAAC;IACH;IAEA,IAAIb,qBAAqB,CAAC,CAAC,EAAE;MAC3Bb,cAAc,CAACyB,SAAS,CAACE,GAAG,CAAC,8BAA8B,CAAC;MAC5D;IACF;IAEA,IAAIP,qBAAqB,CAAC,CAAC,EAAE;MAC3BpB,cAAc,CAACyB,SAAS,CAACE,GAAG,CAAC,wCAAwC,CAAC;IACxE;EACF;EAEA9B,QAAQ,CAACyB,gBAAgB,CAAC,OAAO,EAAGM,KAAK,IAAK;IAC5C,IAAIpB,mBAAmB,KAAK,IAAI,EAAE;MAChC,IACEA,mBAAmB,CAACqB,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,IAC1CF,KAAK,CAACE,MAAM,KAAKxB,iBAAiB,EAClC;QACA;MACF;MACAE,mBAAmB,CAACiB,SAAS,CAACC,MAAM,CAClC,2CACF,CAAC;IACH;EACF,CAAC,CAAC;EAEF,IAAIpB,iBAAiB,KAAK,IAAI,EAAE;IAC9BA,iBAAiB,CAACgB,gBAAgB,CAAC,OAAO,EAAE,MAAM;MAChDd,mBAAmB,CAACiB,SAAS,CAACM,MAAM,CAClC,2CACF,CAAC;IACH,CAAC,CAAC;EACJ;EAEAC,MAAM,CAACC,yBAAyB,GAAGV,UAAU;AAC/C,CAAC,CAAC,C", "sources": ["webpack://jenkins-ui/./src/main/js/components/row-selection-controller/index.js"], "sourcesContent": ["const rowSelectionControllers = document.querySelectorAll(\n  \".jenkins-table__checkbox\",\n);\n\nrowSelectionControllers.forEach((headerCheckbox) => {\n  const table = headerCheckbox.closest(\".jenkins-table\");\n  const checkboxClass = headerCheckbox.dataset.checkboxClass;\n  const tableCheckboxes = table.querySelectorAll(\n    `input[type='checkbox'].${checkboxClass}`,\n  );\n  const moreOptionsButton = table.querySelector(\n    \".jenkins-table__checkbox-options\",\n  );\n  const moreOptionsDropdown = table.querySelector(\n    \".jenkins-table__checkbox-dropdown\",\n  );\n  const moreOptionsAllButton = table.querySelector(\"[data-select='all']\");\n  const moreOptionsNoneButton = table.querySelector(\"[data-select='none']\");\n\n  if (tableCheckboxes.length === 0) {\n    headerCheckbox.disabled = true;\n    if (moreOptionsButton) {\n      moreOptionsButton.disabled = true;\n    }\n  }\n\n  const allCheckboxesSelected = () => {\n    const selectedCheckboxes = Array.from(tableCheckboxes).filter(\n      (e) => e.checked,\n    );\n    return tableCheckboxes.length === selectedCheckboxes.length;\n  };\n\n  const anyCheckboxesSelected = () => {\n    const selectedCheckboxes = Array.from(tableCheckboxes).filter(\n      (e) => e.checked,\n    );\n    return selectedCheckboxes.length > 0;\n  };\n\n  tableCheckboxes.forEach((checkbox) => {\n    checkbox.addEventListener(\"change\", () => {\n      updateIcon();\n    });\n  });\n\n  headerCheckbox.addEventListener(\"click\", () => {\n    const newValue = !allCheckboxesSelected();\n    tableCheckboxes.forEach((e) => (e.checked = newValue));\n    updateIcon();\n  });\n\n  if (moreOptionsAllButton !== null) {\n    moreOptionsAllButton.addEventListener(\"click\", () => {\n      tableCheckboxes.forEach((e) => (e.checked = true));\n      updateIcon();\n    });\n  }\n\n  if (moreOptionsNoneButton !== null) {\n    moreOptionsNoneButton.addEventListener(\"click\", () => {\n      tableCheckboxes.forEach((e) => (e.checked = false));\n      updateIcon();\n    });\n  }\n\n  function updateIcon() {\n    headerCheckbox.classList.remove(\"jenkins-table__checkbox--all\");\n    headerCheckbox.classList.remove(\"jenkins-table__checkbox--indeterminate\");\n    if (moreOptionsDropdown !== null) {\n      moreOptionsDropdown.classList.remove(\n        \"jenkins-table__checkbox-dropdown--visible\",\n      );\n    }\n\n    if (allCheckboxesSelected()) {\n      headerCheckbox.classList.add(\"jenkins-table__checkbox--all\");\n      return;\n    }\n\n    if (anyCheckboxesSelected()) {\n      headerCheckbox.classList.add(\"jenkins-table__checkbox--indeterminate\");\n    }\n  }\n\n  document.addEventListener(\"click\", (event) => {\n    if (moreOptionsDropdown !== null) {\n      if (\n        moreOptionsDropdown.contains(event.target) ||\n        event.target === moreOptionsButton\n      ) {\n        return;\n      }\n      moreOptionsDropdown.classList.remove(\n        \"jenkins-table__checkbox-dropdown--visible\",\n      );\n    }\n  });\n\n  if (moreOptionsButton !== null) {\n    moreOptionsButton.addEventListener(\"click\", () => {\n      moreOptionsDropdown.classList.toggle(\n        \"jenkins-table__checkbox-dropdown--visible\",\n      );\n    });\n  }\n\n  window.updateTableHeaderCheckbox = updateIcon;\n});\n"], "names": ["rowSelectionControllers", "document", "querySelectorAll", "for<PERSON>ach", "headerCheckbox", "table", "closest", "checkboxClass", "dataset", "tableCheckboxes", "moreOptionsButton", "querySelector", "moreOptionsDropdown", "moreOptionsAllButton", "moreOptionsNoneButton", "length", "disabled", "allCheckboxesSelected", "selectedCheckboxes", "Array", "from", "filter", "e", "checked", "anyCheckboxesSelected", "checkbox", "addEventListener", "updateIcon", "newValue", "classList", "remove", "add", "event", "contains", "target", "toggle", "window", "updateTableHeaderCheckbox"], "sourceRoot": ""}