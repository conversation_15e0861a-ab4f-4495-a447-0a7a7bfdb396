{"version": 3, "file": "pages/register.js", "mappings": ";;;;AAAO,SAASA,OAAOA,CAACC,IAAI,EAAE;EAC5B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAC5C,OAAOF,IAAI,CAACG,YAAY,CAAC,OAAO,GAAGJ,IAAI,CAAC;AAC1C;;ACHsC;AAEtC,MAAMK,aAAa,GAAGH,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;AAC1D,MAAMG,cAAc,GAAGJ,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;AAC3D,MAAMI,iBAAiB,GAAGL,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC;AACjE,MAAMK,uBAAuB,GAAGN,QAAQ,CAACC,aAAa,CACpD,0BACF,CAAC;AACD,MAAMM,yBAAyB,GAAGP,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;AAE7EO,sBAAsB,CAAC,CAAC;AAExBL,aAAa,CAACM,gBAAgB,CAAC,OAAO,EAAED,sBAAsB,CAAC;AAE/D,SAASA,sBAAsBA,CAAA,EAAG;EAChC,IAAIL,aAAa,CAACO,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpCL,uBAAuB,CAACM,MAAM,GAAG,IAAI;IACrC;EACF;EAEAN,uBAAuB,CAACM,MAAM,GAAG,KAAK;EACtC,MAAMC,KAAK,GAAGC,aAAa,CAACX,aAAa,CAACO,KAAK,CAAC;EAChDH,yBAAyB,CAACQ,SAAS,GAAGC,gBAAgB,CAACH,KAAK,CAAC;EAC7DN,yBAAyB,CAACU,KAAK,CAACC,KAAK,GAAGC,qBAAqB,CAACN,KAAK,CAAC;EACpET,cAAc,CAACM,KAAK,GAAGP,aAAa,CAACO,KAAK;AAC5C;;AAEA;AACAL,iBAAiB,CAACI,gBAAgB,CAAC,QAAQ,EAAE,MAAM;EACjD,IAAIJ,iBAAiB,CAACe,OAAO,EAAE;IAC7BjB,aAAa,CAACkB,IAAI,GAAG,MAAM;EAC7B,CAAC,MAAM;IACLlB,aAAa,CAACkB,IAAI,GAAG,UAAU;EACjC;AACF,CAAC,CAAC;AAEF,SAASP,aAAaA,CAACQ,QAAQ,EAAE;EAC/B,IAAIT,KAAK,GAAG,CAAC;EAEb,IAAI,CAACS,QAAQ,EAAE;IACb,OAAOT,KAAK;EACd;;EAEA;EACA,MAAMU,OAAO,GAAG,CAAC,CAAC;EAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACX,MAAM,EAAEa,CAAC,EAAE,EAAE;IACxCD,OAAO,CAACD,QAAQ,CAACE,CAAC,CAAC,CAAC,GAAG,CAACD,OAAO,CAACD,QAAQ,CAACE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;IACtDX,KAAK,IAAI,GAAG,GAAGU,OAAO,CAACD,QAAQ,CAACE,CAAC,CAAC,CAAC;EACrC;;EAEA;EACA,MAAMC,UAAU,GAAG;IACjBC,MAAM,EAAE,IAAI,CAACC,IAAI,CAACL,QAAQ,CAAC;IAC3BM,KAAK,EAAE,OAAO,CAACD,IAAI,CAACL,QAAQ,CAAC;IAC7BO,KAAK,EAAE,OAAO,CAACF,IAAI,CAACL,QAAQ,CAAC;IAC7BQ,QAAQ,EAAE,IAAI,CAACH,IAAI,CAACL,QAAQ;EAC9B,CAAC;EAED,IAAIS,cAAc,GAAG,CAAC;EACtB,KAAK,MAAMC,KAAK,IAAIP,UAAU,EAAE;IAC9BM,cAAc,IAAIN,UAAU,CAACO,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;EACtD;EACAnB,KAAK,IAAI,CAACkB,cAAc,GAAG,CAAC,IAAI,EAAE;EAElC,OAAOlB,KAAK;AACd;AAEA,SAASG,gBAAgBA,CAACH,KAAK,EAAE;EAC/B,IAAIA,KAAK,GAAG,EAAE,EAAE;IACd,OAAOhB,OAAO,CAAC,iBAAiB,CAAC;EACnC;EACA,IAAIgB,KAAK,GAAG,EAAE,EAAE;IACd,OAAOhB,OAAO,CAAC,mBAAmB,CAAC;EACrC;EACA,IAAIgB,KAAK,IAAI,EAAE,EAAE;IACf,OAAOhB,OAAO,CAAC,eAAe,CAAC;EACjC;EACA,OAAOA,OAAO,CAAC,eAAe,CAAC;AACjC;AAEA,SAASsB,qBAAqBA,CAACN,KAAK,EAAE;EACpC,IAAIA,KAAK,GAAG,EAAE,EAAE;IACd,OAAO,cAAc;EACvB;EACA,IAAIA,KAAK,GAAG,EAAE,EAAE;IACd,OAAO,eAAe;EACxB;EACA,IAAIA,KAAK,IAAI,EAAE,EAAE;IACf,OAAO,eAAe;EACxB;EACA,OAAO,oBAAoB;AAC7B,C", "sources": ["webpack://jenkins-ui/./src/main/js/util/i18n.js", "webpack://jenkins-ui/./src/main/js/pages/register/index.js"], "sourcesContent": ["export function getI18n(text) {\n  const i18n = document.querySelector(\"#i18n\");\n  return i18n.getAttribute(\"data-\" + text);\n}\n", "import { getI18n } from \"@/util/i18n\";\n\nconst passwordField = document.querySelector(\"#password1\");\nconst password2Field = document.querySelector(\"#password2\");\nconst showPasswordField = document.querySelector(\"#showPassword\");\nconst passwordStrengthWrapper = document.querySelector(\n  \"#passwordStrengthWrapper\",\n);\nconst passwordStrengthIndicator = document.querySelector(\"#passwordStrength\");\n\nupdatePasswordStrength();\n\npasswordField.addEventListener(\"input\", updatePasswordStrength);\n\nfunction updatePasswordStrength() {\n  if (passwordField.value.length === 0) {\n    passwordStrengthWrapper.hidden = true;\n    return;\n  }\n\n  passwordStrengthWrapper.hidden = false;\n  const score = passwordScore(passwordField.value);\n  passwordStrengthIndicator.innerText = passwordStrength(score);\n  passwordStrengthIndicator.style.color = passwordStrengthColor(score);\n  password2Field.value = passwordField.value;\n}\n\n// Toggle password visibility\nshowPasswordField.addEventListener(\"change\", () => {\n  if (showPasswordField.checked) {\n    passwordField.type = \"text\";\n  } else {\n    passwordField.type = \"password\";\n  }\n});\n\nfunction passwordScore(password) {\n  let score = 0;\n\n  if (!password) {\n    return score;\n  }\n\n  // Award every unique letter until 5 repetitions\n  const letters = {};\n\n  for (let i = 0; i < password.length; i++) {\n    letters[password[i]] = (letters[password[i]] || 0) + 1;\n    score += 5.0 / letters[password[i]];\n  }\n\n  // Bonus points for mixing it up\n  const variations = {\n    digits: /\\d/.test(password),\n    lower: /[a-z]/.test(password),\n    upper: /[A-Z]/.test(password),\n    nonWords: /\\W/.test(password),\n  };\n\n  let variationCount = 0;\n  for (const check in variations) {\n    variationCount += variations[check] === true ? 1 : 0;\n  }\n  score += (variationCount - 1) * 10;\n\n  return score;\n}\n\nfunction passwordStrength(score) {\n  if (score > 80) {\n    return getI18n(\"strength-strong\");\n  }\n  if (score > 60) {\n    return getI18n(\"strength-moderate\");\n  }\n  if (score >= 30) {\n    return getI18n(\"strength-weak\");\n  }\n  return getI18n(\"strength-poor\");\n}\n\nfunction passwordStrengthColor(score) {\n  if (score > 80) {\n    return \"var(--green)\";\n  }\n  if (score > 60) {\n    return \"var(--yellow)\";\n  }\n  if (score >= 30) {\n    return \"var(--orange)\";\n  }\n  return \"var(--error-color)\";\n}\n"], "names": ["getI18n", "text", "i18n", "document", "querySelector", "getAttribute", "passwordField", "password2Field", "showPasswordField", "passwordStrengthWrapper", "passwordStrengthIndicator", "updatePasswordStrength", "addEventListener", "value", "length", "hidden", "score", "passwordScore", "innerText", "passwordStrength", "style", "color", "passwordStrengthColor", "checked", "type", "password", "letters", "i", "variations", "digits", "test", "lower", "upper", "nonWords", "variationCount", "check"], "sourceRoot": ""}