# Test Database configuration - Using H2 in-memory database for tests
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# JPA/Hibernate settings for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# App Properties
bezkoder.app.jwtSecret=testSecretKey12345678901234567890123456789012345678901234567890
bezkoder.app.jwtExpirationMs=3600000

# Disable Swagger UI for tests
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false 