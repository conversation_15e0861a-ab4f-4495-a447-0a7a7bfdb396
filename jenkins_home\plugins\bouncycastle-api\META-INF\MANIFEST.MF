Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: bouncycastle API Plugin
Specification-Version: 0.0
Implementation-Title: bouncycastle API Plugin
Implementation-Version: 2.30.1.80-261.v00c0e2618ec3
Plugin-Class: jenkins.bouncycastle.api.BouncyCastlePlugin
Group-Id: org.jenkins-ci.plugins
Artifact-Id: bouncycastle-api
Short-Name: bouncycastle-api
Long-Name: bouncycastle API Plugin
Url: https://github.com/jenkinsci/bouncycastle-api-plugin
Plugin-Version: 2.30.1.80-261.v00c0e2618ec3
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Developers: <PERSON><PERSON>:alvarolobato:<EMAIL>
Support-Dynamic-Loading: true
Plugin-License-Name: The MIT license
Plugin-License-Url: https://www.opensource.org/licenses/mit-license.php
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/bouncycastle-
 api-plugin.git
Plugin-ScmTag: 00c0e2618ec35f208968eeeb25cdc5debe9e652a
Plugin-ScmUrl: https://github.com/jenkinsci/bouncycastle-api-plugin
Implementation-Build: 00c0e2618ec35f208968eeeb25cdc5debe9e652a

