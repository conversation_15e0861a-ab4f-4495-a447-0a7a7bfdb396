{"version": 3, "file": "section-to-tabs.js", "mappings": ";AAAA;AACA,MAAMA,QAAQ,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,mBAAmB,CAAC;AAC/D,MAAMC,OAAO,GAAGF,QAAQ,CAACG,aAAa,CAAC,aAAa,CAAC;;AAErD;AACAJ,QAAQ,CAACK,OAAO,CAAEC,OAAO,IAAK;EAC5BA,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;AAChC,CAAC,CAAC;;AAEF;AACAR,QAAQ,CAAC,CAAC,CAAC,CAACO,KAAK,CAACC,OAAO,GAAG,OAAO;AAEnC,MAAMC,MAAM,GAAGR,QAAQ,CAACS,aAAa,CAAC,KAAK,CAAC;AAC5CD,MAAM,CAACE,SAAS,GAAG,QAAQ;;AAE3B;AACAX,QAAQ,CAACK,OAAO,CAAC,CAACC,OAAO,EAAEM,KAAK,KAAK;EACnC,MAAMC,YAAY,GAAGP,OAAO,CAACF,aAAa,CAAC,0BAA0B,CAAC;EACtES,YAAY,CAACN,KAAK,CAACC,OAAO,GAAG,MAAM;EAEnC,MAAMM,GAAG,GAAGb,QAAQ,CAACS,aAAa,CAAC,KAAK,CAAC;EACzCI,GAAG,CAACH,SAAS,GAAG,KAAK;EAErB,IAAIC,KAAK,KAAK,CAAC,EAAE;IACfE,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;EAC7B;EAEAF,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBlB,QAAQ,CAACC,gBAAgB,CAAC,MAAM,CAAC,CAACG,OAAO,CAAES,GAAG,IAAK;MACjDA,GAAG,CAACC,SAAS,CAACK,MAAM,CAAC,QAAQ,CAAC;IAChC,CAAC,CAAC;IACFN,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IAE3BhB,QAAQ,CAACK,OAAO,CAAEC,OAAO,IAAK;MAC5BA,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IAChC,CAAC,CAAC;IACFR,QAAQ,CAACY,KAAK,CAAC,CAACL,KAAK,CAACC,OAAO,GAAG,OAAO;EACzC,CAAC,CAAC;EAEF,MAAMa,OAAO,GAAGpB,QAAQ,CAACS,aAAa,CAAC,GAAG,CAAC;EAC3CW,OAAO,CAACC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;EACjCD,OAAO,CAACE,SAAS,GAAGV,YAAY,CAACW,WAAW;EAE5CV,GAAG,CAACW,MAAM,CAACJ,OAAO,CAAC;EAEnBZ,MAAM,CAACgB,MAAM,CAACX,GAAG,CAAC;AACpB,CAAC,CAAC;AAEFX,OAAO,CAACuB,YAAY,CAACjB,MAAM,EAAET,QAAQ,CAAC,CAAC,CAAC,CAAC,C", "sources": ["webpack://jenkins-ui/./src/main/js/section-to-tabs.js"], "sourcesContent": ["// Converts a page's section headings into clickable tabs, see 'About Jenkins' page for example\nconst tabPanes = document.querySelectorAll(\".jenkins-tab-pane\");\nconst content = document.querySelector(\"#main-panel\");\n\n// Hide tab panes\ntabPanes.forEach((tabPane) => {\n  tabPane.style.display = \"none\";\n});\n\n// Show the first\ntabPanes[0].style.display = \"block\";\n\nconst tabBar = document.createElement(\"div\");\ntabBar.className = \"tabBar\";\n\n// Add tabs for each tab pane\ntabPanes.forEach((tabPane, index) => {\n  const tabPaneTitle = tabPane.querySelector(\".jenkins-tab-pane__title\");\n  tabPaneTitle.style.display = \"none\";\n\n  const tab = document.createElement(\"div\");\n  tab.className = \"tab\";\n\n  if (index === 0) {\n    tab.classList.add(\"active\");\n  }\n\n  tab.addEventListener(\"click\", function (e) {\n    e.preventDefault();\n    document.querySelectorAll(\".tab\").forEach((tab) => {\n      tab.classList.remove(\"active\");\n    });\n    tab.classList.add(\"active\");\n\n    tabPanes.forEach((tabPane) => {\n      tabPane.style.display = \"none\";\n    });\n    tabPanes[index].style.display = \"block\";\n  });\n\n  const tabLink = document.createElement(\"a\");\n  tabLink.setAttribute(\"href\", \"#\");\n  tabLink.innerText = tabPaneTitle.textContent;\n\n  tab.append(tabLink);\n\n  tabBar.append(tab);\n});\n\ncontent.insertBefore(tabBar, tabPanes[0]);\n"], "names": ["tabPanes", "document", "querySelectorAll", "content", "querySelector", "for<PERSON>ach", "tabPane", "style", "display", "tabBar", "createElement", "className", "index", "tabPaneTitle", "tab", "classList", "add", "addEventListener", "e", "preventDefault", "remove", "tabLink", "setAttribute", "innerText", "textContent", "append", "insertBefore"], "sourceRoot": ""}