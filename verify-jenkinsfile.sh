#!/bin/bash

echo "Verifying Jenkinsfile configuration..."

# Check if <PERSON><PERSON><PERSON> exists
if [ ! -f "Jenkinsfile" ]; then
    echo "ERROR: Jenkins<PERSON>le not found!"
    exit 1
fi

# Check for bat commands (should not exist)
bat_count=$(grep -c "bat " Jenkinsfile || true)
if [ $bat_count -gt 0 ]; then
    echo "ERROR: Found $bat_count 'bat' commands in Jenkinsfile!"
    echo "These should be 'sh' commands for Linux:"
    grep -n "bat " Jenkinsfile
    exit 1
else
    echo "✓ No 'bat' commands found in Jenkinsfile"
fi

# Check for sh commands (should exist)
sh_count=$(grep -c "sh " Jenkinsfile || true)
if [ $sh_count -gt 0 ]; then
    echo "✓ Found $sh_count 'sh' commands in Jenkinsfile"
    echo "Shell commands found:"
    grep -n "sh " Jenkinsfile
else
    echo "WARNING: No 'sh' commands found in Jenkinsfile"
fi

# Check agent configuration
if grep -q "agent any" Jenkinsfile; then
    echo "WARNING: Using 'agent any' - consider specifying 'agent { label 'built-in' }'"
elif grep -q "agent {" <PERSON><PERSON><PERSON>; then
    echo "✓ Agent configuration found:"
    grep -A 2 "agent {" Jenkinsfile
fi

echo ""
echo "Jenkinsfile verification completed!"
echo "If everything looks correct, the issue might be in Jenkins cache."
echo "Run './cleanup-jenkins.sh' to clear <PERSON> cache and restart fresh."
