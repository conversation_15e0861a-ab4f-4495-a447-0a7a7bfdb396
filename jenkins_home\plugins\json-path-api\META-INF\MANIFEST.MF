Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: JSON Path API Plugin
Specification-Version: 2.9
Implementation-Title: JSON Path API Plugin
Implementation-Version: 2.9.0-148.v22a_7ffe323ce
Group-Id: io.jenkins.plugins
Artifact-Id: json-path-api
Short-Name: json-path-api
Long-Name: JSON Path API Plugin
Url: https://github.com/jenkinsci/json-path-api-plugin
Plugin-Version: 2.9.0-148.v22a_7ffe323ce
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: asm-api:9.7.1-97.v4cc844130d97
Plugin-Developers: 
Support-Dynamic-Loading: true
Plugin-License-Name: Apache License 2.0
Plugin-License-Url: https://www.apache.org/licenses/LICENSE-2.0
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/json-path-api
 -plugin
Plugin-ScmTag: 22a7ffe323ce9b079322befffa2b9e81e4d8dbc6
Plugin-ScmUrl: https://github.com/jenkinsci/json-path-api-plugin
Implementation-Build: 22a7ffe323ce9b079322befffa2b9e81e4d8dbc6

