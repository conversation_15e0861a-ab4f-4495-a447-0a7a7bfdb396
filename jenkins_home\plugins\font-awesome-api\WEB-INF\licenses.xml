<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='font-awesome-api' version='6.7.2-1'><l:dependency name='Font Awesome API Plugin' groupId='io.jenkins.plugins' artifactId='font-awesome-api' version='6.7.2-1' url='https://github.com/jenkinsci/font-awesome-api-plugin'><l:description>Provides the free fonts of Font Awesome for Jenkins plugins.</l:description><l:license name='MIT license' url=''/></l:dependency><l:dependency name='asm' groupId='org.ow2.asm' artifactId='asm' version='9.7.1' url='http://asm.ow2.io/'><l:description>ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='asm-tree' groupId='org.ow2.asm' artifactId='asm-tree' version='9.7.1' url='http://asm.ow2.io/'><l:description>Tree API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Groovy Sandbox' groupId='org.kohsuke' artifactId='groovy-sandbox' version='1.34' url='https://github.com/jenkinsci/groovy-sandbox'><l:description>Executes untrusted Groovy script safely</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='asm-commons' groupId='org.ow2.asm' artifactId='asm-commons' version='9.7.1' url='http://asm.ow2.io/'><l:description>Usefull class adapters based on ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='SCM API Plugin' groupId='org.jenkins-ci.plugins' artifactId='scm-api' version='703.v72ff4b_259600' url='https://github.com/jenkinsci/scm-api-plugin/blob/master/docs/consumer.adoc'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Structs Plugin' groupId='org.jenkins-ci.plugins' artifactId='structs' version='338.v848422169819' url='https://github.com/jenkinsci/structs-plugin'><l:description>Library plugin for DSL plugins that need names for Jenkins objects.</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='asm-util' groupId='org.ow2.asm' artifactId='asm-util' version='9.7.1' url='http://asm.ow2.io/'><l:description>Utilities for ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='ASM API Plugin' groupId='io.jenkins.plugins' artifactId='asm-api' version='9.7.1-97.v4cc844130d97' url='https://github.com/jenkinsci/asm-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/license/mit/'/></l:dependency><l:dependency name='asm-analysis' groupId='org.ow2.asm' artifactId='asm-analysis' version='9.7.1' url='http://asm.ow2.io/'><l:description>Static code analysis API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Script Security Plugin' groupId='org.jenkins-ci.plugins' artifactId='script-security' version='1369.v9b_98a_4e95b_2d' url='https://github.com/jenkinsci/script-security-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>