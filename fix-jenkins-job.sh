#!/bin/bash

echo "Fixing <PERSON> pipeline job configuration..."

# Wait for <PERSON> to be ready
echo "Waiting for <PERSON> to be ready..."
sleep 10

# Copy the correct job configuration
echo "Copying job configuration..."
docker exec jenkins mkdir -p /var/jenkins_home/jobs/CaseCashBack-Pipeline

# Copy our fixed job configuration
docker cp jenkins-job-config.xml jenkins:/var/jenkins_home/jobs/CaseCashBack-Pipeline/config.xml

# Set correct permissions
docker exec jenkins chown -R jenkins:jenkins /var/jenkins_home/jobs/CaseCashBack-Pipeline

# Restart Jenkins to reload configuration
echo "Restarting <PERSON> to reload configuration..."
docker restart jenkins

echo "Jenkins job configuration fixed!"
echo "Wait 30 seconds for <PERSON> to restart, then access: http://localhost:8082"
echo "The new pipeline job will be called 'CaseCashBack-Pipeline'"
