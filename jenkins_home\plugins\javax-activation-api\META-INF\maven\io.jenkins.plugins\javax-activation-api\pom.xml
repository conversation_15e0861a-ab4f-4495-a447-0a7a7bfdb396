<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>javax-activation-api</artifactId>
  <version>1.2.0-8</version>
  <packaging>hpi</packaging>
  <name>JavaBeans Activation Framework (JAF) API</name>
  <description>Plugin providing the JavaBeans Activation Framework (JAF) API for other plugins</description>
  <url>https://github.com/jenkinsci/javax-activation-api-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>CDDL-1.1</name>
      <url>https://spdx.org/licenses/CDDL-1.1.html</url>
    </license>
  </licenses>
  <scm child.scm.connection.inherit.append.path="false" child.scm.developerConnection.inherit.append.path="false" child.scm.url.inherit.append.path="false">
    <connection>scm:git:https://github.com/jenkinsci/javax-activation-api-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/javax-activation-api-plugin.git</developerConnection>
    <tag>javax-activation-api-1.2.0-8</tag>
    <url>https://github.com/jenkinsci/javax-activation-api-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.sun.activation</groupId>
      <artifactId>javax.activation</artifactId>
      <version>1.2.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
