<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='github-branch-source' version='1822.v9eec8e5e69e3'><l:dependency name='GitHub Branch Source Plugin' groupId='org.jenkins-ci.plugins' artifactId='github-branch-source' version='1822.v9eec8e5e69e3' url='https://github.com/jenkinsci/github-branch-source-plugin'><l:description>Multibranch projects and organization folders from GitHub. Maintained by CloudBees, Inc.</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Variant Plugin' groupId='org.jenkins-ci.plugins' artifactId='variant' version='60.v7290fc0eb_b_cd' url='https://github.com/jenkinsci/variant-plugin'><l:description>This user-invisible library plugin allows other multi-modal plugins to behave differently depending on where they run.</l:description><l:license name='MIT License' url='http://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Jakarta Mail API' groupId='io.jenkins.plugins' artifactId='jakarta-mail-api' version='2.1.3-1' url='https://github.com/jenkinsci/jakarta-mail-api-plugin'><l:description>Plugin providing the Jakarta Mail API for other plugins</l:description><l:license name='EPL-2.0' url='https://opensource.org/licenses/EPL-2.0'/></l:dependency><l:dependency name='asm-util' groupId='org.ow2.asm' artifactId='asm-util' version='9.7.1' url='http://asm.ow2.io/'><l:description>Utilities for ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Apache HttpClient Fluent API' groupId='org.apache.httpcomponents' artifactId='fluent-hc' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents Client fluent API</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='ASM API Plugin' groupId='io.jenkins.plugins' artifactId='asm-api' version='9.7.1-97.v4cc844130d97' url='https://github.com/jenkinsci/asm-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/license/mit/'/></l:dependency><l:dependency name='Gson API Plugin' groupId='io.jenkins.plugins' artifactId='gson-api' version='2.11.0-85.v1f4e87273c33' url='https://github.com/jenkinsci/gson-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Credentials Binding Plugin' groupId='org.jenkins-ci.plugins' artifactId='credentials-binding' version='687.v619cb_15e923f' url='https://github.com/jenkinsci/credentials-binding-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Gson' groupId='com.google.code.gson' artifactId='gson' version='2.11.0' url='https://github.com/google/gson'><l:description>Gson JSON library</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JGit - Apache httpclient based HTTP support' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.http.apache' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.http.apache'><l:description>Apache httpclient based HTTP support</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='asm-tree' groupId='org.ow2.asm' artifactId='asm-tree' version='9.7.1' url='http://asm.ow2.io/'><l:description>Tree API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Jenkins Git client plugin' groupId='org.jenkins-ci.plugins' artifactId='git-client' version='6.1.0' url='https://github.com/jenkinsci/git-client-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Jakarta Mail API' groupId='jakarta.mail' artifactId='jakarta.mail-api' version='2.1.3' url='https://projects.eclipse.org/projects/ee4j/jakarta.mail-api'><l:description>Jakarta Mail API 2.1 Specification API</l:description><l:license name='EPL 2.0' url='http://www.eclipse.org/legal/epl-2.0'/><l:license name='GPL2 w/ CPE' url='https://www.gnu.org/software/classpath/license.html'/><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='JGit - Core' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit'><l:description>Repository access and algorithms</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Pipeline: SCM Step' groupId='org.jenkins-ci.plugins.workflow' artifactId='workflow-scm-step' version='427.v4ca_6512e7df1' url='https://github.com/jenkinsci/workflow-scm-step-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JSON in Java' groupId='org.json' artifactId='json' version='20241224' url='https://github.com/douglascrockford/JSON-java'><l:description>JSON is a light-weight, language independent, data interchange format.
        See http://www.JSON.org/

        The files in this package implement JSON encoders/decoders in Java.
        It also includes the capability to convert between JSON and XML, HTTP
        headers, Cookies, and CDL.

        This is a reference implementation. There are a large number of JSON packages
        in Java. Perhaps someday the Java community will standardize on one. Until
        then, choose carefully.</l:description><l:license name='Public Domain' url='https://github.com/stleary/JSON-java/blob/master/LICENSE'/></l:dependency><l:dependency name='JavaEWAH' groupId='com.googlecode.javaewah' artifactId='JavaEWAH' version='1.2.3' url='https://github.com/lemire/javaewah'><l:description>The bit array data structure is implemented in Java as the BitSet class. Unfortunately, this fails to scale without compression.
  JavaEWAH is a word-aligned compressed variant of the Java bitset class. It uses a 64-bit run-length encoding (RLE) compression scheme.
  The goal of word-aligned compression is not to achieve the best compression, but rather to improve query processing time. Hence, we try to save CPU cycles, maybe at the expense of storage. However, the EWAH scheme we implemented is always more efficient storage-wise than an uncompressed bitmap (implemented in Java as the BitSet class). Unlike some alternatives, javaewah does not rely on a patented scheme.</l:description><l:license name='Apache 2' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JGit - Large File Storage' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.lfs' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.lfs'><l:description>JGit Large File Storage (LFS) implementation.</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Apache HttpClient' groupId='org.apache.httpcomponents' artifactId='httpclient' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents Client</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache Mina SSHD :: Mina' groupId='org.apache.sshd' artifactId='sshd-mina' version='2.14.0' url='https://www.apache.org/sshd/sshd-mina/'><l:description>The Apache Software Foundation provides support for the Apache community of open-source software projects.
    The Apache projects are characterized by a collaborative, consensus based development process, an open and
    pragmatic software license, and a desire to create high quality software that leads the way in its field.
    We consider ourselves not simply a group of projects sharing a server, but rather a community of developers
    and users.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='asm-analysis' groupId='org.ow2.asm' artifactId='asm-analysis' version='9.7.1' url='http://asm.ow2.io/'><l:description>Static code analysis API of ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='SSH Credentials Plugin' groupId='org.jenkins-ci.plugins' artifactId='ssh-credentials' version='349.vb_8b_6b_9709f5b_' url='https://github.com/jenkinsci/ssh-credentials-plugin'><l:description>Allows storage of SSH credentials in Jenkins</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='JGit - HTTP Server' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.http.server' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.http.server'><l:description>Git aware HTTP server implementation.</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Angus Mail Provider' groupId='org.eclipse.angus' artifactId='angus-mail' version='2.0.3' url='http://eclipse-ee4j.github.io/angus-mail/angus-mail'><l:description>Angus Mail Provider</l:description><l:license name='EPL 2.0' url='http://www.eclipse.org/legal/epl-2.0'/><l:license name='GPL2 w/ CPE' url='https://www.gnu.org/software/classpath/license.html'/><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='JGit - Apache sshd-based SSH support' groupId='org.eclipse.jgit' artifactId='org.eclipse.jgit.ssh.apache' version='7.0.0.202409031743-r' url='https://www.eclipse.org/jgit//org.eclipse.jgit.ssh.apache'><l:description>SSH support for JGit based on Apache MINA sshd</l:description><l:license name='BSD-3-Clause' url='https://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Mina SSHD API :: Common' groupId='io.jenkins.plugins.mina-sshd-api' artifactId='mina-sshd-api-common' version='2.14.0-138.v6341ee58e1df' url='https://github.com/jenkinsci/mina-sshd-api-plugin'><l:description>This plugin provides the Apache Mina SSHD Common library to plugins.</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Angus Activation Registries' groupId='org.eclipse.angus' artifactId='angus-activation' version='2.0.2' url='https://github.com/eclipse-ee4j/angus-activation/angus-activation'><l:description>Angus Activation Registries Implementation</l:description><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='SnakeYAML' groupId='org.yaml' artifactId='snakeyaml' version='2.3' url='https://bitbucket.org/snakeyaml/snakeyaml'><l:description>YAML 1.1 parser and emitter for Java</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='asm' groupId='org.ow2.asm' artifactId='asm' version='9.7.1' url='http://asm.ow2.io/'><l:description>ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Apache HttpCore' groupId='org.apache.httpcomponents' artifactId='httpcore' version='4.4.16' url='http://hc.apache.org/httpcomponents-core-ga'><l:description>Apache HttpComponents Core (blocking I/O)</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jenkins Apache HttpComponents Client 4.x API Plugin' groupId='org.jenkins-ci.plugins' artifactId='apache-httpcomponents-client-4-api' version='4.5.14-208.v438351942757' url='https://github.com/jenkinsci/apache-httpcomponents-client-4-api-plugin'><l:description>Bundles Apache HttpComponents Client 4.x and allows it to be used by Jenkins plugins</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='SnakeYAML API Plugin' groupId='io.jenkins.plugins' artifactId='snakeyaml-api' version='2.3-123.v13484c65210a_' url='https://github.com/jenkinsci/snakeyaml-api-plugin'><l:description>This plugin packages stock SnakeYAML library</l:description><l:license name='The MIT License (MIT)' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Jakarta Activation API' groupId='jakarta.activation' artifactId='jakarta.activation-api' version='2.1.3' url='https://github.com/jakartaee/jaf-api'><l:description>Jakarta Activation API 2.1 Specification</l:description><l:license name='EDL 1.0' url='http://www.eclipse.org/org/documents/edl-v10.php'/></l:dependency><l:dependency name='Apache HttpAsyncClient Cache' groupId='org.apache.httpcomponents' artifactId='httpasyncclient-cache' version='4.1.5' url='http://hc.apache.org/httpcomponents-asyncclient'><l:description>Apache HttpComponents AsyncClient Cache</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache HttpClient Cache' groupId='org.apache.httpcomponents' artifactId='httpclient-cache' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents HttpClient - Cache</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Mina SSHD API :: Core' groupId='io.jenkins.plugins.mina-sshd-api' artifactId='mina-sshd-api-core' version='2.14.0-138.v6341ee58e1df' url='https://github.com/jenkinsci/mina-sshd-api-plugin'><l:description>This plugin provides the Apache Mina SSHD Core library to plugins.</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Jenkins Mailer Plugin' groupId='org.jenkins-ci.plugins' artifactId='mailer' version='489.vd4b_25144138f' url='https://github.com/jenkinsci/mailer-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Apache HttpCore NIO' groupId='org.apache.httpcomponents' artifactId='httpcore-nio' version='4.4.16' url='http://hc.apache.org/httpcomponents-core-ga'><l:description>Apache HttpComponents Core (non-blocking I/O)</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='JSON Api Plugin' groupId='io.jenkins.plugins' artifactId='json-api' version='20241224-119.va_dca_a_b_ea_7da_5' url='https://github.com/jenkinsci/json-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT No Attribution License' url='https://opensource.org/license/mit-0/'/></l:dependency><l:dependency name='Apache HttpClient Mime' groupId='org.apache.httpcomponents' artifactId='httpmime' version='4.5.14' url='http://hc.apache.org/httpcomponents-client-ga'><l:description>Apache HttpComponents HttpClient - MIME coded entities</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='asm-commons' groupId='org.ow2.asm' artifactId='asm-commons' version='9.7.1' url='http://asm.ow2.io/'><l:description>Usefull class adapters based on ASM, a very small and fast Java bytecode manipulation framework</l:description><l:license name='BSD-3-Clause' url='https://asm.ow2.io/license.html'/></l:dependency><l:dependency name='Apache Mina SSHD :: Common support utilities' groupId='org.apache.sshd' artifactId='sshd-common' version='2.14.0' url='https://www.apache.org/sshd/sshd-common/'><l:description>The Apache Software Foundation provides support for the Apache community of open-source software projects.
    The Apache projects are characterized by a collaborative, consensus based development process, an open and
    pragmatic software license, and a desire to create high quality software that leads the way in its field.
    We consider ourselves not simply a group of projects sharing a server, but rather a community of developers
    and users.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache Mina SSHD :: Core' groupId='org.apache.sshd' artifactId='sshd-core' version='2.14.0' url='https://www.apache.org/sshd/sshd-core/'><l:description>The Apache Software Foundation provides support for the Apache community of open-source software projects.
    The Apache projects are characterized by a collaborative, consensus based development process, an open and
    pragmatic software license, and a desire to create high quality software that leads the way in its field.
    We consider ourselves not simply a group of projects sharing a server, but rather a community of developers
    and users.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Byte Buddy (without dependencies)' groupId='net.bytebuddy' artifactId='byte-buddy' version='1.15.11' url='https://bytebuddy.net/byte-buddy'><l:description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Apache MINA Core' groupId='org.apache.mina' artifactId='mina-core' version='2.0.27' url='https://mina.apache.org/mina-core/'><l:description>Apache MINA is a network application framework which helps users develop high performance and highly scalable network applications easily.  It provides an abstract event-driven asynchronous API over various transports such as TCP/IP and UDP/IP via Java NIO.</l:description><l:license name='Apache 2.0 License' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Jakarta Activation API' groupId='io.jenkins.plugins' artifactId='jakarta-activation-api' version='2.1.3-1' url='https://github.com/jenkinsci/jakarta-activation-api-plugin'><l:description>Plugin providing the Jakarta Activation API for other plugins</l:description><l:license name='BSD-3-Clause' url='https://opensource.org/licenses/BSD-3-Clause'/></l:dependency><l:dependency name='Apache HttpAsyncClient' groupId='org.apache.httpcomponents' artifactId='httpasyncclient' version='4.1.5' url='http://hc.apache.org/httpcomponents-asyncclient'><l:description>Apache HttpComponents AsyncClient</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>