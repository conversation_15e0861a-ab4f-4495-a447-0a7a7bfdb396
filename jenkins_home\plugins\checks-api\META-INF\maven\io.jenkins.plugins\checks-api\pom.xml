<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>checks-api</artifactId>
  <version>373.vfe7645102093</version>
  <packaging>hpi</packaging>
  <name>Checks API plugin</name>
  <description>Defines an API for Jenkins to publish checks to SCM platforms.</description>
  <url>https://github.com/jenkinsci/checks-api-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/checks-api-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/checks-api-plugin.git</developerConnection>
    <tag>fe76451020934ecc4d5e71a6cf06c5aa942af537</tag>
    <url>https://github.com/jenkinsci/checks-api-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>plugin-util-api</artifactId>
      <version>6.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>commons-text-api</artifactId>
      <version>1.13.1-176.v74d88f22034b_</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>commons-lang3-api</artifactId>
      <version>3.17.0-87.v5cf526e63b_8b_</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>display-url-api</artifactId>
      <version>2.209.v582ed814ff2f</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins.workflow</groupId>
      <artifactId>workflow-step-api</artifactId>
      <version>700.v6e45cb_a_5a_a_21</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins.workflow</groupId>
      <artifactId>workflow-support</artifactId>
      <version>968.v8f17397e87b_8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.main</groupId>
      <artifactId>jenkins-core</artifactId>
      <version>2.492.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.4</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.65</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
