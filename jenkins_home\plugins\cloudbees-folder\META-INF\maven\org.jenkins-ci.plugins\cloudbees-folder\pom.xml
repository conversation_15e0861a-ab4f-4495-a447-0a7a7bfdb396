<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jenkins-ci.plugins</groupId>
  <artifactId>cloudbees-folder</artifactId>
  <version>6.1023.v4fcb_72152519</version>
  <packaging>hpi</packaging>
  <name>Folders Plugin</name>
  <description>This plugin allows users to create "folders" to organize jobs. Users can define custom taxonomies (like
    by project type, organization type etc). Folders are nestable and you can define views within folders. Maintained by CloudBees, Inc.</description>
  <url>https://github.com/jenkinsci/cloudbees-folder-plugin/</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/cloudbees-folder-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/cloudbees-folder-plugin.git</developerConnection>
    <tag>4fcb721525194e6a8841807aae45045acede2da6</tag>
    <url>https://github.com/jenkinsci/cloudbees-folder-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>ionicons-api</artifactId>
      <version>74.v93d5eb_813d5f</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.plugins</groupId>
      <artifactId>credentials</artifactId>
      <version>1405.vb_cda_74a_f8974</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jenkins-ci.main</groupId>
      <artifactId>jenkins-core</artifactId>
      <version>2.479.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.4</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.64</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
