<div>
  Bu se&#231;en<PERSON> de<PERSON>ye al&#305;n&#305;rsa, projenizin
  yap&#305;land&#305;rmalar&#305; ile
  <a href="lastSuccessfulBuild/fingerprint">
    ili&#351;kili (parmakizi y&#246;ntemiyle) t&#252;m
    yap&#305;land&#305;rmalar&#305;n
  </a>
  loglar&#305;, log rotasyonundan etkilenmeyecektir.

  <p>
    <PERSON> &#252;zerinde yap&#305;land&#305;rd&#305;&#287;&#305;n&#305;z
    i&#351;iniz, ba&#351;ka i&#351;lere ba&#287;l&#305; ise, ve
    &#231;al&#305;&#351;ma alan&#305;n&#305;z&#305; etiketlemek
    istiyorsan&#305;z, ba&#287;&#305;ml&#305;l&#305;klar&#305;n&#305; da
    etiketlemeniz uygun ve zorunludur. Problem &#351;u ki, e&#287;er projenizin
    i&#231;erisi<PERSON><PERSON> yap&#305;land&#305;rmalar&#305;n loglar&#305;, silinmek
    i&#231;in ayarlanm&#305;&#351; ise log rotasyon mekanizmas&#305;,
    ba&#287;&#305;ml&#305;l&#305;klar&#305;n&#305;za burnunu sokabilir. Ve bu
    olursa, ba&#287;&#305;ml&#305;l&#305;klar&#305;n&#305;z&#305; g&#252;venilir
    bir &#351;ekilde etiketleyemezsiniz.
  </p>

  <p>
    Bu &#246;zellik, ba&#287;l&#305; oldu&#287;unuz
    yap&#305;land&#305;rmalar&#305; "kilitleyerek" bu problemi &#231;&#246;zer,
    b&#246;ylece t&#252;m ba&#287;&#305;ml&#305;l&#305;klar&#305;n&#305;z&#305;
    daima, rahat&#231;a etiketleyebilirsiniz.
  </p>
</div>
