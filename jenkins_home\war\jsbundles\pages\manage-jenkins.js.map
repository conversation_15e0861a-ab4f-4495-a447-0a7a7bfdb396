{"version": 3, "file": "pages/manage-jenkins.js", "mappings": ";AAAA,MAAMA,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;AAErEF,cAAc,CAACG,WAAW,GAAG,YAAY;EACvC,OAAOC,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAACK,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,CACnEC,GAAG,CAAEC,IAAI,KAAM;IACdC,GAAG,EAAED,IAAI,CAACN,aAAa,CAAC,GAAG,CAAC,CAACQ,IAAI;IACjCC,IAAI,EAAEH,IAAI,CAACN,aAAa,CACtB,oEACF,CAAC,CAACU,SAAS;IACXC,KAAK,EAAEL,IAAI,CAACN,aAAa,CAAC,IAAI,CAAC,CAACY;EAClC,CAAC,CAAC,CAAC,CACFC,MAAM,CAAEP,IAAI,IAAK,CAACA,IAAI,CAACC,GAAG,CAACO,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC,C", "sources": ["webpack://jenkins-ui/./src/main/js/pages/manage-jenkins/index.js"], "sourcesContent": ["const searchBarInput = document.querySelector(\"#settings-search-bar\");\n\nsearchBarInput.suggestions = function () {\n  return Array.from(document.querySelectorAll(\".jenkins-section__item\"))\n    .map((item) => ({\n      url: item.querySelector(\"a\").href,\n      icon: item.querySelector(\n        \".jenkins-section__item__icon svg, .jenkins-section__item__icon img\",\n      ).outerHTML,\n      label: item.querySelector(\"dt\").textContent,\n    }))\n    .filter((item) => !item.url.endsWith(\"#\"));\n};\n"], "names": ["searchBarInput", "document", "querySelector", "suggestions", "Array", "from", "querySelectorAll", "map", "item", "url", "href", "icon", "outerHTML", "label", "textContent", "filter", "endsWith"], "sourceRoot": ""}