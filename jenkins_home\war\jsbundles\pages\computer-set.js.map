{"version": 3, "file": "pages/computer-set.js", "mappings": ";;;;AAAO,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EACnDF,QAAQ,CAACG,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAAC;EAChC,OAAOJ,QAAQ,CAACK,OAAO,CAACC,iBAAiB;AAC3C;AAEO,SAASC,IAAIA,CAACC,MAAM,EAAE;EAC3B,OAAOA,MAAM,CACVJ,IAAI,CAAC,CAAC,CACNK,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBC,WAAW,CAAC,CAAC;AAClB;;ACXmD;AAEnDT,QAAQ,CACLU,aAAa,CAAC,8BAA8B,CAAC,CAC7CC,gBAAgB,CAAC,OAAO,EAAE,MAAM;EAC/B,MAAMZ,QAAQ,GAAGC,QAAQ,CAACU,aAAa,CAAC,gCAAgC,CAAC;EACzE,MAAME,KAAK,GAAGb,QAAQ,CAACc,YAAY,CAAC,YAAY,CAAC;EACjD,MAAMT,OAAO,GAAGP,qBAAqB,CACnC,OAAO,GAAGE,QAAQ,CAACG,SAAS,GAAG,QACjC,CAAC;EAEDY,MAAM,CAACC,KAAK,CAACX,OAAO,EAAE;IACpBY,QAAQ,EAAE,OAAO;IACjBJ,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC,CAAC,C", "sources": ["webpack://jenkins-ui/./src/main/js/util/dom.js", "webpack://jenkins-ui/./src/main/js/pages/computer-set/index.js"], "sourcesContent": ["export function createElementFromHtml(html) {\n  const template = document.createElement(\"template\");\n  template.innerHTML = html.trim();\n  return template.content.firstElementChild;\n}\n\nexport function toId(string) {\n  return string\n    .trim()\n    .replace(/[\\W_]+/g, \"-\")\n    .toLowerCase();\n}\n", "import { createElementFromHtml } from \"@/util/dom\";\n\ndocument\n  .querySelector(\"#button-computer-icon-legend\")\n  .addEventListener(\"click\", () => {\n    const template = document.querySelector(\"#template-computer-icon-legend\");\n    const title = template.getAttribute(\"data-title\");\n    const content = createElementFromHtml(\n      \"<div>\" + template.innerHTML + \"</div>\",\n    );\n\n    dialog.modal(content, {\n      maxWidth: \"550px\",\n      title: title,\n    });\n  });\n"], "names": ["createElementFromHtml", "html", "template", "document", "createElement", "innerHTML", "trim", "content", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "toId", "string", "replace", "toLowerCase", "querySelector", "addEventListener", "title", "getAttribute", "dialog", "modal", "max<PERSON><PERSON><PERSON>"], "sourceRoot": ""}