/* ------------------------------------------------------------------------------------------------------------------- */
/* Undo problematic bootstrap styles
/* These styles will only be picked up on Jenkins >=2.239
/* ------------------------------------------------------------------------------------------------------------------- */

:root {
    --bs-nav-tabs-border-width: 0;
}

.pagination {
    --bs-pagination-active-color: var(--text-color);
    --bs-pagination-active-bg: var(--item-background--active);
    --bs-pagination-active-border-color: var(--item-background--active);

    --bs-pagination-hover-color: var(--link-color);
    --bs-pagination-hover-bg: var(--item-background--hover);
    --bs-pagination-hover-border-color: var(--item-background--hover);

    --bs-link-color: var(--link-color);
    --bs-pagination-bg: var(--background);
    --bs-pagination-border-color: var(--item-background--hover);
}

.modal {
    --bs-modal-color: var(--text-color);
    --bs-modal-bg: var(--background);
}

body {
    background-color: var(--background);
    color: var(--text-color);
}

.table {
    color: inherit;
}

.form-control {
    background-color: var(--input-color);
    border: 1px solid var(--input-border);
    color: var(--text-color);
}

.custom-select {
    color: var(--text-color);
}

pre {
    color: var(--text-color);
}

.log-output {
    font-size: var(--font-size-sm);
}

/* ------------------------------------------------------------------------------------------------------------------- */
/* Bootstrap tabs in Jenkins style                                                                                                  */
/* ------------------------------------------------------------------------------------------------------------------- */

.tabBar {
    list-style: none;
}

.tabBar .tab a.active {
    z-index: 2;
    cursor: default;
    font-weight: 450;
    color: var(--tabs-item-foreground--active);

    &::before {
        background-clip: padding-box;
        background-color: var(--tabs-item-background--selected) !important;
        border: var(--jenkins-border--subtle) !important;
    }

    &::after {
        display: none;
    }

    &::before,
    &::after {
        inset: 0;
    }
}

/* ------------------------------------------------------------------------------------------------------------------- */
/* Bootstrap carousel                                                                                                  */
/* ------------------------------------------------------------------------------------------------------------------- */

.carousel-control-prev, .carousel-control-next {
    width: 15px;
    fill: var(--text-color);
}

/* ------------------------------------------------------------------------------------------------------------------- */
/* Font awesome icons float right                                                                                      */
/* ------------------------------------------------------------------------------------------------------------------- */
.card {
    background-color: var(--background);
    border-radius: calc(var(--table-border-radius) + 2px);
    border-width: 5px;
    border-color: var(--table-background);
    color: var(--text-color);
    font-size: inherit;
}

.card-header {
    font-size: 1.1rem;
    font-weight: 500;
    background: var(--table-background);
    border-bottom: var(--table-background);
}

.card-header-title {
    margin-right: auto;
}

.icon-right  {
    fill: currentColor;
    height: 24px;
    width: 24px;
    position: relative;
    margin: auto;
}

.icon-border {
    float: right;
    background: var(--item-background--active);
    border-radius: 100%;
    height: 48px;
    width: 48px;
    content: "";
    pointer-events: none;
    position: relative;
    display: flex;
}

.tooltip {
    --bs-tooltip-opacity: 1;
    --bs-tooltip-arrow-width: 0px;
    --bs-tooltip-arrow-height: 0px;
    --bs-tooltip-font-size: 550;
    --bs-tooltip-color: var(--text-color);
    --bs-tooltip-padding-x: 0.8rem;
    --bs-tooltip-padding-y: 0.45rem;
    --bs-tooltip-max-width: min(50vw, 1000px) !important;
    --bs-tooltip-border-radius: 0.66rem;
    --bs-tooltip-bg: #f8f9fa;
}

.tooltip-inner {
    background-color: var(--background);
    font-weight: 550;
    font-size: 0.75rem;
    backdrop-filter: contrast(0.6) brightness(2.4) saturate(2) blur(15px);
    box-shadow: 0 0 8px 2px rgba(0,0,30,.05), 0 0 1px 1px rgba(0,0,20,.025), 0 10px 20px rgba(0,0,20,.15);
}
