<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.jenkins.plugins</groupId>
  <artifactId>jaxb</artifactId>
  <version>2.3.9-133.vb_ec76a_73f706</version>
  <packaging>hpi</packaging>
  <name>JAXB plugin</name>
  <description>Detached packaging for JAXB for more transparent Java 9+ compatibility</description>
  <url>https://github.com/jenkinsci/jaxb-plugin</url>
  <inceptionYear>2016</inceptionYear>
  <licenses>
    <license>
      <name>MIT</name>
      <url>https://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/jenkinsci/jaxb-plugin.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/jaxb-plugin.git</developerConnection>
    <tag>bec76a73f70667968d170e2c8be11519c510d4d9</tag>
    <url>https://github.com/jenkinsci/jaxb-plugin</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>https://repo.jenkins-ci.org/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <version>2.3.9</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.sun.activation</groupId>
          <artifactId>jakarta.activation</artifactId>
        </exclusion>
        <exclusion>
          <groupId>jakarta.xml.bind</groupId>
          <artifactId>jakarta.xml.bind-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.jenkins.plugins</groupId>
      <artifactId>javax-activation-api</artifactId>
      <version>1.2.0-7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.3.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>javax.activation</groupId>
          <artifactId>javax.activation-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jenkins-ci.tools</groupId>
        <artifactId>maven-hpi-plugin</artifactId>
        <version>3.61</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
