<div>
  いつエージェントを開始・終了するのか選択します。

  <dl>
    <dt><b>可能な限りオンラインのままにする</b></dt>
    <dd>
      これはデフォルトで通常の設定です。
      このモードでは、Jenkinsはエージェントを極力オンラインのままにしようとします。
      ユーザーの手助けなしにJenkinsがエージェントを開始できるなら、エージェントが使用できない場合に定期的にリスタートしようとします。
      Jenkinsはエージェントをオフラインにはしません。
    </dd>

    <!--dt><b>
            Take this agent online and offline at specific times
        </b></dt>
        <dd>
            In this mode, <PERSON> will keep the agent on-line according to the configured schedule.
            If <PERSON> can start the agent without user assistance, it will periodically
            attempt to start the agent if it is unavailable during an on-line window.

            During an offline window, the agent will only be taken off-line if there are no active
            jobs running on the agent.
        </dd-->

    <dt><b>要求時にはオンラインにし、待機中にはオフラインにする</b></dt>
    <dd>
      このモードでは、ユーザーの手助けなしにJenkinsがエージェントを開始できるなら、
      次の条件に合う未実行のジョブがある間はエージェントを開始しようとします。
      <ul>
        <li>少なくともある一定の起動要求期間にキューにある</li>
        <li>このエージェント上で実行可能</li>
      </ul>

      エージェントは次の場合オフラインになります。
      <ul>
        <li>エージェント上に実行中のジョブが存在しない</li>
        <li>エージェントが少なくともある一定の期間待機中である</li>
      </ul>
    </dd>
  </dl>
</div>
