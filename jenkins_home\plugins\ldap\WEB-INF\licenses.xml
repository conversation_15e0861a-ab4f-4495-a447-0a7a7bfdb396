<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='ldap' version='780.vcb_33c9a_e4332'><l:dependency name='LDAP Plugin' groupId='org.jenkins-ci.plugins' artifactId='ldap' version='780.vcb_33c9a_e4332' url='https://github.com/jenkinsci/ldap-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='spring-security-crypto' groupId='org.springframework.security' artifactId='spring-security-crypto' version='6.3.4' url='https://spring.io/projects/spring-security'><l:description>Spring Security</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='micrometer-commons' groupId='io.micrometer' artifactId='micrometer-commons' version='1.12.11' url='https://github.com/micrometer-metrics/micrometer'><l:description>Module containing common code</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Spring Core' groupId='org.springframework' artifactId='spring-core' version='6.1.14' url='https://github.com/spring-projects/spring-framework'><l:description>Spring Core</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Spring Context' groupId='org.springframework' artifactId='spring-context' version='6.1.14' url='https://github.com/spring-projects/spring-framework'><l:description>Spring Context</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='SLF4J API Module' groupId='org.slf4j' artifactId='slf4j-api' version='2.0.16' url='http://www.slf4j.org'><l:description>The slf4j API</l:description><l:license name='MIT License' url='http://www.opensource.org/licenses/mit-license.php'/></l:dependency><l:dependency name='spring-ldap-core' groupId='org.springframework.ldap' artifactId='spring-ldap-core' version='3.2.7' url='https://spring.io/projects/spring-ldap'><l:description>Spring LDAP</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Spring Expression Language (SpEL)' groupId='org.springframework' artifactId='spring-expression' version='6.1.14' url='https://github.com/spring-projects/spring-framework'><l:description>Spring Expression Language (SpEL)</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Time4J-TZDATA' groupId='net.time4j' artifactId='time4j-tzdata' version='5.0-2025a' url='http://www.time4j.net'><l:description>TZ-Repository for Time4J</l:description><l:license name='GNU LESSER GENERAL PUBLIC LICENSE, Version 2.1, February 1999' url='http://www.gnu.org/licenses/lgpl-2.1.html'/></l:dependency><l:dependency name='Spring Transaction' groupId='org.springframework' artifactId='spring-tx' version='6.1.14' url='https://github.com/spring-projects/spring-framework'><l:description>Spring Transaction</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Time4J-Base' groupId='net.time4j' artifactId='time4j-base' version='5.9.4' url='http://www.time4j.net/time4j-base'><l:description>Advanced Date, Time and Interval Library for Java</l:description><l:license name='GNU LESSER GENERAL PUBLIC LICENSE, Version 2.1, February 1999' url='http://www.gnu.org/licenses/lgpl-2.1.html'/></l:dependency><l:dependency name='spring-security-ldap' groupId='org.springframework.security' artifactId='spring-security-ldap' version='6.3.4' url='https://spring.io/projects/spring-security'><l:description>Spring Security</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='spring-security-core' groupId='org.springframework.security' artifactId='spring-security-core' version='6.3.4' url='https://spring.io/projects/spring-security'><l:description>Spring Security</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='Spring AOP' groupId='org.springframework' artifactId='spring-aop' version='6.1.14' url='https://github.com/spring-projects/spring-framework'><l:description>Spring AOP</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency><l:dependency name='micrometer-observation' groupId='io.micrometer' artifactId='micrometer-observation' version='1.12.11' url='https://github.com/micrometer-metrics/micrometer'><l:description>Module containing Observation related code</l:description><l:license name='The Apache Software License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Spring Beans' groupId='org.springframework' artifactId='spring-beans' version='6.1.14' url='https://github.com/spring-projects/spring-framework'><l:description>Spring Beans</l:description><l:license name='Apache License, Version 2.0' url='https://www.apache.org/licenses/LICENSE-2.0'/></l:dependency></l:dependencies>