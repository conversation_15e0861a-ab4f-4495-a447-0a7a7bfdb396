﻿<div>
  Spécifie le(s) JDK avec le(s)quel(s) les builds seront exécutés. Si aucun
  n'est donné, le JDK par défaut est utilisé (pas de variable
  <tt>JAVA_HOME</tt> explicite; on suppose que la commande <tt>java</tt>
  est dans le <tt>PATH</tt>). Si de multiples JDK sont précisés, la
  matrice de configuration incluera tous ces JDK.
  <br>
  Avoir de multiples valeurs est utile typiquement quand le job exécute des
  tests et que vous devez les lancer sur plusieurs versions différentes
  du JDK.
  <br>
  Au cours d'un build, le JDK sélectionné est visible dans l'axe 'jdk'.
  Référez-vous à l'aide sur les 'axes' ci-dessous pour plus d'information
  sur l'accès à la valeur d'un axe.
</div>