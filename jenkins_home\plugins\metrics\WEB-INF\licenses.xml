<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='metrics' version='4.2.32-476.v5042e1c1edd7'><l:dependency name='Metrics Plugin' groupId='org.jenkins-ci.plugins' artifactId='metrics' version='4.2.32-476.v5042e1c1edd7' url='https://github.com/jenkinsci/metrics-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='The MIT license' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Jackson Integration for Metrics' groupId='io.dropwizard.metrics' artifactId='metrics-json' version='4.2.32' url='https://metrics.dropwizard.io/metrics-json'><l:description>A set of Jackson modules which provide serializers for most Metrics classes.</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.html'/></l:dependency><l:dependency name='Metrics Health Checks' groupId='io.dropwizard.metrics' artifactId='metrics-healthchecks' version='4.2.32' url='https://metrics.dropwizard.io/metrics-healthchecks'><l:description>An addition to Metrics which provides the ability to run application-specific health checks,
        allowing you to check your application's heath in production.</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.html'/></l:dependency><l:dependency name='Metrics Integration with JMX' groupId='io.dropwizard.metrics' artifactId='metrics-jmx' version='4.2.32' url='https://metrics.dropwizard.io/metrics-jmx'><l:description>A set of classes which allow you to report metrics via JMX.</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.html'/></l:dependency><l:dependency name='Metrics Integration for Servlets' groupId='io.dropwizard.metrics' artifactId='metrics-servlet' version='4.2.32' url='https://metrics.dropwizard.io/metrics-servlet'><l:description>An instrumented filter for servlet environments.</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.html'/></l:dependency><l:dependency name='JVM Integration for Metrics' groupId='io.dropwizard.metrics' artifactId='metrics-jvm' version='4.2.32' url='https://metrics.dropwizard.io/metrics-jvm'><l:description>A set of classes which allow you to monitor critical aspects of your Java Virtual Machine
        using Metrics.</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.html'/></l:dependency><l:dependency name='Metrics Core' groupId='io.dropwizard.metrics' artifactId='metrics-core' version='4.2.32' url='https://metrics.dropwizard.io/metrics-core'><l:description>Metrics is a Java library which gives you unparalleled insight into what your code does in
        production. Metrics provides a powerful toolkit of ways to measure the behavior of critical
        components in your production environment.</l:description><l:license name='Apache License 2.0' url='https://www.apache.org/licenses/LICENSE-2.0.html'/></l:dependency></l:dependencies>