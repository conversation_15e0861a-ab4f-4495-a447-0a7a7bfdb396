<div>
  Настройки на планираните изгражданията на тази машина от Jenkins

  <dl>
    <dt><b>Компютърът да се използва максимално</b></dt>
    <dd>
      Това е стандартната стойност.
      <br />
      В този режим Jenkins свободно решава дали да използва компютъра. Когато се
      появи нужда от изграждане, което може да се извърши от тази машина,
      Jenkins ще я ползва.
    </dd>

    <dt><b>Само задачи, чиито изрази с етикети отговарят на тази машина</b></dt>
    <dd>
      В този режим Jenkins изгражда проект на този компютър, когато проектът е
      ограничен с етикети до определени машини, а този компютър отговаря на
      етикетите или ограничението по име.
      <p>
        Това позволява заделянето на тази машина за определен вид задачи.
        Например, ако някои задачи включват тестове за производителност, може да
        искате те да се изпълняват на определени, точно конфигурирани машини,
        като на тях не се изпълнява нищо друго. Това се постига, като ограничите
        задачите чрез израз с етикети, който напасва с тази машина.
        <br />
        Допълнително, като зададете
        <i>броя на едновременните задачи</i>
        да е
        <code>1</code>
        , осигурявате, че в даден момент се изпълнява само една серия от
        тестовете за производителност, така резултатите няма да се повлияят от
        друга задача изпълнявана по това време.
      </p>
    </dd>
  </dl>
</div>
