<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='antisamy-markup-formatter' version='173.v680e3a_b_69ff3'><l:dependency name='OWASP Markup Formatter Plugin' groupId='org.jenkins-ci.plugins' artifactId='antisamy-markup-formatter' version='173.v680e3a_b_69ff3' url='https://github.com/jenkinsci/antisamy-markup-formatter-plugin'><l:description>Sanitize HTML markup in user-submitted text to be displayed on the Jenkins UI.</l:description><l:license name='MIT' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='OWASP Java HTML Sanitizer' groupId='com.googlecode.owasp-java-html-sanitizer' artifactId='owasp-java-html-sanitizer' version='20220608.1' url='https://github.com/OWASP/java-html-sanitizer/owasp-java-html-sanitizer'><l:description>Takes third-party HTML and produces HTML that is safe to embed in
    your web application.
    Fast and easy to configure.</l:description><l:license name='Apache License, Version 2.0' url='http://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>