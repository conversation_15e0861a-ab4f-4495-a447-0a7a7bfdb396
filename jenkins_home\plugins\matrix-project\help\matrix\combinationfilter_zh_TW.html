<div>
  Jenkins 預設會建置所有軸線可能的排列組合。不過有時候組合會過多，或是有些組合根本就不合理。
  這種情況下，您可以使用 Groovy 表示式回傳值過濾掉不要的組合，讓矩陣稀疏點。

  <p>
  設定了 Groovy 表示式後，只有回傳結果是 <b>true</b> 的組合才會建置。
  表示式執行時，軸線值會被當做變數傳入 (變數值設成當下要判斷的組合)。

  <h4>依變數值篩選</h4>
  <p>
  假設您要在不同作業系統的不同編譯器上建置。
  而您的 Agent 標籤有 <b>label=[linux,solaris]</b>，並建了 <b>compiler=[gcc,cc]</b> 這個軸線。

  下列的每一個表式示都會過濾掉在 <b>linux</b> 上的 <b>cc</b> 建置。
  差別在您怎麼看待這個限制，您可能會覺得某些方式就是比較直觀。

  <center>
    <table>
        <tr>
            <td>讀作「同時是 Linux 跟 cc 時，就不要做」。</td>
            <td>
                <pre>!(label=="linux" &amp;&amp; compiler=="cc")</pre>
            </td>
        </tr>
        <tr>
            <td>讀作「一定要在 Solaris 上或是用 gcc 才算是有效的組合」。</td>
            <td>
                <pre>label=="solaris" || compiler=="gcc"</pre>
            </td>
        </tr>
        <tr>
            <td>讀作「在 Solaris 上，只用 cc」。</td>
            <td>
                <pre>(label=="solaris").implies(compiler=="cc")</pre>
            </td>
        </tr>
    </table>
  </center>

  <h4>矩陣稀疏化</h4>
  <p>
  除了設定變數值篩選規則外，您也可以使用 "<code>index</code>" 特別變數，將矩陣稀疏化。

  <p>
  例如 <code>index%2==0</code> 可以跳過一種組合後再挑一種，讓矩陣大小減半，這種涵蓋率也很合常理。
  同樣的，<code>index%3!=0</code> 會每三種組合就挑一個掉，讓矩陣縮小三分之一。
</div>