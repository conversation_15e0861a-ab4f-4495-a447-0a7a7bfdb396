<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://inkscape.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   sodipodi:docname="go-next.svg"
   sodipodi:docbase="/home/<USER>/cvs/freedesktop.org/tango-icon-theme/scalable/actions"
   inkscape:version="0.43+devel"
   sodipodi:version="0.32"
   id="svg11300"
   height="48"
   width="48"
   inkscape:export-filename="/home/<USER>/Desktop/wi-fi.png"
   inkscape:export-xdpi="90.000000"
   inkscape:export-ydpi="90.000000"
   version="1.0"
   inkscape:output_extension="org.inkscape.output.svg.inkscape">
  <defs
     id="defs3">
    <linearGradient
       id="linearGradient2591">
      <stop
         style="stop-color:#73d216"
         offset="0"
         id="stop2593" />
      <stop
         style="stop-color:#4e9a06"
         offset="1.0000000"
         id="stop2595" />
    </linearGradient>
    <linearGradient
       id="linearGradient8662"
       inkscape:collect="always">
      <stop
         id="stop8664"
         offset="0"
         style="stop-color:#000000;stop-opacity:1;" />
      <stop
         id="stop8666"
         offset="1"
         style="stop-color:#000000;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       id="linearGradient8650"
       inkscape:collect="always">
      <stop
         id="stop8652"
         offset="0"
         style="stop-color:#ffffff;stop-opacity:1;" />
      <stop
         id="stop8654"
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0;" />
    </linearGradient>
    <radialGradient
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(2.046729,-3.749427e-16,2.853404e-16,1.557610,-19.51799,3.452086)"
       r="17.171415"
       fy="2.8969381"
       fx="19.701141"
       cy="2.8969381"
       cx="19.701141"
       id="radialGradient8656"
       xlink:href="#linearGradient8650"
       inkscape:collect="always" />
    <radialGradient
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.000000,0.000000,0.000000,0.536723,2.511012e-15,16.87306)"
       r="15.644737"
       fy="36.421127"
       fx="24.837126"
       cy="36.421127"
       cx="24.837126"
       id="radialGradient8668"
       xlink:href="#linearGradient8662"
       inkscape:collect="always" />
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2591"
       id="radialGradient2597"
       cx="22.291636"
       cy="32.797512"
       fx="22.291636"
       fy="32.797512"
       r="16.9562"
       gradientTransform="matrix(0.843022,1.871885e-16,-2.265228e-16,1.020168,4.499298,1.381992)"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     inkscape:window-y="25"
     inkscape:window-x="0"
     inkscape:window-height="885"
     inkscape:window-width="1280"
     inkscape:showpageshadow="false"
     inkscape:document-units="px"
     inkscape:grid-bbox="true"
     showgrid="false"
     inkscape:current-layer="layer1"
     inkscape:cy="27.398876"
     inkscape:cx="34.827552"
     inkscape:zoom="11.313708"
     inkscape:pageshadow="2"
     inkscape:pageopacity="0.0"
     borderopacity="0.25490196"
     bordercolor="#666666"
     pagecolor="#ffffff"
     id="base"
     fill="#4e9a06"
     stroke="#4e9a06" />
  <metadata
     id="metadata4">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:creator>
          <cc:Agent>
            <dc:title>Jakub Steiner</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:source>http://jimmac.musichall.cz</dc:source>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/2.0/" />
        <dc:title>Go Next</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>go</rdf:li>
            <rdf:li>next</rdf:li>
            <rdf:li>right</rdf:li>
            <rdf:li>arrow</rdf:li>
            <rdf:li>pointer</rdf:li>
            <rdf:li>&gt;</rdf:li>
          </rdf:Bag>
        </dc:subject>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/2.0/">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Notice" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/Attribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
        <cc:requires
           rdf:resource="http://web.resource.org/cc/ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:groupmode="layer"
     inkscape:label="Layer 1"
     id="layer1">
    <path
       transform="matrix(1.271186,0.000000,0.000000,1.271186,-8.119376,-15.10179)"
       d="M 40.481863 36.421127 A 15.644737 8.3968935 0 1 1  9.1923885,36.421127 A 15.644737 8.3968935 0 1 1  40.481863 36.421127 z"
       sodipodi:ry="8.3968935"
       sodipodi:rx="15.644737"
       sodipodi:cy="36.421127"
       sodipodi:cx="24.837126"
       id="path8660"
       style="opacity:0.29946522;color:#000000;fill:url(#radialGradient8668);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       sodipodi:type="arc" />
    <path
       sodipodi:nodetypes="cccccccc"
       id="path8643"
       d="M 8.5541875,15.517348 L 8.5541875,32.511768 L 21.538,32.511768 L 21.538,41.056806 L 41.497835,24.150365 L 21.41919,7.1251168 L 21.41919,15.522652 L 8.5541875,15.517348 z "
       style="opacity:1;color:#000000;fill:url(#radialGradient2597);fill-opacity:1;fill-rule:evenodd;stroke:#3a7304;stroke-width:1.00000036;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible" />
    <path
       sodipodi:nodetypes="cccccc"
       id="path8645"
       d="M 21.962385,8.2485033 L 21.962385,16.054978 L 9.1452151,16.054978 L 9.1452151,25.095691 C 26.895215,27.095691 25.778752,17.640403 40.528752,24.140403 L 21.962385,8.2485033 z "
       style="opacity:0.5080214;color:#000000;fill:url(#radialGradient8656);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible" />
    <path
       style="opacity:0.48128339;color:#000000;fill:none;fill-opacity:1;fill-rule:evenodd;stroke:#ffffff;stroke-width:1.00000036;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:10;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible"
       d="M 9.537702,16.561892 L 9.537702,31.546332 L 22.523069,31.546332 L 22.523069,38.941498 L 40.001083,24.145807 L 22.507108,9.3654066 L 22.507108,16.566789 L 9.537702,16.561892 z "
       id="path8658"
       sodipodi:nodetypes="cccccccc" />
  </g>
</svg>
