<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='org.jenkins-ci.plugins' artifactId='git' version='5.7.0'><l:dependency name='Git plugin' groupId='org.jenkins-ci.plugins' artifactId='git' version='5.7.0' url='https://github.com/jenkinsci/git-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Font Awesome API Plugin' groupId='io.jenkins.plugins' artifactId='font-awesome-api' version='6.6.0-2' url='https://github.com/jenkinsci/font-awesome-api-plugin'><l:description>Provides the free fonts of Font Awesome for Jenkins plugins.</l:description><l:license name='MIT license' url=''/></l:dependency><l:dependency name='JBoss Marshalling River' groupId='org.jboss.marshalling' artifactId='jboss-marshalling-river' version='2.2.1.Final' url='http://www.jboss.org'><l:description>JBoss Marshalling River Implementation</l:description><l:license name='Apache License 2.0' url='http://repository.jboss.org/licenses/apache-2.0.txt'/></l:dependency><l:dependency name='JBoss Marshalling API' groupId='org.jboss.marshalling' artifactId='jboss-marshalling' version='2.2.1.Final' url='http://www.jboss.org'><l:description>JBoss Marshalling API</l:description><l:license name='Apache License 2.0' url='http://repository.jboss.org/licenses/apache-2.0.txt'/></l:dependency><l:dependency name='Plugin Utilities API Plugin' groupId='io.jenkins.plugins' artifactId='plugin-util-api' version='5.1.0' url='https://github.com/jenkinsci/plugin-util-api-plugin'><l:description>Provides several utility classes that can be used to accelerate plugin development.</l:description><l:license name='MIT license' url=''/></l:dependency><l:dependency name='commons-lang3 v3.x Jenkins API Plugin' groupId='io.jenkins.plugins' artifactId='commons-lang3-api' version='3.17.0-84.vb_b_938040b_078' url='https://github.com/jenkinsci/commons-lang3-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='Apache-2.0' url='https://opensource.org/licenses/Apache-2.0'/></l:dependency><l:dependency name='SLF4J API Module' groupId='org.slf4j' artifactId='slf4j-api' version='2.0.16' url='http://www.slf4j.org'><l:description>The slf4j API</l:description><l:license name='MIT License' url='http://www.opensource.org/licenses/mit-license.php'/></l:dependency><l:dependency name='Apache Commons Lang' groupId='org.apache.commons' artifactId='commons-lang3' version='3.14.0' url='https://commons.apache.org/proper/commons-lang/'><l:description>Apache Commons Lang, a package of Java utility classes for the
  classes that are in java.lang's hierarchy, or are considered to be so
  standard as to justify existence in java.lang.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency><l:dependency name='Pipeline: Supporting APIs' groupId='org.jenkins-ci.plugins.workflow' artifactId='workflow-support' version='936.v9fa_77211ca_e1' url='https://github.com/jenkinsci/workflow-support-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency><l:dependency name='Bootstrap 5 API Plugin' groupId='io.jenkins.plugins' artifactId='bootstrap5-api' version='5.3.3-1' url='https://github.com/jenkinsci/bootstrap5-api-plugin'><l:description>Provides Bootstrap 5 for Jenkins plugins.</l:description><l:license name='MIT license' url=''/></l:dependency><l:dependency name='Pipeline: API' groupId='org.jenkins-ci.plugins.workflow' artifactId='workflow-api' version='1336.vee415d95c521' url='https://github.com/jenkinsci/workflow-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='MIT License' url='https://opensource.org/licenses/MIT'/></l:dependency></l:dependencies>