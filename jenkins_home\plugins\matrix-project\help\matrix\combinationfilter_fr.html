﻿<div>
  <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON> builde toutes les combinaisons possibles de tous les axes, de façon exhaustive. <PERSON><PERSON><PERSON>, 
  cela fait trop de combinaisons ou certaines combinaisons n'ont pas de sens.
  Dans ce cas, vous pouvez réduire la matrice en filtrant les combinaisons que vous ne voulez pas à l'aide
  d'une expression Groovy qui retourne vrai ou faux.

  <p>
  Quand vous spécifiez une expression Groovy ici, seules les combinaisons qui retournent <b>true</b>
  seront construites. Lors de l'évaluation de l'expression, les axes sont exposés comme des variables
  (avec leurs valeurs positionnées sur la combinaison courante).

  <h4>Filtre basé sur les valeurs</h4>
  <p>
  Par exemple, disons que vous buildez sur des systèmes d'exploitation différents, pour des compilateurs
  différents. Supposons que les libellés de vos esclaves sont <b>label=[linux,solaris]</b> et que vous
  avez un axe <b>compiler=[gcc,cc].</b>

  Chacune des expressions suivantes filtreront (c-à-d retireront) les builds <b>cc</b> sur <b>linux</b>.
  Selon la façon dont vous envisagez cette contrainte, vous trouverez certaines expressions plus claires
  que d'autres.

  <center>
    <table>
        <tr>
            <td>Lire "si simultanément linux et cc, alors c'est invalide"</td>
            <td>
                <pre>!(label=="linux" && compiler=="cc")</pre>
            </td>
        </tr>
        <tr>
            <td>Lire "pour qu'une combinaison soit valide, elle doit être soit sur solaris, soit utiliser gcc."</td>
            <td>
                <pre>label=="solaris" || compiler=="gcc"</pre>
            </td>
        </tr>
        <tr>
            <td>Lire "quand on est sur solaris, n'utiliser que cc"</td>
            <td>
                <pre>(label=="solaris").implies(compiler=="cc")</pre>
            </td>
        </tr>
    </table>
  </center>

  <h4>Limitation de la matrice</h4>
  <p>
  En plus des règles de filtrage basées sur les valeurs, on peut également utiliser une
  variable spéciale "<tt>index</tt>" qui peut être utilisée pour diminuer la matrice.

  <p>
  Par exemple, <tt>index%2==0</tt> diminuerait la matrice de moitié en retirant 1 combinaison sur 2,
  de façon à ce que la couverture reste raisonnable.
  De même, <tt>index%3!=0</tt> réduirait la matrice à 66% en supprimant 1 combinaison sur 3.
</div>