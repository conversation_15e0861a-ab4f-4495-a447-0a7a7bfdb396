#!/bin/bash

# <PERSON><PERSON>t to fix Jenkins home directory permissions
# Run this script before starting <PERSON> if you encounter permission issues

echo "Fixing Jenkins home directory permissions..."

# Create jenkins_home directory if it doesn't exist
if [ ! -d "./jenkins_home" ]; then
    echo "Creating jenkins_home directory..."
    mkdir -p ./jenkins_home
fi

# Fix ownership and permissions
echo "Setting correct ownership and permissions..."
sudo chown -R 1000:999 ./jenkins_home
sudo chmod -R 755 ./jenkins_home

# Create the copy_reference_file.log if it doesn't exist
if [ ! -f "./jenkins_home/copy_reference_file.log" ]; then
    echo "Creating copy_reference_file.log..."
    touch ./jenkins_home/copy_reference_file.log
    sudo chown 1000:999 ./jenkins_home/copy_reference_file.log
    sudo chmod 644 ./jenkins_home/copy_reference_file.log
fi

echo "Jenkins permissions fixed successfully!"
echo "You can now run: docker-compose up -d jenkins"
