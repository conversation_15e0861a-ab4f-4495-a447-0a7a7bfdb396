<div xmlns="http://www.w3.org/1999/html">
  Définit un pattern (expression régulière) pour vérifier si un nom de job est
  valide ou pas.
  <p>
    Forcer la vérification sur les jobs existants, permet de forcer une
    convention de nommage sur les jobs existants - e.g. même si l'utilisateur ne
    change pas le nom, il sera validé avec le pattern donné lors de toutes les
    soumissions et aucune mise à jour ne sera possible jusqu'à la confirmation
    du nom.
    <br />
    <i>
      Cette option n'affecte pas l'exécution des jobs dont le nom ne respecte
      pas le pattern. Elle ne fait que contrôler le processus de validation lors
      de la sauvegarde des configurations de job.
    </i>
  </p>
</div>
