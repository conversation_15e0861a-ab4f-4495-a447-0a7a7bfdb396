<div>
	<p><b>Trigger - </b>If configured, and trigger conditions are met, an
	email of this type will be sent upon completion of a build.  Click the
	help button next to the trigger name to find out more about what conditions
	will trigger an email of this type to be sent.</p>
	
	<p><b>Send To Recipient List - </b>If this is checked, an email will
	be sent all email addresses in the global recipient list.</p>
	
	<p><b>Send To Committers - </b>If this is checked, an email will
	be sent all users who are listed under "changes" for the build.
	This will use the default email suffix from <PERSON>'s configuration page.</p>
	
	<p><b>Send To Requester - </b>If this is checked, an email will
	be sent user who triggers the build.</p>
	
	<p><b>Include Culprits - </b>If this is checked and Send To Committers
	is checked, emails will include everyone who committed since
	the last successful build.</p>
	
	<p><b>More Configuration - </b>You can change more settings for each
	email trigger by clicking on the "+(expand)" link.</p>
	
	<p><b>Remove - </b>You can remove an email trigger by clicking the
	"Delete" button on the row for the specified trigger.</p>
</div>
