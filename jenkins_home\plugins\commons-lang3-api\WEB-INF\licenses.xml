<?xml version='1.0' encoding='UTF-8'?>
<l:dependencies xmlns:l='licenses' groupId='io.jenkins.plugins' artifactId='commons-lang3-api' version='3.17.0-87.v5cf526e63b_8b_'><l:dependency name='commons-lang3 v3.x Jenkins API Plugin' groupId='io.jenkins.plugins' artifactId='commons-lang3-api' version='3.17.0-87.v5cf526e63b_8b_' url='https://github.com/jenkinsci/commons-lang3-api-plugin'><l:description>The Jenkins Plugins Parent POM Project</l:description><l:license name='Apache-2.0' url='https://opensource.org/licenses/Apache-2.0'/></l:dependency><l:dependency name='Apache Commons Lang' groupId='org.apache.commons' artifactId='commons-lang3' version='3.17.0' url='https://commons.apache.org/proper/commons-lang/'><l:description>Apache Commons Lang, a package of Java utility classes for the
  classes that are in java.lang's hierarchy, or are considered to be so
  standard as to justify existence in java.lang.

  The code is tested using the latest revision of the JDK for supported
  LTS releases: 8, 11, 17 and 21 currently.
  See https://github.com/apache/commons-lang/blob/master/.github/workflows/maven.yml
  
  Please ensure your build environment is up-to-date and kindly report any build issues.</l:description><l:license name='Apache-2.0' url='https://www.apache.org/licenses/LICENSE-2.0.txt'/></l:dependency></l:dependencies>