Manifest-Version: 1.0
Created-By: Maven Archiver 3.6.0
Build-Jdk-Spec: 17
Specification-Title: Credentials Plugin
Specification-Version: 0.0
Implementation-Title: Credentials Plugin
Implementation-Version: 1415.v831096eb_5534
Plugin-Class: com.cloudbees.plugins.credentials.PluginImpl
Group-Id: org.jenkins-ci.plugins
Artifact-Id: credentials
Short-Name: credentials
Long-Name: Credentials Plugin
Url: https://github.com/jenkinsci/credentials-plugin/blob/master/docs/RE
 ADME.adoc
Compatible-Since-Version: 1354
Plugin-Version: 1415.v831096eb_5534
Hudson-Version: 2.479.1
Jenkins-Version: 2.479.1
Plugin-Dependencies: configuration-as-code:1850.va_a_8c31d3158b_;resolut
 ion:=optional,bouncycastle-api:2.30.1.78.1-248.ve27176eb_46cb_,structs:
 338.v848422169819
Plugin-Developers: <PERSON>:stephenconnolly:
Plugin-License-Name: The MIT license
Plugin-License-Url: https://opensource.org/licenses/MIT
Plugin-ScmConnection: scm:git:https://github.com/jenkinsci/credentials-p
 lugin
Plugin-ScmTag: 831096eb553450632960a7c5c90f4320526ced81
Plugin-ScmUrl: https://github.com/jenkinsci/credentials-plugin
Implementation-Build: 831096eb553450632960a7c5c90f4320526ced81

