var culori=(()=>{var Ar=Object.defineProperty;var v0=Object.getOwnPropertyDescriptor;var y0=Object.getOwnPropertyNames;var T0=Object.prototype.hasOwnProperty;var z0=(e,t)=>{for(var r in t)Ar(e,r,{get:t[r],enumerable:!0})},k0=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of y0(t))!T0.call(e,n)&&n!==r&&Ar(e,n,{get:()=>t[n],enumerable:!(o=v0(t,n))||o.enumerable});return e};var L0=e=>k0(Ar({},"__esModule",{value:!0}),e);var Fi={};z0(Fi,{a98:()=>gi,average:()=>Mn,averageAngle:()=>X,averageNumber:()=>Fr,blend:()=>$n,blerp:()=>Wt,clampChroma:()=>t0,clampGamut:()=>Pr,clampRgb:()=>e0,colorsNamed:()=>At,convertA98ToXyz65:()=>at,convertCubehelixToRgb:()=>Vt,convertDlchToLab65:()=>ye,convertHsiToRgb:()=>lt,convertHslToRgb:()=>dt,convertHsvToRgb:()=>ze,convertHwbToRgb:()=>ct,convertItpToXyz65:()=>bt,convertJabToJch:()=>gt,convertJabToRgb:()=>Ye,convertJabToXyz65:()=>Ee,convertJchToJab:()=>Mt,convertLab65ToDlch:()=>Te,convertLab65ToRgb:()=>re,convertLab65ToXyz65:()=>Ce,convertLabToLch:()=>q,convertLabToRgb:()=>Be,convertLabToXyz50:()=>me,convertLchToLab:()=>D,convertLchuvToLuv:()=>yt,convertLrgbToOklab:()=>Fe,convertLrgbToRgb:()=>Y,convertLuvToLchuv:()=>vt,convertLuvToXyz50:()=>_e,convertOkhslToOklab:()=>Ue,convertOkhsvToOklab:()=>Qe,convertOklabToLrgb:()=>U,convertOklabToOkhsl:()=>We,convertOklabToOkhsv:()=>Ke,convertOklabToRgb:()=>ae,convertP3ToXyz65:()=>kt,convertProphotoToXyz50:()=>wt,convertRec2020ToXyz65:()=>Ht,convertRgbToCubehelix:()=>Qt,convertRgbToHsi:()=>pt,convertRgbToHsl:()=>ut,convertRgbToHsv:()=>ke,convertRgbToHwb:()=>ht,convertRgbToJab:()=>je,convertRgbToLab:()=>Ze,convertRgbToLab65:()=>oe,convertRgbToLrgb:()=>j,convertRgbToOklab:()=>ne,convertRgbToXyb:()=>hr,convertRgbToXyz50:()=>W,convertRgbToXyz65:()=>N,convertRgbToYiq:()=>Mr,convertXybToRgb:()=>br,convertXyz50ToLab:()=>se,convertXyz50ToLuv:()=>we,convertXyz50ToProphoto:()=>Rt,convertXyz50ToRgb:()=>F,convertXyz50ToXyz65:()=>gr,convertXyz65ToA98:()=>ft,convertXyz65ToItp:()=>xt,convertXyz65ToJab:()=>Je,convertXyz65ToLab65:()=>$e,convertXyz65ToP3:()=>Lt,convertXyz65ToRec2020:()=>_t,convertXyz65ToRgb:()=>I,convertXyz65ToXyz50:()=>xr,convertYiqToRgb:()=>vr,converter:()=>v,cubehelix:()=>Mi,differenceCie76:()=>mn,differenceCie94:()=>sn,differenceCiede2000:()=>cn,differenceCmc:()=>hn,differenceEuclidean:()=>fe,differenceHueChroma:()=>te,differenceHueNaive:()=>er,differenceHueSaturation:()=>ee,differenceHyab:()=>bn,differenceItp:()=>gn,differenceKotsarenkoRamos:()=>xn,displayable:()=>Sr,dlab:()=>vi,dlch:()=>yi,easingGamma:()=>Xr,easingInOutSine:()=>x0,easingMidpoint:()=>Lr,easingSmootherstep:()=>b0,easingSmoothstep:()=>c0,easingSmoothstepInverse:()=>h0,filterBrightness:()=>n0,filterContrast:()=>a0,filterDeficiencyDeuter:()=>m0,filterDeficiencyProt:()=>u0,filterDeficiencyTrit:()=>s0,filterGrayscale:()=>l0,filterHueRotate:()=>d0,filterInvert:()=>p0,filterSaturate:()=>i0,filterSepia:()=>f0,fixupAlpha:()=>y,fixupHueDecreasing:()=>an,fixupHueIncreasing:()=>nn,fixupHueLonger:()=>on,fixupHueShorter:()=>_,formatCss:()=>Hn,formatHex:()=>Xn,formatHex8:()=>Sn,formatHsl:()=>Cn,formatRgb:()=>Pn,getMode:()=>R,hsi:()=>Ti,hsl:()=>zi,hsv:()=>ki,hwb:()=>Li,inGamut:()=>Ct,interpolate:()=>Dn,interpolateWith:()=>Eo,interpolateWithPremultipliedAlpha:()=>Jn,interpolatorLinear:()=>p,interpolatorPiecewise:()=>Ut,interpolatorSplineBasis:()=>_r,interpolatorSplineBasisClosed:()=>Hr,interpolatorSplineMonotone:()=>Bn,interpolatorSplineMonotone2:()=>Zn,interpolatorSplineMonotoneClosed:()=>Fn,interpolatorSplineNatural:()=>Yn,interpolatorSplineNaturalClosed:()=>Gn,itp:()=>Ri,jab:()=>wi,jch:()=>_i,lab:()=>Hi,lab65:()=>Xi,lch:()=>Si,lch65:()=>Pi,lchuv:()=>Ci,lerp:()=>V,lrgb:()=>$i,luv:()=>Ni,mapAlphaDivide:()=>kr,mapAlphaMultiply:()=>zr,mapTransferGamma:()=>In,mapTransferLinear:()=>Pt,mapper:()=>ce,modeA98:()=>Br,modeCubehelix:()=>Wr,modeDlab:()=>Vr,modeDlch:()=>eo,modeHsi:()=>to,modeHsl:()=>mt,modeHsv:()=>st,modeHwb:()=>ro,modeItp:()=>ao,modeJab:()=>uo,modeJch:()=>mo,modeLab:()=>Le,modeLab65:()=>ho,modeLch:()=>Re,modeLch65:()=>bo,modeLchuv:()=>xo,modeLrgb:()=>go,modeLuv:()=>Mo,modeOkhsl:()=>yo,modeOkhsv:()=>To,modeOklab:()=>zo,modeOklch:()=>ko,modeP3:()=>Lo,modeProphoto:()=>_o,modeRec2020:()=>So,modeRgb:()=>G,modeXyb:()=>$o,modeXyz50:()=>No,modeXyz65:()=>Io,modeYiq:()=>Oo,nearest:()=>o0,okhsl:()=>Ii,okhsv:()=>Oi,oklab:()=>Ai,oklch:()=>qi,p3:()=>Di,parse:()=>Et,parseHex:()=>Dt,parseHsl:()=>ar,parseHslLegacy:()=>nr,parseHwb:()=>fr,parseLab:()=>pr,parseLch:()=>dr,parseNamed:()=>qt,parseOklab:()=>mr,parseOklch:()=>sr,parseRgb:()=>Zt,parseRgbLegacy:()=>Jt,parseTransparent:()=>Ft,prophoto:()=>Ji,random:()=>Nn,rec2020:()=>Ei,removeParser:()=>Vo,rgb:()=>ji,round:()=>yr,samples:()=>Wn,serializeHex:()=>Tr,serializeHex8:()=>qo,serializeHsl:()=>Jo,serializeRgb:()=>Do,toGamut:()=>r0,trilerp:()=>rn,unlerp:()=>tn,useMode:()=>T,useParser:()=>qr,wcagContrast:()=>g0,wcagLuminance:()=>$r,xyb:()=>Yi,xyz50:()=>Gi,xyz65:()=>Bi,yiq:()=>Zi});var R0=(e,t)=>{if(typeof e=="number"){if(t===3)return{mode:"rgb",r:(e>>8&15|e>>4&240)/255,g:(e>>4&15|e&240)/255,b:(e&15|e<<4&240)/255};if(t===4)return{mode:"rgb",r:(e>>12&15|e>>8&240)/255,g:(e>>8&15|e>>4&240)/255,b:(e>>4&15|e&240)/255,alpha:(e&15|e<<4&240)/255};if(t===6)return{mode:"rgb",r:(e>>16&255)/255,g:(e>>8&255)/255,b:(e&255)/255};if(t===8)return{mode:"rgb",r:(e>>24&255)/255,g:(e>>16&255)/255,b:(e>>8&255)/255,alpha:(e&255)/255}}},Ot=R0;var w0={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},At=w0;var _0=e=>Ot(At[e.toLowerCase()],6),qt=_0;var H0=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,X0=e=>{let t;return(t=e.match(H0))?Ot(parseInt(t[1],16),t[1].length):void 0},Dt=X0;var A="([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)",ol=`(?:${A}|none)`,ge=`${A}%`,nl=`(?:${A}%|none)`,ot=`(?:${A}%|${A})`,S0=`(?:${A}%|${A}|none)`,Ko=`(?:${A}(deg|grad|rad|turn)|${A})`,al=`(?:${A}(deg|grad|rad|turn)|${A}|none)`,pe="\\s*,\\s*";var fl=new RegExp("^"+S0+"$");var P0=new RegExp(`^rgba?\\(\\s*${A}${pe}${A}${pe}${A}\\s*(?:,\\s*${ot}\\s*)?\\)$`),C0=new RegExp(`^rgba?\\(\\s*${ge}${pe}${ge}${pe}${ge}\\s*(?:,\\s*${ot}\\s*)?\\)$`),$0=e=>{let t={mode:"rgb"},r;if(r=e.match(P0))r[1]!==void 0&&(t.r=r[1]/255),r[2]!==void 0&&(t.g=r[2]/255),r[3]!==void 0&&(t.b=r[3]/255);else if(r=e.match(C0))r[1]!==void 0&&(t.r=r[1]/100),r[2]!==void 0&&(t.g=r[2]/100),r[3]!==void 0&&(t.b=r[3]/100);else return;return r[4]!==void 0?t.alpha=Math.max(0,Math.min(1,r[4]/100)):r[5]!==void 0&&(t.alpha=Math.max(0,Math.min(1,+r[5]))),t},Jt=$0;var N0=(e,t)=>e===void 0?void 0:typeof e!="object"?Et(e):e.mode!==void 0?e:t?{...e,mode:t}:void 0,P=N0;var I0=(e="rgb")=>t=>(t=P(t,e))!==void 0?t.mode===e?t:K[t.mode][e]?K[t.mode][e](t):e==="rgb"?K[t.mode].rgb(t):K.rgb[e](K[t.mode].rgb(t)):void 0,v=I0;var K={},Qo={},Me=[],jt={},O0=e=>e,T=e=>(K[e.mode]={...K[e.mode],...e.toMode},Object.keys(e.fromMode||{}).forEach(t=>{K[t]||(K[t]={}),K[t][e.mode]=e.fromMode[t]}),e.ranges||(e.ranges={}),e.difference||(e.difference={}),e.channels.forEach(t=>{if(e.ranges[t]===void 0&&(e.ranges[t]=[0,1]),!e.interpolate[t])throw new Error(`Missing interpolator for: ${t}`);typeof e.interpolate[t]=="function"&&(e.interpolate[t]={use:e.interpolate[t]}),e.interpolate[t].fixup||(e.interpolate[t].fixup=O0)}),Qo[e.mode]=e,(e.parse||[]).forEach(t=>{qr(t,e.mode)}),v(e.mode)),R=e=>Qo[e],qr=(e,t)=>{if(typeof e=="string"){if(!t)throw new Error("'mode' required when 'parser' is a string");jt[e]=t}else typeof e=="function"&&Me.indexOf(e)<0&&Me.push(e)},Vo=e=>{if(typeof e=="string")delete jt[e];else if(typeof e=="function"){let t=Me.indexOf(e);t>0&&Me.splice(t,1)}};var Dr=/[^\x00-\x7F]|[a-zA-Z_]/,A0=/[^\x00-\x7F]|[-\w]/,d={Function:"function",Ident:"ident",Number:"number",Percentage:"percentage",ParenClose:")",None:"none",Hue:"hue",Alpha:"alpha"},x=0;function Yt(e){let t=e[x],r=e[x+1];return t==="-"||t==="+"?/\d/.test(r)||r==="."&&/\d/.test(e[x+2]):t==="."?/\d/.test(r):/\d/.test(t)}function Jr(e){if(x>=e.length)return!1;let t=e[x];if(Dr.test(t))return!0;if(t==="-"){if(e.length-x<2)return!1;let r=e[x+1];return!!(r==="-"||Dr.test(r))}return!1}var q0={deg:1,rad:180/Math.PI,grad:9/10,turn:360};function nt(e){let t="";if((e[x]==="-"||e[x]==="+")&&(t+=e[x++]),t+=Gt(e),e[x]==="."&&/\d/.test(e[x+1])&&(t+=e[x++]+Gt(e)),(e[x]==="e"||e[x]==="E")&&((e[x+1]==="-"||e[x+1]==="+")&&/\d/.test(e[x+2])?t+=e[x++]+e[x++]+Gt(e):/\d/.test(e[x+1])&&(t+=e[x++]+Gt(e))),Jr(e)){let r=Bt(e);return r==="deg"||r==="rad"||r==="turn"||r==="grad"?{type:d.Hue,value:t*q0[r]}:void 0}return e[x]==="%"?(x++,{type:d.Percentage,value:+t}):{type:d.Number,value:+t}}function Gt(e){let t="";for(;/\d/.test(e[x]);)t+=e[x++];return t}function Bt(e){let t="";for(;x<e.length&&A0.test(e[x]);)t+=e[x++];return t}function D0(e){let t=Bt(e);return e[x]==="("?(x++,{type:d.Function,value:t}):t==="none"?{type:d.None,value:void 0}:{type:d.Ident,value:t}}function J0(e=""){let t=e.trim(),r=[],o;for(x=0;x<t.length;){if(o=t[x++],o===`
`||o==="	"||o===" "){for(;x<t.length&&(t[x]===`
`||t[x]==="	"||t[x]===" ");)x++;continue}if(o===",")return;if(o===")"){r.push({type:d.ParenClose});continue}if(o==="+"){if(x--,Yt(t)){r.push(nt(t));continue}return}if(o==="-"){if(x--,Yt(t)){r.push(nt(t));continue}if(Jr(t)){r.push({type:d.Ident,value:Bt(t)});continue}return}if(o==="."){if(x--,Yt(t)){r.push(nt(t));continue}return}if(o==="/"){for(;x<t.length&&(t[x]===`
`||t[x]==="	"||t[x]===" ");)x++;let n;if(Yt(t)&&(n=nt(t),n.type!==d.Hue)){r.push({type:d.Alpha,value:n});continue}if(Jr(t)&&Bt(t)==="none"){r.push({type:d.Alpha,value:{type:d.None,value:void 0}});continue}return}if(/\d/.test(o)){x--,r.push(nt(t));continue}if(Dr.test(o)){x--,r.push(D0(t));continue}return}return r}function E0(e){e._i=0;let t=e[e._i++];if(!t||t.type!==d.Function||t.value!=="color"||(t=e[e._i++],t.type!==d.Ident))return;let r=jt[t.value];if(!r)return;let o={mode:r},n=en(e,!1);if(!n)return;let a=R(r).channels;for(let f=0,i,l;f<a.length;f++)i=n[f],l=a[f],i.type!==d.None&&(o[l]=i.type===d.Number?i.value:i.value/100,l==="alpha"&&(o[l]=Math.max(0,Math.min(1,o[l]))));return o}function en(e,t){let r=[],o;for(;e._i<e.length;){if(o=e[e._i++],o.type===d.None||o.type===d.Number||o.type===d.Alpha||o.type===d.Percentage||t&&o.type===d.Hue){r.push(o);continue}if(o.type===d.ParenClose){if(e._i<e.length)return;continue}return}if(!(r.length<3||r.length>4)){if(r.length===4){if(r[3].type!==d.Alpha)return;r[3]=r[3].value}return r.length===3&&r.push({type:d.None,value:void 0}),r.every(n=>n.type!==d.Alpha)?r:void 0}}function j0(e,t){e._i=0;let r=e[e._i++];if(!r||r.type!==d.Function)return;let o=en(e,t);if(o)return o.unshift(r.value),o}var Y0=e=>{if(typeof e!="string")return;let t=J0(e),r=t?j0(t,!0):void 0,o,n=0,a=Me.length;for(;n<a;)if((o=Me[n++](e,r))!==void 0)return o;return t?E0(t):void 0},Et=Y0;function G0(e,t){if(!t||t[0]!=="rgb"&&t[0]!=="rgba")return;let r={mode:"rgb"},[,o,n,a,f]=t;if(!(o.type===d.Hue||n.type===d.Hue||a.type===d.Hue))return o.type!==d.None&&(r.r=o.type===d.Number?o.value/255:o.value/100),n.type!==d.None&&(r.g=n.type===d.Number?n.value/255:n.value/100),a.type!==d.None&&(r.b=a.type===d.Number?a.value/255:a.value/100),f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var Zt=G0;var B0=e=>e==="transparent"?{mode:"rgb",r:0,g:0,b:0,alpha:0}:void 0,Ft=B0;var V=(e,t,r)=>e+r*(t-e),tn=(e,t,r)=>(r-e)/(t-e),Wt=(e,t,r,o,n,a)=>V(V(e,t,n),V(r,o,n),a),rn=(e,t,r,o,n,a,f,i,l,s,u)=>V(Wt(e,t,r,o,l,s),Wt(n,a,f,i,l,s),u);var Z0=e=>{let t=[];for(let r=0;r<e.length-1;r++){let o=e[r],n=e[r+1];o===void 0&&n===void 0?t.push(void 0):o!==void 0&&n!==void 0?t.push([o,n]):t.push(o!==void 0?[o,o]:[n,n])}return t},Ut=e=>t=>{let r=Z0(t);return o=>{let n=o*r.length,a=o>=1?r.length-1:Math.max(Math.floor(n),0),f=r[a];return f===void 0?void 0:e(f[0],f[1],n-a)}};var p=Ut(V);var y=e=>{let t=!1,r=e.map(o=>o!==void 0?(t=!0,o):1);return t?r:e};var F0={mode:"rgb",channels:["r","g","b","alpha"],parse:[Zt,Dt,Jt,qt,Ft,"srgb"],serialize:"srgb",interpolate:{r:p,g:p,b:p,alpha:{use:p,fixup:y}},gamut:!0,white:{r:1,g:1,b:1},black:{r:0,g:0,b:0}},G=F0;var Er=(e=0)=>Math.pow(Math.abs(e),2.19921875)*Math.sign(e),W0=e=>{let t=Er(e.r),r=Er(e.g),o=Er(e.b),n={mode:"xyz65",x:.5766690429101305*t+.1855582379065463*r+.1882286462349947*o,y:.297344975250536*t+.6273635662554661*r+.0752914584939979*o,z:.0270313613864123*t+.0706888525358272*r+.9913375368376386*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},at=W0;var jr=e=>Math.pow(Math.abs(e),.4547069271758437)*Math.sign(e),U0=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n={mode:"a98",r:jr(e*2.0415879038107465-t*.5650069742788597-.3447313507783297*r),g:jr(e*-.9692436362808798+t*1.8759675015077206+.0415550574071756*r),b:jr(e*.0134442806320312-t*.1183623922310184+1.0151749943912058*r)};return o!==void 0&&(n.alpha=o),n},ft=U0;var Yr=(e=0)=>{let t=Math.abs(e);return t<=.04045?e/12.92:(Math.sign(e)||1)*Math.pow((t+.055)/1.055,2.4)},K0=({r:e,g:t,b:r,alpha:o})=>{let n={mode:"lrgb",r:Yr(e),g:Yr(t),b:Yr(r)};return o!==void 0&&(n.alpha=o),n},j=K0;var Q0=e=>{let{r:t,g:r,b:o,alpha:n}=j(e),a={mode:"xyz65",x:.4123907992659593*t+.357584339383878*r+.1804807884018343*o,y:.2126390058715102*t+.715168678767756*r+.0721923153607337*o,z:.0193308187155918*t+.119194779794626*r+.9505321522496607*o};return n!==void 0&&(a.alpha=n),a},N=Q0;var Gr=(e=0)=>{let t=Math.abs(e);return t>.0031308?(Math.sign(e)||1)*(1.055*Math.pow(t,.4166666666666667)-.055):e*12.92},V0=({r:e,g:t,b:r,alpha:o},n="rgb")=>{let a={mode:n,r:Gr(e),g:Gr(t),b:Gr(r)};return o!==void 0&&(a.alpha=o),a},Y=V0;var ea=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Y({r:e*3.2409699419045226-t*1.537383177570094-.4986107602930034*r,g:e*-.9692436362808796+t*1.8759675015077204+.0415550574071756*r,b:e*.0556300796969936-t*.2039769588889765+1.0569715142428784*r});return o!==void 0&&(n.alpha=o),n},I=ea;var ta={...G,mode:"a98",parse:["a98-rgb"],serialize:"a98-rgb",fromMode:{rgb:e=>ft(N(e)),xyz65:ft},toMode:{rgb:e=>I(at(e)),xyz65:at}},Br=ta;var ra=e=>(e=e%360)<0?e+360:e,k=ra;var Kt=(e,t)=>e.map((r,o,n)=>{if(r===void 0)return r;let a=k(r);return o===0||e[o-1]===void 0?a:t(a-k(n[o-1]))}).reduce((r,o)=>!r.length||o===void 0||r[r.length-1]===void 0?(r.push(o),r):(r.push(o+r[r.length-1]),r),[]),_=e=>Kt(e,t=>Math.abs(t)<=180?t:t-360*Math.sign(t)),on=e=>Kt(e,t=>Math.abs(t)>=180||t===0?t:t-360*Math.sign(t)),nn=e=>Kt(e,t=>t>=0?t:t+360),an=e=>Kt(e,t=>t<=0?t:t-360);var H=[-.14861,1.78277,-.29227,-.90649,1.97294,0],fn=Math.PI/180,ln=180/Math.PI;var pn=H[3]*H[4],dn=H[1]*H[4],un=H[1]*H[2]-H[0]*H[3],oa=({r:e,g:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=(un*r+e*pn-t*dn)/(un+pn-dn),a=r-n,f=(H[4]*(t-n)-H[2]*a)/H[3],i={mode:"cubehelix",l:n,s:n===0||n===1?void 0:Math.sqrt(a*a+f*f)/(H[4]*n*(1-n))};return i.s&&(i.h=Math.atan2(f,a)*ln-120),o!==void 0&&(i.alpha=o),i},Qt=oa;var na=({h:e,s:t,l:r,alpha:o})=>{let n={mode:"rgb"};e=(e===void 0?0:e+120)*fn,r===void 0&&(r=0);let a=t===void 0?0:t*r*(1-r),f=Math.cos(e),i=Math.sin(e);return n.r=r+a*(H[0]*f+H[1]*i),n.g=r+a*(H[2]*f+H[3]*i),n.b=r+a*(H[4]*f+H[5]*i),o!==void 0&&(n.alpha=o),n},Vt=na;var ee=(e,t)=>{if(e.h===void 0||t.h===void 0||!e.s||!t.s)return 0;let r=k(e.h),o=k(t.h),n=Math.sin((o-r+360)/2*Math.PI/180);return 2*Math.sqrt(e.s*t.s)*n},er=(e,t)=>{if(e.h===void 0||t.h===void 0)return 0;let r=k(e.h),o=k(t.h);return Math.abs(o-r)>180?r-(o-360*Math.sign(o-r)):o-r},te=(e,t)=>{if(e.h===void 0||t.h===void 0||!e.c||!t.c)return 0;let r=k(e.h),o=k(t.h),n=Math.sin((o-r+360)/2*Math.PI/180);return 2*Math.sqrt(e.c*t.c)*n},fe=(e="rgb",t=[1,1,1,0])=>{let r=R(e),o=r.channels,n=r.difference,a=v(e);return(f,i)=>{let l=a(f),s=a(i);return Math.sqrt(o.reduce((u,m,h)=>{let c=n[m]?n[m](l,s):l[m]-s[m];return u+(t[h]||0)*Math.pow(isNaN(c)?0:c,2)},0))}},mn=()=>fe("lab65"),sn=(e=1,t=.045,r=.015)=>{let o=v("lab65");return(n,a)=>{let f=o(n),i=o(a),l=f.l,s=f.a,u=f.b,m=Math.sqrt(s*s+u*u),h=i.l,c=i.a,b=i.b,M=Math.sqrt(c*c+b*b),g=Math.pow(l-h,2),z=Math.pow(m-M,2),L=Math.pow(s-c,2)+Math.pow(u-b,2)-z;return Math.sqrt(g/Math.pow(e,2)+z/Math.pow(1+t*m,2)+L/Math.pow(1+r*m,2))}},cn=(e=1,t=1,r=1)=>{let o=v("lab65");return(n,a)=>{let f=o(n),i=o(a),l=f.l,s=f.a,u=f.b,m=Math.sqrt(s*s+u*u),h=i.l,c=i.a,b=i.b,M=Math.sqrt(c*c+b*b),g=(m+M)/2,z=.5*(1-Math.sqrt(Math.pow(g,7)/(Math.pow(g,7)+Math.pow(25,7)))),L=s*(1+z),S=c*(1+z),C=Math.sqrt(L*L+u*u),$=Math.sqrt(S*S+b*b),O=Math.abs(L)+Math.abs(u)===0?0:Math.atan2(u,L);O+=(O<0)*2*Math.PI;let J=Math.abs(S)+Math.abs(b)===0?0:Math.atan2(b,S);J+=(J<0)*2*Math.PI;let he=h-l,le=$-C,Z=C*$===0?0:J-O;Z-=(Z>Math.PI)*2*Math.PI,Z+=(Z<-Math.PI)*2*Math.PI;let Q=2*Math.sqrt(C*$)*Math.sin(Z/2),be=(l+h)/2,xe=(C+$)/2,E;C*$===0?E=O+J:(E=(O+J)/2,E-=(Math.abs(O-J)>Math.PI)*Math.PI,E+=(E<0)*2*Math.PI);let $t=Math.pow(be-50,2),Nt=1-.17*Math.cos(E-Math.PI/6)+.24*Math.cos(2*E)+.32*Math.cos(3*E+Math.PI/30)-.2*Math.cos(4*E-63*Math.PI/180),et=1+.015*$t/Math.sqrt(20+$t),tt=1+.045*xe,Pe=1+.015*xe*Nt,Nr=30*Math.PI/180*Math.exp(-1*Math.pow((180/Math.PI*E-275)/25,2)),It=2*Math.sqrt(Math.pow(xe,7)/(Math.pow(xe,7)+Math.pow(25,7))),rt=-1*Math.sin(2*Nr)*It;return Math.sqrt(Math.pow(he/(e*et),2)+Math.pow(le/(t*tt),2)+Math.pow(Q/(r*Pe),2)+rt*le/(t*tt)*Q/(r*Pe))}},hn=(e=1,t=1)=>{let r=v("lab65");return(o,n)=>{let a=r(o),f=a.l,i=a.a,l=a.b,s=Math.sqrt(i*i+l*l),u=Math.atan2(l,i);u=u+2*Math.PI*(u<0);let m=r(n),h=m.l,c=m.a,b=m.b,M=Math.sqrt(c*c+b*b),g=Math.pow(f-h,2),z=Math.pow(s-M,2),L=Math.pow(i-c,2)+Math.pow(l-b,2)-z,S=Math.sqrt(Math.pow(s,4)/(Math.pow(s,4)+1900)),C=u>=164/180*Math.PI&&u<=345/180*Math.PI?.56+Math.abs(.2*Math.cos(u+168/180*Math.PI)):.36+Math.abs(.4*Math.cos(u+35/180*Math.PI)),$=f<16?.511:.040975*f/(1+.01765*f),O=.0638*s/(1+.0131*s)+.638,J=O*(S*C+1-S);return Math.sqrt(g/Math.pow(e*$,2)+z/Math.pow(t*O,2)+L/Math.pow(J,2))}},bn=()=>{let e=v("lab65");return(t,r)=>{let o=e(t),n=e(r),a=o.l-n.l,f=o.a-n.a,i=o.b-n.b;return Math.abs(a)+Math.sqrt(f*f+i*i)}},xn=()=>fe("yiq",[.5053,.299,.1957]),gn=()=>fe("itp",[518400,129600,518400]);var X=e=>{let t=e.reduce((o,n)=>{if(n!==void 0){let a=n*Math.PI/180;o.sin+=Math.sin(a),o.cos+=Math.cos(a)}return o},{sin:0,cos:0}),r=Math.atan2(t.sin,t.cos)*180/Math.PI;return r<0?360+r:r},Fr=e=>{let t=e.filter(r=>r!==void 0);return t.length?t.reduce((r,o)=>r+o,0)/t.length:void 0},Zr=e=>typeof e=="function";function Mn(e,t="rgb",r){let o=R(t),n=e.map(v(t));return o.channels.reduce((a,f)=>{let i=n.map(l=>l[f]).filter(l=>l!==void 0);if(i.length){let l;Zr(r)?l=r:r&&Zr(r[f])?l=r[f]:o.average&&Zr(o.average[f])?l=o.average[f]:l=Fr,a[f]=l(i,f)}return a},{mode:t})}var aa={mode:"cubehelix",channels:["h","s","l","alpha"],parse:["--cubehelix"],serialize:"--cubehelix",ranges:{h:[0,360],s:[0,4.614],l:[0,1]},fromMode:{rgb:Qt},toMode:{rgb:Vt},interpolate:{h:{use:p,fixup:_},s:p,l:p,alpha:{use:p,fixup:y}},difference:{h:ee},average:{h:X}},Wr=aa;var fa=({l:e,a:t,b:r,alpha:o},n="lch")=>{t===void 0&&(t=0),r===void 0&&(r=0);let a=Math.sqrt(t*t+r*r),f={mode:n,l:e,c:a};return a&&(f.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(f.alpha=o),f},q=fa;var ia=({l:e,c:t,h:r,alpha:o},n="lab")=>{r===void 0&&(r=0);let a={mode:n,l:e,a:t?t*Math.cos(r/180*Math.PI):0,b:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(a.alpha=o),a},D=ia;var tr=Math.pow(29,3)/Math.pow(3,3),rr=Math.pow(6,3)/Math.pow(29,3);var w={X:.9642956764295677,Y:1,Z:.8251046025104602},de={X:.3127/.329,Y:1,Z:(1-.3127-.329)/.329},zp=Math.pow(29,3)/Math.pow(3,3),kp=Math.pow(6,3)/Math.pow(29,3);var Ur=e=>Math.pow(e,3)>rr?Math.pow(e,3):(116*e-16)/tr,la=({l:e,a:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=(e+16)/116,a=t/500+n,f=n-r/200,i={mode:"xyz65",x:Ur(a)*de.X,y:Ur(n)*de.Y,z:Ur(f)*de.Z};return o!==void 0&&(i.alpha=o),i},Ce=la;var pa=e=>I(Ce(e)),re=pa;var Kr=e=>e>rr?Math.cbrt(e):(tr*e+16)/116,da=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Kr(e/de.X),a=Kr(t/de.Y),f=Kr(r/de.Z),i={mode:"lab65",l:116*a-16,a:500*(n-a),b:200*(a-f)};return o!==void 0&&(i.alpha=o),i},$e=da;var ua=e=>{let t=$e(N(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},oe=ua;var ve=.14444444444444443*Math.PI,Ne=Math.cos(ve),Ie=Math.sin(ve),or=100/Math.log(139/100);var ma=({l:e,c:t,h:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n={mode:"lab65",l:(Math.exp(e*1/or)-1)/.0039},a=(Math.exp(.0435*t*1*1)-1)/.075,f=a*Math.cos(r/180*Math.PI-ve),i=a*Math.sin(r/180*Math.PI-ve);return n.a=f*Ne-i/.83*Ie,n.b=f*Ie+i/.83*Ne,o!==void 0&&(n.alpha=o),n},ye=ma;var sa=({l:e,a:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=t*Ne+r*Ie,a=.83*(r*Ne-t*Ie),f=Math.sqrt(n*n+a*a),i={mode:"dlch",l:or/1*Math.log(1+.0039*e),c:Math.log(1+.075*f)/(.0435*1*1)};return i.c&&(i.h=k((Math.atan2(a,n)+ve)/Math.PI*180)),o!==void 0&&(i.alpha=o),i},Te=sa;var vn=e=>ye(q(e,"dlch")),yn=e=>D(Te(e),"dlab"),ca={mode:"dlab",parse:["--din99o-lab"],serialize:"--din99o-lab",toMode:{lab65:vn,rgb:e=>re(vn(e))},fromMode:{lab65:yn,rgb:e=>yn(oe(e))},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-40.09,45.501],b:[-40.469,44.344]},interpolate:{l:p,a:p,b:p,alpha:{use:p,fixup:y}}},Vr=ca;var ha={mode:"dlch",parse:["--din99o-lch"],serialize:"--din99o-lch",toMode:{lab65:ye,dlab:e=>D(e,"dlab"),rgb:e=>re(ye(e))},fromMode:{lab65:Te,dlab:e=>q(e,"dlch"),rgb:e=>Te(oe(e))},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,51.484],h:[0,360]},interpolate:{l:p,c:p,h:{use:p,fixup:_},alpha:{use:p,fixup:y}},difference:{h:te},average:{h:X}},eo=ha;function lt({h:e,s:t,i:r,alpha:o}){e=k(e!==void 0?e:0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.abs(e/60%2-1),a;switch(Math.floor(e/60)){case 0:a={r:r*(1+t*(3/(2-n)-1)),g:r*(1+t*(3*(1-n)/(2-n)-1)),b:r*(1-t)};break;case 1:a={r:r*(1+t*(3*(1-n)/(2-n)-1)),g:r*(1+t*(3/(2-n)-1)),b:r*(1-t)};break;case 2:a={r:r*(1-t),g:r*(1+t*(3/(2-n)-1)),b:r*(1+t*(3*(1-n)/(2-n)-1))};break;case 3:a={r:r*(1-t),g:r*(1+t*(3*(1-n)/(2-n)-1)),b:r*(1+t*(3/(2-n)-1))};break;case 4:a={r:r*(1+t*(3*(1-n)/(2-n)-1)),g:r*(1-t),b:r*(1+t*(3/(2-n)-1))};break;case 5:a={r:r*(1+t*(3/(2-n)-1)),g:r*(1-t),b:r*(1+t*(3*(1-n)/(2-n)-1))};break;default:a={r:r*(1-t),g:r*(1-t),b:r*(1-t)}}return a.mode="rgb",o!==void 0&&(a.alpha=o),a}function pt({r:e,g:t,b:r,alpha:o}){e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsi",s:e+t+r===0?0:1-3*a/(e+t+r),i:(e+t+r)/3};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var ba={mode:"hsi",toMode:{rgb:lt},parse:["--hsi"],serialize:"--hsi",fromMode:{rgb:pt},channels:["h","s","i","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:p,fixup:_},s:p,i:p,alpha:{use:p,fixup:y}},difference:{h:ee},average:{h:X}},to=ba;function dt({h:e,s:t,l:r,alpha:o}){e=k(e!==void 0?e:0),t===void 0&&(t=0),r===void 0&&(r=0);let n=r+t*(r<.5?r:1-r),a=n-(n-r)*2*Math.abs(e/60%2-1),f;switch(Math.floor(e/60)){case 0:f={r:n,g:a,b:2*r-n};break;case 1:f={r:a,g:n,b:2*r-n};break;case 2:f={r:2*r-n,g:n,b:a};break;case 3:f={r:2*r-n,g:a,b:n};break;case 4:f={r:a,g:2*r-n,b:n};break;case 5:f={r:n,g:2*r-n,b:a};break;default:f={r:2*r-n,g:2*r-n,b:2*r-n}}return f.mode="rgb",o!==void 0&&(f.alpha=o),f}function ut({r:e,g:t,b:r,alpha:o}){e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsl",s:n===a?0:(n-a)/(1-Math.abs(n+a-1)),l:.5*(n+a)};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var xa=(e,t)=>{switch(t){case"deg":return+e;case"rad":return e/Math.PI*180;case"grad":return e/10*9;case"turn":return e*360}},Tn=xa;var ga=new RegExp(`^hsla?\\(\\s*${Ko}${pe}${ge}${pe}${ge}\\s*(?:,\\s*${ot}\\s*)?\\)$`),Ma=e=>{let t=e.match(ga);if(!t)return;let r={mode:"hsl"};return t[3]!==void 0?r.h=+t[3]:t[1]!==void 0&&t[2]!==void 0&&(r.h=Tn(t[1],t[2])),t[4]!==void 0&&(r.s=Math.min(Math.max(0,t[4]/100),1)),t[5]!==void 0&&(r.l=Math.min(Math.max(0,t[5]/100),1)),t[6]!==void 0?r.alpha=Math.max(0,Math.min(1,t[6]/100)):t[7]!==void 0&&(r.alpha=Math.max(0,Math.min(1,+t[7]))),r},nr=Ma;function va(e,t){if(!t||t[0]!=="hsl"&&t[0]!=="hsla")return;let r={mode:"hsl"},[,o,n,a,f]=t;if(o.type!==d.None){if(o.type===d.Percentage)return;r.h=o.value}if(n.type!==d.None){if(n.type===d.Hue)return;r.s=n.value/100}if(a.type!==d.None){if(a.type===d.Hue)return;r.l=a.value/100}return f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var ar=va;var ya={mode:"hsl",toMode:{rgb:dt},fromMode:{rgb:ut},channels:["h","s","l","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[ar,nr],serialize:e=>`hsl(${e.h!==void 0?e.h:"none"} ${e.s!==void 0?e.s*100+"%":"none"} ${e.l!==void 0?e.l*100+"%":"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:p,fixup:_},s:p,l:p,alpha:{use:p,fixup:y}},difference:{h:ee},average:{h:X}},mt=ya;function ze({h:e,s:t,v:r,alpha:o}){e=k(e!==void 0?e:0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.abs(e/60%2-1),a;switch(Math.floor(e/60)){case 0:a={r,g:r*(1-t*n),b:r*(1-t)};break;case 1:a={r:r*(1-t*n),g:r,b:r*(1-t)};break;case 2:a={r:r*(1-t),g:r,b:r*(1-t*n)};break;case 3:a={r:r*(1-t),g:r*(1-t*n),b:r};break;case 4:a={r:r*(1-t*n),g:r*(1-t),b:r};break;case 5:a={r,g:r*(1-t),b:r*(1-t*n)};break;default:a={r:r*(1-t),g:r*(1-t),b:r*(1-t)}}return a.mode="rgb",o!==void 0&&(a.alpha=o),a}function ke({r:e,g:t,b:r,alpha:o}){e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsv",s:n===0?0:1-a/n,v:n};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var Ta={mode:"hsv",toMode:{rgb:ze},parse:["--hsv"],serialize:"--hsv",fromMode:{rgb:ke},channels:["h","s","v","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:p,fixup:_},s:p,v:p,alpha:{use:p,fixup:y}},difference:{h:ee},average:{h:X}},st=Ta;function ct({h:e,w:t,b:r,alpha:o}){if(t===void 0&&(t=0),r===void 0&&(r=0),t+r>1){let n=t+r;t/=n,r/=n}return ze({h:e,s:r===1?1:1-t/(1-r),v:1-r,alpha:o})}function ht(e){let t=ke(e);if(t===void 0)return;let r=t.s!==void 0?t.s:0,o=t.v!==void 0?t.v:0,n={mode:"hwb",w:(1-r)*o,b:1-o};return t.h!==void 0&&(n.h=t.h),t.alpha!==void 0&&(n.alpha=t.alpha),n}function za(e,t){if(!t||t[0]!=="hwb")return;let r={mode:"hwb"},[,o,n,a,f]=t;if(o.type!==d.None){if(o.type===d.Percentage)return;r.h=o.value}if(n.type!==d.None){if(n.type===d.Hue)return;r.w=n.value/100}if(a.type!==d.None){if(a.type===d.Hue)return;r.b=a.value/100}return f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var fr=za;var ka={mode:"hwb",toMode:{rgb:ct},fromMode:{rgb:ht},channels:["h","w","b","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[fr],serialize:e=>`hwb(${e.h!==void 0?e.h:"none"} ${e.w!==void 0?e.w*100+"%":"none"} ${e.b!==void 0?e.b*100+"%":"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:p,fixup:_},w:p,b:p,alpha:{use:p,fixup:y}},difference:{h:er},average:{h:X}},ro=ka;var Oe=.1593017578125,zn=78.84375,Ae=.8359375,qe=18.8515625,De=18.6875;function ir(e){if(e<0)return 0;let t=Math.pow(e,1/zn);return 1e4*Math.pow(Math.max(0,t-Ae)/(qe-De*t),1/Oe)}function lr(e){if(e<0)return 0;let t=Math.pow(e/1e4,Oe);return Math.pow((Ae+qe*t)/(1+De*t),zn)}var oo=e=>Math.max(e/203,0),Ra=({i:e,t,p:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=ir(e+.008609037037932761*t+.11102962500302593*r),a=ir(e-.00860903703793275*t-.11102962500302599*r),f=ir(e+.5600313357106791*t-.32062717498731885*r),i={mode:"xyz65",x:oo(2.070152218389422*n-1.3263473389671556*a+.2066510476294051*f),y:oo(.3647385209748074*n+.680566024947227*a-.0453045459220346*f),z:oo(-.049747207535812*n-.0492609666966138*a+1.1880659249923042*f)};return o!==void 0&&(i.alpha=o),i},bt=Ra;var no=(e=0)=>Math.max(e*203,0),wa=({x:e,y:t,z:r,alpha:o})=>{let n=no(e),a=no(t),f=no(r),i=lr(.3592832590121217*n+.6976051147779502*a-.0358915932320289*f),l=lr(-.1920808463704995*n+1.1004767970374323*a+.0753748658519118*f),s=lr(.0070797844607477*n+.0748396662186366*a+.8433265453898765*f),u=.5*i+.5*l,m=1.61376953125*i-3.323486328125*l+1.709716796875*s,h=4.378173828125*i-4.24560546875*l-.132568359375*s,c={mode:"itp",i:u,t:m,p:h};return o!==void 0&&(c.alpha=o),c},xt=wa;var _a={mode:"itp",channels:["i","t","p","alpha"],parse:["--ictcp"],serialize:"--ictcp",toMode:{xyz65:bt,rgb:e=>I(bt(e))},fromMode:{xyz65:xt,rgb:e=>xt(N(e))},ranges:{i:[0,.581],t:[-.369,.272],p:[-.164,.331]},interpolate:{i:p,t:p,p,alpha:{use:p,fixup:y}}},ao=_a;var Ha=134.03437499999998,Xa=16295499532821565e-27,fo=e=>{if(e<0)return 0;let t=Math.pow(e/1e4,Oe);return Math.pow((Ae+qe*t)/(1+De*t),Ha)},io=(e=0)=>Math.max(e*203,0),Sa=({x:e,y:t,z:r,alpha:o})=>{e=io(e),t=io(t),r=io(r);let n=1.15*e-.15*r,a=.66*t+.34*e,f=fo(.41478972*n+.579999*a+.014648*r),i=fo(-.20151*n+1.120649*a+.0531008*r),l=fo(-.0166008*n+.2648*a+.6684799*r),s=(f+i)/2,u={mode:"jab",j:.44*s/(1-.56*s)-Xa,a:3.524*f-4.066708*i+.542708*l,b:.199076*f+1.096799*i-1.295875*l};return o!==void 0&&(u.alpha=o),u},Je=Sa;var Pa=134.03437499999998,kn=16295499532821565e-27,lo=e=>{if(e<0)return 0;let t=Math.pow(e,1/Pa);return 1e4*Math.pow((Ae-t)/(De*t-qe),1/Oe)},po=e=>e/203,Ca=({j:e,a:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=(e+kn)/(.44+.56*(e+kn)),a=lo(n+.13860504*t+.058047316*r),f=lo(n-.13860504*t-.058047316*r),i=lo(n-.096019242*t-.8118919*r),l={mode:"xyz65",x:po(1.661373024652174*a-.914523081304348*f+.23136208173913045*i),y:po(-.3250758611844533*a+1.571847026732543*f-.21825383453227928*i),z:po(-.090982811*a-.31272829*f+1.5227666*i)};return o!==void 0&&(l.alpha=o),l},Ee=Ca;var $a=e=>{let t=Je(N(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},je=$a;var Na=e=>I(Ee(e)),Ye=Na;var Ia={mode:"jab",channels:["j","a","b","alpha"],parse:["--jzazbz"],serialize:"--jzazbz",fromMode:{rgb:je,xyz65:Je},toMode:{rgb:Ye,xyz65:Ee},ranges:{j:[0,.222],a:[-.109,.129],b:[-.185,.134]},interpolate:{j:p,a:p,b:p,alpha:{use:p,fixup:y}}},uo=Ia;var Oa=({j:e,a:t,b:r,alpha:o})=>{t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.sqrt(t*t+r*r),a={mode:"jch",j:e,c:n};return n&&(a.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(a.alpha=o),a},gt=Oa;var Aa=({j:e,c:t,h:r,alpha:o})=>{r===void 0&&(r=0);let n={mode:"jab",j:e,a:t?t*Math.cos(r/180*Math.PI):0,b:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(n.alpha=o),n},Mt=Aa;var qa={mode:"jch",parse:["--jzczhz"],serialize:"--jzczhz",toMode:{jab:Mt,rgb:e=>Ye(Mt(e))},fromMode:{rgb:e=>gt(je(e)),jab:gt},channels:["j","c","h","alpha"],ranges:{j:[0,.221],c:[0,.19],h:[0,360]},interpolate:{h:{use:p,fixup:_},c:p,j:p,alpha:{use:p,fixup:y}},difference:{h:te},average:{h:X}},mo=qa;var ue=Math.pow(29,3)/Math.pow(3,3),Ge=Math.pow(6,3)/Math.pow(29,3);var so=e=>Math.pow(e,3)>Ge?Math.pow(e,3):(116*e-16)/ue,Da=({l:e,a:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=(e+16)/116,a=t/500+n,f=n-r/200,i={mode:"xyz50",x:so(a)*w.X,y:so(n)*w.Y,z:so(f)*w.Z};return o!==void 0&&(i.alpha=o),i},me=Da;var Ja=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Y({r:e*3.1341359569958707-t*1.6173863321612538-.4906619460083532*r,g:e*-.978795502912089+t*1.916254567259524+.03344273116131949*r,b:e*.07195537988411677-t*.2289768264158322+1.405386058324125*r});return o!==void 0&&(n.alpha=o),n},F=Ja;var Ea=e=>F(me(e)),Be=Ea;var ja=e=>{let{r:t,g:r,b:o,alpha:n}=j(e),a={mode:"xyz50",x:.436065742824811*t+.3851514688337912*r+.14307845442264197*o,y:.22249319175623702*t+.7168870538238823*r+.06061979053616537*o,z:.013923904500943465*t+.09708128566574634*r+.7140993584005155*o};return n!==void 0&&(a.alpha=n),a},W=ja;var co=e=>e>Ge?Math.cbrt(e):(ue*e+16)/116,Ya=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=co(e/w.X),a=co(t/w.Y),f=co(r/w.Z),i={mode:"lab",l:116*a-16,a:500*(n-a),b:200*(a-f)};return o!==void 0&&(i.alpha=o),i},se=Ya;var Ga=e=>{let t=se(W(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},Ze=Ga;function Ba(e,t){if(!t||t[0]!=="lab")return;let r={mode:"lab"},[,o,n,a,f]=t;if(!(o.type===d.Hue||n.type===d.Hue||a.type===d.Hue))return o.type!==d.None&&(r.l=Math.min(Math.max(0,o.value),100)),n.type!==d.None&&(r.a=n.type===d.Number?n.value:n.value*125/100),a.type!==d.None&&(r.b=a.type===d.Number?a.value:a.value*125/100),f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var pr=Ba;var Za={mode:"lab",toMode:{xyz50:me,rgb:Be},fromMode:{xyz50:se,rgb:Ze},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-100,100],b:[-100,100]},parse:[pr],serialize:e=>`lab(${e.l!==void 0?e.l:"none"} ${e.a!==void 0?e.a:"none"} ${e.b!==void 0?e.b:"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{l:p,a:p,b:p,alpha:{use:p,fixup:y}}},Le=Za;var Fa={...Le,mode:"lab65",parse:["--lab-d65"],serialize:"--lab-d65",toMode:{xyz65:Ce,rgb:re},fromMode:{xyz65:$e,rgb:oe},ranges:{l:[0,100],a:[-86.182,98.234],b:[-107.86,94.477]}},ho=Fa;function Wa(e,t){if(!t||t[0]!=="lch")return;let r={mode:"lch"},[,o,n,a,f]=t;if(o.type!==d.None){if(o.type===d.Hue)return;r.l=Math.min(Math.max(0,o.value),100)}if(n.type!==d.None&&(r.c=Math.max(0,n.type===d.Number?n.value:n.value*150/100)),a.type!==d.None){if(a.type===d.Percentage)return;r.h=a.value}return f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var dr=Wa;var Ua={mode:"lch",toMode:{lab:D,rgb:e=>Be(D(e))},fromMode:{rgb:e=>q(Ze(e)),lab:q},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,150],h:[0,360]},parse:[dr],serialize:e=>`lch(${e.l!==void 0?e.l:"none"} ${e.c!==void 0?e.c:"none"} ${e.h!==void 0?e.h:"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:p,fixup:_},c:p,l:p,alpha:{use:p,fixup:y}},difference:{h:te},average:{h:X}},Re=Ua;var Ka={...Re,mode:"lch65",parse:["--lch-d65"],serialize:"--lch-d65",toMode:{lab65:e=>D(e,"lab65"),rgb:e=>re(D(e,"lab65"))},fromMode:{rgb:e=>q(oe(e),"lch65"),lab65:e=>q(e,"lch65")},ranges:{l:[0,100],c:[0,133.807],h:[0,360]}},bo=Ka;var Qa=({l:e,u:t,v:r,alpha:o})=>{t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.sqrt(t*t+r*r),a={mode:"lchuv",l:e,c:n};return n&&(a.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(a.alpha=o),a},vt=Qa;var Va=({l:e,c:t,h:r,alpha:o})=>{r===void 0&&(r=0);let n={mode:"luv",l:e,u:t?t*Math.cos(r/180*Math.PI):0,v:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(n.alpha=o),n},yt=Va;var Ln=(e,t,r)=>4*e/(e+15*t+3*r),Rn=(e,t,r)=>9*t/(e+15*t+3*r),ef=Ln(w.X,w.Y,w.Z),tf=Rn(w.X,w.Y,w.Z),rf=e=>e<=Ge?ue*e:116*Math.cbrt(e)-16,of=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=rf(t/w.Y),a=Ln(e,t,r),f=Rn(e,t,r);!isFinite(a)||!isFinite(f)?n=a=f=0:(a=13*n*(a-ef),f=13*n*(f-tf));let i={mode:"luv",l:n,u:a,v:f};return o!==void 0&&(i.alpha=o),i},we=of;var nf=(e,t,r)=>4*e/(e+15*t+3*r),af=(e,t,r)=>9*t/(e+15*t+3*r),ff=nf(w.X,w.Y,w.Z),lf=af(w.X,w.Y,w.Z),pf=({l:e,u:t,v:r,alpha:o})=>{if(e===void 0&&(e=0),e===0)return{mode:"xyz50",x:0,y:0,z:0};t===void 0&&(t=0),r===void 0&&(r=0);let n=t/(13*e)+ff,a=r/(13*e)+lf,f=w.Y*(e<=8?e/ue:Math.pow((e+16)/116,3)),i=f*(9*n)/(4*a),l=f*(12-3*n-20*a)/(4*a),s={mode:"xyz50",x:i,y:f,z:l};return o!==void 0&&(s.alpha=o),s},_e=pf;var df=e=>vt(we(W(e))),uf=e=>F(_e(yt(e))),mf={mode:"lchuv",toMode:{luv:yt,rgb:uf},fromMode:{rgb:df,luv:vt},channels:["l","c","h","alpha"],parse:["--lchuv"],serialize:"--lchuv",ranges:{l:[0,100],c:[0,176.956],h:[0,360]},interpolate:{h:{use:p,fixup:_},c:p,l:p,alpha:{use:p,fixup:y}},difference:{h:te},average:{h:X}},xo=mf;var sf={...G,mode:"lrgb",toMode:{rgb:Y},fromMode:{rgb:j},parse:["srgb-linear"],serialize:"srgb-linear"},go=sf;var cf={mode:"luv",toMode:{xyz50:_e,rgb:e=>F(_e(e))},fromMode:{xyz50:we,rgb:e=>we(W(e))},channels:["l","u","v","alpha"],parse:["--luv"],serialize:"--luv",ranges:{l:[0,100],u:[-84.936,175.042],v:[-125.882,87.243]},interpolate:{l:p,u:p,v:p,alpha:{use:p,fixup:y}}},Mo=cf;var hf=({r:e,g:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.cbrt(.41222147079999993*e+.5363325363*t+.0514459929*r),a=Math.cbrt(.2119034981999999*e+.6806995450999999*t+.1073969566*r),f=Math.cbrt(.08830246189999998*e+.2817188376*t+.6299787005000002*r),i={mode:"oklab",l:.2104542553*n+.793617785*a-.0040720468*f,a:1.9779984951*n-2.428592205*a+.4505937099*f,b:.0259040371*n+.7827717662*a-.808675766*f};return o!==void 0&&(i.alpha=o),i},Fe=hf;var bf=e=>{let t=Fe(j(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},ne=bf;var xf=({l:e,a:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Math.pow(e*.9999999984505198+.39633779217376786*t+.2158037580607588*r,3),a=Math.pow(e*1.0000000088817609-.10556134232365635*t-.06385417477170591*r,3),f=Math.pow(e*1.0000000546724108-.08948418209496575*t-1.2914855378640917*r,3),i={mode:"lrgb",r:4.076741661347994*n-3.307711590408193*a+.230969928729428*f,g:-1.2684380040921763*n+2.6097574006633715*a-.3413193963102197*f,b:-.004196086541837188*n-.7034186144594493*a+1.7076147009309444*f};return o!==void 0&&(i.alpha=o),i},U=xf;var gf=e=>Y(U(e)),ae=gf;function Tt(e){let o=1.170873786407767;return .5*(o*e-.206+Math.sqrt((o*e-.206)*(o*e-.206)+4*.03*o*e))}function He(e){return(e*e+.206*e)/(1.170873786407767*(e+.03))}function Mf(e,t){let r,o,n,a,f,i,l,s;-1.88170328*e-.80936493*t>1?(r=1.19086277,o=1.76576728,n=.59662641,a=.75515197,f=.56771245,i=4.0767416621,l=-3.3077115913,s=.2309699292):1.81444104*e-1.19445276*t>1?(r=.73956515,o=-.45954404,n=.08285427,a=.1254107,f=.14503204,i=-1.2684380046,l=2.6097574011,s=-.3413193965):(r=1.35733652,o=-.00915799,n=-1.1513021,a=-.50559606,f=.00692167,i=-.0041960863,l=-.7034186147,s=1.707614701);let u=r+o*e+n*t+a*e*e+f*e*t,m=.3963377774*e+.2158037573*t,h=-.1055613458*e-.0638541728*t,c=-.0894841775*e-1.291485548*t;{let b=1+u*m,M=1+u*h,g=1+u*c,z=b*b*b,L=M*M*M,S=g*g*g,C=3*m*b*b,$=3*h*M*M,O=3*c*g*g,J=6*m*m*b,he=6*h*h*M,le=6*c*c*g,Z=i*z+l*L+s*S,Q=i*C+l*$+s*O,be=i*J+l*he+s*le;u=u-Z*Q/(Q*Q-.5*Z*be)}return u}function vo(e,t){let r=Mf(e,t),o=U({l:1,a:r*e,b:r*t}),n=Math.cbrt(1/Math.max(o.r,o.g,o.b)),a=n*r;return[n,a]}function vf(e,t,r,o,n,a=null){a||(a=vo(e,t));let f;if((r-n)*a[1]-(a[0]-n)*o<=0)f=a[1]*n/(o*a[0]+a[1]*(n-r));else{f=a[1]*(n-1)/(o*(a[0]-1)+a[1]*(n-r));{let i=r-n,l=o,s=.3963377774*e+.2158037573*t,u=-.1055613458*e-.0638541728*t,m=-.0894841775*e-1.291485548*t,h=i+l*s,c=i+l*u,b=i+l*m;{let M=n*(1-f)+f*r,g=f*o,z=M+g*s,L=M+g*u,S=M+g*m,C=z*z*z,$=L*L*L,O=S*S*S,J=3*h*z*z,he=3*c*L*L,le=3*b*S*S,Z=6*h*h*z,Q=6*c*c*L,be=6*b*b*S,xe=4.0767416621*C-3.3077115913*$+.2309699292*O-1,E=4.0767416621*J-3.3077115913*he+.2309699292*le,$t=4.0767416621*Z-3.3077115913*Q+.2309699292*be,Nt=E/(E*E-.5*xe*$t),et=-xe*Nt,tt=-1.2684380046*C+2.6097574011*$-.3413193965*O-1,Pe=-1.2684380046*J+2.6097574011*he-.3413193965*le,Nr=-1.2684380046*Z+2.6097574011*Q-.3413193965*be,It=Pe/(Pe*Pe-.5*tt*Nr),rt=-tt*It,Wo=-.0041960863*C-.7034186147*$+1.707614701*O-1,Ir=-.0041960863*J-.7034186147*he+1.707614701*le,M0=-.0041960863*Z-.7034186147*Q+1.707614701*be,Uo=Ir/(Ir*Ir-.5*Wo*M0),Or=-Wo*Uo;et=Nt>=0?et:1e6,rt=It>=0?rt:1e6,Or=Uo>=0?Or:1e6,f+=Math.min(et,Math.min(rt,Or))}}}return f}function zt(e,t,r=null){r||(r=vo(e,t));let o=r[0],n=r[1];return[n/o,n/(1-o)]}function ur(e,t,r){let o=vo(t,r),n=vf(t,r,e,1,e,o),a=zt(t,r,o),f=.11516993+1/(7.4477897+4.1590124*r+t*(-2.19557347+1.75198401*r+t*(-2.13704948-10.02301043*r+t*(-4.24894561+5.38770819*r+4.69891013*t)))),i=.11239642+1/(1.6132032-.68124379*r+t*(.40370612+.90148123*r+t*(-.27087943+.6122399*r+t*(.00299215-.45399568*r-.14661872*t)))),l=n/Math.min(e*a[0],(1-e)*a[1]),s=e*f,u=(1-e)*i,m=.9*l*Math.sqrt(Math.sqrt(1/(1/(s*s*s*s)+1/(u*u*u*u))));return s=e*.4,u=(1-e)*.8,[Math.sqrt(1/(1/(s*s)+1/(u*u))),m,n]}function We(e){let t=e.l!==void 0?e.l:0,r=e.a!==void 0?e.a:0,o=e.b!==void 0?e.b:0,n={mode:"okhsl",l:Tt(t)};e.alpha!==void 0&&(n.alpha=e.alpha);let a=Math.sqrt(r*r+o*o);if(!a)return n.s=0,n;let[f,i,l]=ur(t,r/a,o/a),s;if(a<i){let u=0,m=.8*f,h=1-m/i;s=(a-u)/(m+h*(a-u))*.8}else{let u=i,m=.2*i*i*1.25*1.25/f,h=1-m/(l-i);s=.8+.2*((a-u)/(m+h*(a-u)))}return s&&(n.s=s,n.h=k(Math.atan2(o,r)*180/Math.PI)),n}function Ue(e){let t=e.h!==void 0?e.h:0,r=e.s!==void 0?e.s:0,o=e.l!==void 0?e.l:0,n={mode:"oklab",l:He(o)};if(e.alpha!==void 0&&(n.alpha=e.alpha),!r||o===1)return n.a=n.b=0,n;let a=Math.cos(t/180*Math.PI),f=Math.sin(t/180*Math.PI),[i,l,s]=ur(n.l,a,f),u,m,h,c;r<.8?(u=1.25*r,m=0,h=.8*i,c=1-h/l):(u=5*(r-.8),m=l,h=.2*l*l*1.25*1.25/i,c=1-h/(s-l));let b=m+u*h/(1-c*u);return n.a=b*a,n.b=b*f,n}var yf={...mt,mode:"okhsl",channels:["h","s","l","alpha"],parse:["--okhsl"],serialize:"--okhsl",fromMode:{oklab:We,rgb:e=>We(ne(e))},toMode:{oklab:Ue,rgb:e=>ae(Ue(e))}},yo=yf;function Ke(e){let t=e.l!==void 0?e.l:0,r=e.a!==void 0?e.a:0,o=e.b!==void 0?e.b:0,n=Math.sqrt(r*r+o*o),a=n?r/n:1,f=n?o/n:1,[i,l]=zt(a,f),s=.5,u=1-s/i,m=l/(n+t*l),h=m*t,c=m*n,b=He(h),M=c*b/h,g=U({l:b,a:a*M,b:f*M}),z=Math.cbrt(1/Math.max(g.r,g.g,g.b,0));t=t/z,n=n/z*Tt(t)/t,t=Tt(t);let L={mode:"okhsv",s:n?(s+l)*c/(l*s+l*u*c):0,v:t?t/h:0};return L.s&&(L.h=k(Math.atan2(o,r)*180/Math.PI)),e.alpha!==void 0&&(L.alpha=e.alpha),L}function Qe(e){let t={mode:"oklab"};e.alpha!==void 0&&(t.alpha=e.alpha);let r=e.h!==void 0?e.h:0,o=e.s!==void 0?e.s:0,n=e.v!==void 0?e.v:0,a=Math.cos(r/180*Math.PI),f=Math.sin(r/180*Math.PI),[i,l]=zt(a,f),s=.5,u=1-s/i,m=1-o*s/(s+l-l*u*o),h=o*l*s/(s+l-l*u*o),c=He(m),b=h*c/m,M=U({l:c,a:a*b,b:f*b}),g=Math.cbrt(1/Math.max(M.r,M.g,M.b,0)),z=He(n*m),L=h*z/m;return t.l=z*g,t.a=L*a*g,t.b=L*f*g,t}var Tf={...st,mode:"okhsv",channels:["h","s","v","alpha"],parse:["--okhsv"],serialize:"--okhsv",fromMode:{oklab:Ke,rgb:e=>Ke(ne(e))},toMode:{oklab:Qe,rgb:e=>ae(Qe(e))}},To=Tf;function zf(e,t){if(!t||t[0]!=="oklab")return;let r={mode:"oklab"},[,o,n,a,f]=t;if(!(o.type===d.Hue||n.type===d.Hue||a.type===d.Hue))return o.type!==d.None&&(r.l=Math.min(Math.max(0,o.type===d.Number?o.value:o.value/100),1)),n.type!==d.None&&(r.a=n.type===d.Number?n.value:n.value*.4/100),a.type!==d.None&&(r.b=a.type===d.Number?a.value:a.value*.4/100),f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var mr=zf;var kf={...Le,mode:"oklab",toMode:{lrgb:U,rgb:ae},fromMode:{lrgb:Fe,rgb:ne},ranges:{l:[0,1],a:[-.4,.4],b:[-.4,.4]},parse:[mr],serialize:e=>`oklab(${e.l!==void 0?e.l:"none"} ${e.a!==void 0?e.a:"none"} ${e.b!==void 0?e.b:"none"}${e.alpha<1?` / ${e.alpha}`:""})`},zo=kf;function Lf(e,t){if(!t||t[0]!=="oklch")return;let r={mode:"oklch"},[,o,n,a,f]=t;if(o.type!==d.None){if(o.type===d.Hue)return;r.l=Math.min(Math.max(0,o.type===d.Number?o.value:o.value/100),1)}if(n.type!==d.None&&(r.c=Math.max(0,n.type===d.Number?n.value:n.value*.4/100)),a.type!==d.None){if(a.type===d.Percentage)return;r.h=a.value}return f.type!==d.None&&(r.alpha=Math.min(1,Math.max(0,f.type===d.Number?f.value:f.value/100))),r}var sr=Lf;var Rf={...Re,mode:"oklch",toMode:{oklab:e=>D(e,"oklab"),rgb:e=>ae(D(e,"oklab"))},fromMode:{rgb:e=>q(ne(e),"oklch"),oklab:e=>q(e,"oklch")},parse:[sr],serialize:e=>`oklch(${e.l!==void 0?e.l:"none"} ${e.c!==void 0?e.c:"none"} ${e.h!==void 0?e.h:"none"}${e.alpha<1?` / ${e.alpha}`:""})`,ranges:{l:[0,1],c:[0,.4],h:[0,360]}},ko=Rf;var wf=e=>{let{r:t,g:r,b:o,alpha:n}=j(e),a={mode:"xyz65",x:.486570948648216*t+.265667693169093*r+.1982172852343625*o,y:.2289745640697487*t+.6917385218365062*r+.079286914093745*o,z:0*t+.0451133818589026*r+1.043944368900976*o};return n!==void 0&&(a.alpha=n),a},kt=wf;var _f=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Y({r:e*2.4934969119414263-t*.9313836179191242-.402710784450717*r,g:e*-.8294889695615749+t*1.7626640603183465+.0236246858419436*r,b:e*.0358458302437845-t*.0761723892680418+.9568845240076871*r},"p3");return o!==void 0&&(n.alpha=o),n},Lt=_f;var Hf={...G,mode:"p3",parse:["display-p3"],serialize:"display-p3",fromMode:{rgb:e=>Lt(N(e)),xyz65:Lt},toMode:{rgb:e=>I(kt(e)),xyz65:kt}},Lo=Hf;var Ro=e=>{let t=Math.abs(e);return t>=.001953125?Math.sign(e)*Math.pow(t,.5555555555555556):16*e},Xf=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n={mode:"prophoto",r:Ro(e*1.3457868816471585-t*.2555720873797946-.0511018649755453*r),g:Ro(e*-.5446307051249019+t*1.5082477428451466+.0205274474364214*r),b:Ro(e*0+t*0+1.2119675456389452*r)};return o!==void 0&&(n.alpha=o),n},Rt=Xf;var wo=(e=0)=>{let t=Math.abs(e);return t>=.03125?Math.sign(e)*Math.pow(t,1.8):e/16},Sf=e=>{let t=wo(e.r),r=wo(e.g),o=wo(e.b),n={mode:"xyz50",x:.7977666449006423*t+.1351812974005331*r+.0313477341283922*o,y:.2880748288194013*t+.7118352342418731*r+899369387256e-16*o,z:0*t+0*r+.8251046025104602*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},wt=Sf;var Pf={...G,mode:"prophoto",parse:["prophoto-rgb"],serialize:"prophoto-rgb",fromMode:{xyz50:Rt,rgb:e=>Rt(W(e))},toMode:{xyz50:wt,rgb:e=>F(wt(e))}},_o=Pf;var wn=1.09929682680944,Cf=.018053968510807,Ho=e=>{let t=Math.abs(e);return t>Cf?(Math.sign(e)||1)*(wn*Math.pow(t,.45)-(wn-1)):4.5*e},$f=({x:e,y:t,z:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n={mode:"rec2020",r:Ho(e*1.7166511879712683-t*.3556707837763925-.2533662813736599*r),g:Ho(e*-.6666843518324893+t*1.6164812366349395+.0157685458139111*r),b:Ho(e*.0176398574453108-t*.0427706132578085+.9421031212354739*r)};return o!==void 0&&(n.alpha=o),n},_t=$f;var _n=1.09929682680944,Nf=.018053968510807,Xo=(e=0)=>{let t=Math.abs(e);return t<Nf*4.5?e/4.5:(Math.sign(e)||1)*Math.pow((t+_n-1)/_n,1/.45)},If=e=>{let t=Xo(e.r),r=Xo(e.g),o=Xo(e.b),n={mode:"xyz65",x:.6369580483012911*t+.1446169035862083*r+.1688809751641721*o,y:.262700212011267*t+.6779980715188708*r+.059301716469862*o,z:0*t+.0280726930490874*r+1.0609850577107909*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},Ht=If;var Of={...G,mode:"rec2020",fromMode:{xyz65:_t,rgb:e=>_t(N(e))},toMode:{xyz65:Ht,rgb:e=>I(Ht(e))},parse:["rec2020"],serialize:"rec2020"},So=Of;var ie=.0037930732552754493,cr=Math.cbrt(ie);var Po=e=>Math.cbrt(e)-cr,Af=e=>{let{r:t,g:r,b:o,alpha:n}=j(e),a=Po(.3*t+.622*r+.078*o+ie),f=Po(.23*t+.692*r+.078*o+ie),i=Po(.2434226892454782*t+.2047674442449682*r+.5518098665095535*o+ie),l={mode:"xyb",x:(a-f)/2,y:(a+f)/2,b:i-(a+f)/2};return n!==void 0&&(l.alpha=n),l},hr=Af;var Co=e=>Math.pow(e+cr,3),qf=({x:e,y:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n=Co(e+t)-ie,a=Co(t-e)-ie,f=Co(r+t)-ie,i=Y({r:11.031566904639861*n-9.866943908131562*a-.16462299650829934*f,g:-3.2541473810744237*n+4.418770377582723*a-.16462299650829934*f,b:-3.6588512867136815*n+2.7129230459360922*a+1.9459282407775895*f});return o!==void 0&&(i.alpha=o),i},br=qf;var Df={mode:"xyb",channels:["x","y","b","alpha"],parse:["--xyb"],serialize:"--xyb",toMode:{rgb:br},fromMode:{rgb:hr},ranges:{x:[-.0154,.0281],y:[0,.8453],b:[-.2778,.388]},interpolate:{x:p,y:p,b:p,alpha:{use:p,fixup:y}}},$o=Df;var Jf={mode:"xyz50",parse:["xyz-d50"],serialize:"xyz-d50",toMode:{rgb:F,lab:se},fromMode:{rgb:W,lab:me},channels:["x","y","z","alpha"],ranges:{x:[0,.964],y:[0,.999],z:[0,.825]},interpolate:{x:p,y:p,z:p,alpha:{use:p,fixup:y}}},No=Jf;var Ef=e=>{let{x:t,y:r,z:o,alpha:n}=e;t===void 0&&(t=0),r===void 0&&(r=0),o===void 0&&(o=0);let a={mode:"xyz50",x:1.0479298208405488*t+.0229467933410191*r-.0501922295431356*o,y:.0296278156881593*t+.990434484573249*r-.0170738250293851*o,z:-.0092430581525912*t+.0150551448965779*r+.7518742899580008*o};return n!==void 0&&(a.alpha=n),a},xr=Ef;var jf=e=>{let{x:t,y:r,z:o,alpha:n}=e;t===void 0&&(t=0),r===void 0&&(r=0),o===void 0&&(o=0);let a={mode:"xyz65",x:.9554734527042182*t-.0230985368742614*r+.0632593086610217*o,y:-.0283697069632081*t+1.0099954580058226*r+.021041398966943*o,z:.0123140016883199*t-.0205076964334779*r+1.3303659366080753*o};return n!==void 0&&(a.alpha=n),a},gr=jf;var Yf={mode:"xyz65",toMode:{rgb:I,xyz50:xr},fromMode:{rgb:N,xyz50:gr},ranges:{x:[0,.95],y:[0,1],z:[0,1.088]},channels:["x","y","z","alpha"],parse:["xyz","xyz-d65"],serialize:"xyz-d65",interpolate:{x:p,y:p,z:p,alpha:{use:p,fixup:y}}},Io=Yf;var Gf=({r:e,g:t,b:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n={mode:"yiq",y:.29889531*e+.58662247*t+.11448223*r,i:.59597799*e-.2741761*t-.32180189*r,q:.21147017*e-.52261711*t+.31114694*r};return o!==void 0&&(n.alpha=o),n},Mr=Gf;var Bf=({y:e,i:t,q:r,alpha:o})=>{e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0);let n={mode:"rgb",r:e+.95608445*t+.6208885*r,g:e-.27137664*t-.6486059*r,b:e-1.10561724*t+1.70250126*r};return o!==void 0&&(n.alpha=o),n},vr=Bf;var Zf={mode:"yiq",toMode:{rgb:vr},fromMode:{rgb:Mr},channels:["y","i","q","alpha"],parse:["--yiq"],serialize:"--yiq",ranges:{i:[-.595,.595],q:[-.522,.522]},interpolate:{y:p,i:p,q:p,alpha:{use:p,fixup:y}}},Oo=Zf;var Ff=(e,t)=>Math.round(e*(t=Math.pow(10,t)))/t,Wf=(e=4)=>t=>typeof t=="number"?Ff(t,e):t,yr=Wf;var Xt=yr(2),St=e=>Math.max(0,Math.min(1,e||0)),Xe=e=>Math.round(St(e)*255),Ao=v("rgb"),Uf=v("hsl"),Tr=e=>{if(e===void 0)return;let t=Xe(e.r),r=Xe(e.g),o=Xe(e.b);return"#"+(1<<24|t<<16|r<<8|o).toString(16).slice(1)},qo=e=>{if(e===void 0)return;let t=Xe(e.alpha!==void 0?e.alpha:1);return Tr(e)+(256|t).toString(16).slice(1)},Do=e=>{if(e===void 0)return;let t=Xe(e.r),r=Xe(e.g),o=Xe(e.b);return e.alpha===void 0||e.alpha===1?`rgb(${t}, ${r}, ${o})`:`rgba(${t}, ${r}, ${o}, ${Xt(St(e.alpha))})`},Jo=e=>{if(e===void 0)return;let t=Xt(e.h||0),r=Xt(St(e.s)*100)+"%",o=Xt(St(e.l)*100)+"%";return e.alpha===void 0||e.alpha===1?`hsl(${t}, ${r}, ${o})`:`hsla(${t}, ${r}, ${o}, ${Xt(St(e.alpha))})`},Hn=e=>{let t=P(e);if(!t)return;let r=R(t.mode);if(!r.serialize||typeof r.serialize=="string"){let o=`color(${r.serialize||`--${t.mode}`} `;return r.channels.forEach((n,a)=>{n!=="alpha"&&(o+=(a?" ":"")+(t[n]!==void 0?t[n]:"none"))}),t.alpha!==void 0&&t.alpha<1&&(o+=` / ${t.alpha}`),o+")"}if(typeof r.serialize=="function")return r.serialize(t)},Xn=e=>Tr(Ao(e)),Sn=e=>qo(Ao(e)),Pn=e=>Do(Ao(e)),Cn=e=>Jo(Uf(e));var Kf={normal:(e,t)=>t,multiply:(e,t)=>e*t,screen:(e,t)=>e+t-e*t,"hard-light":(e,t)=>t<.5?e*2*t:2*t*(1-e)-1,overlay:(e,t)=>e<.5?t*2*e:2*e*(1-t)-1,darken:(e,t)=>Math.min(e,t),lighten:(e,t)=>Math.max(e,t),"color-dodge":(e,t)=>e===0?0:t===1?1:Math.min(1,e/(1-t)),"color-burn":(e,t)=>e===1?1:t===0?0:1-Math.min(1,(1-e)/t),"soft-light":(e,t)=>t<.5?e-(1-2*t)*e*(1-e):e+(2*t-1)*((e<.25?((16*e-12)*e+4)*e:Math.sqrt(e))-e),difference:(e,t)=>Math.abs(e-t),exclusion:(e,t)=>e+t-2*e*t},Qf=(e,t="normal",r="rgb")=>{let o=typeof t=="function"?t:Kf[t],n=v(r),a=R(r).channels;return e.map(i=>{let l=n(i);return l.alpha===void 0&&(l.alpha=1),l}).reduce((i,l)=>{if(i===void 0)return l;let s=l.alpha+i.alpha*(1-l.alpha);return a.reduce((u,m)=>(m!=="alpha"&&(s===0?u[m]=0:(u[m]=l.alpha*(1-i.alpha)*l[m]+l.alpha*i.alpha*o(i[m],l[m])+(1-l.alpha)*i.alpha*i[m],u[m]=Math.max(0,Math.min(1,u[m]/s)))),u),{mode:r,alpha:s})})},$n=Qf;var Vf=([e,t])=>e+Math.random()*(t-e),ei=e=>Object.keys(e).reduce((t,r)=>{let o=e[r];return t[r]=Array.isArray(o)?o:[o,o],t},{}),ti=(e="rgb",t={})=>{let r=R(e),o=ei(t);return r.channels.reduce((n,a)=>((o.alpha||a!=="alpha")&&(n[a]=Vf(o[a]||r.ranges[a])),n),{mode:e})},Nn=ti;var ce=(e,t="rgb",r=!1)=>{let o=t?R(t).channels:null,n=t?v(t):P;return a=>{let f=n(a);if(!f)return;let i=(o||R(f.mode).channels).reduce((s,u)=>{let m=e(f[u],u,f,t);return m!==void 0&&!isNaN(m)&&(s[u]=m),s},{mode:f.mode});if(!r)return i;let l=P(a);return l&&l.mode!==i.mode?v(l.mode)(i):i}},zr=(e,t,r)=>t!=="alpha"?(e||0)*(r.alpha!==void 0?r.alpha:1):e,kr=(e,t,r)=>t!=="alpha"&&r.alpha!==0?(e||0)/(r.alpha!==void 0?r.alpha:1):e,Pt=(e=1,t=0)=>(r,o)=>o!=="alpha"?r*e+t:r,In=(e=1,t=1,r=0)=>(o,n)=>n!=="alpha"?e*Math.pow(o,t)+r:o;var ri=e=>{e[0]===void 0&&(e[0]=0),e[e.length-1]===void 0&&(e[e.length-1]=1);let t=1,r,o,n,a;for(;t<e.length;){if(e[t]===void 0){for(o=t,n=e[t-1],r=t;e[r]===void 0;)r++;for(a=(e[r]-n)/(r-t+1);t<r;)e[t]=n+(t+1-o)*a,t++}else e[t]<e[t-1]&&(e[t]=e[t-1]);t++}return e},On=ri;var oi=(e=.5)=>t=>e<=0?1:e>=1?0:Math.pow(t,Math.log(.5)/Math.log(e)),Lr=oi;var Rr=e=>typeof e=="function",Se=e=>e&&typeof e=="object",An=e=>typeof e=="number",qn=(e,t="rgb",r,o)=>{let n=R(t),a=v(t),f=[],i=[],l={};e.forEach(h=>{Array.isArray(h)?(f.push(a(h[0])),i.push(h[1])):An(h)||Rr(h)?l[i.length]=h:(f.push(a(h)),i.push(void 0))}),On(i);let s=n.channels.reduce((h,c)=>{let b;return Se(r)&&Se(r[c])&&r[c].fixup?b=r[c].fixup:Se(n.interpolate[c])&&n.interpolate[c].fixup?b=n.interpolate[c].fixup:b=M=>M,h[c]=b(f.map(M=>M[c])),h},{});if(o){let h=f.map((c,b)=>n.channels.reduce((M,g)=>(M[g]=s[g][b],M),{mode:t}));s=n.channels.reduce((c,b)=>(c[b]=h.map(M=>{let g=o(M[b],b,M,t);return isNaN(g)?void 0:g}),c),{})}let u=n.channels.reduce((h,c)=>{let b;return Rr(r)?b=r:Se(r)&&Rr(r[c])?b=r[c]:Se(r)&&Se(r[c])&&r[c].use?b=r[c].use:Rr(n.interpolate[c])?b=n.interpolate[c]:Se(n.interpolate[c])&&(b=n.interpolate[c].use),h[c]=b(s[c]),h},{}),m=f.length-1;return h=>{if(h=Math.min(Math.max(0,h),1),h<=i[0])return f[0];if(h>i[m])return f[m];let c=0;for(;i[c]<h;)c++;let b=i[c-1],M=i[c]-b,g=(h-b)/M,z=l[c]||l[0];z!==void 0&&(An(z)&&(z=Lr((z-b)/M)),g=z(g));let L=(c-1+g)/m;return n.channels.reduce((S,C)=>{let $=u[C](L);return $!==void 0&&(S[C]=$),S},{mode:t})}},Dn=(e,t="rgb",r)=>qn(e,t,r),Eo=(e,t)=>(r,o="rgb",n)=>{let a=t?ce(t,o):void 0,f=qn(r,o,n,e);return a?i=>a(f(i)):f},Jn=Eo(zr,kr);var wr=(e,t)=>(e+t)%t,En=(e,t,r,o,n)=>{let a=n*n,f=a*n;return((1-3*n+3*a-f)*e+(4-6*a+3*f)*t+(1+3*n+3*a-3*f)*r+f*o)/6},_r=e=>t=>{let r=e.length-1,o=t>=1?r-1:Math.max(0,Math.floor(t*r));return En(o>0?e[o-1]:2*e[o]-e[o+1],e[o],e[o+1],o<r-1?e[o+2]:2*e[o+1]-e[o],(t-o/r)*r)},Hr=e=>t=>{let r=e.length-1,o=Math.floor(t*r);return En(e[wr(o-1,e.length)],e[wr(o,e.length)],e[wr(o+1,e.length)],e[wr(o+2,e.length)],(t-o/r)*r)};var jn=e=>{let t,r=e.length-1,o=new Array(r),n=new Array(r),a=new Array(r);for(o[1]=1/4,n[1]=(6*e[1]-e[0])/4,t=2;t<r;++t)o[t]=1/(4-o[t-1]),n[t]=(6*e[t]-(t==r-1?e[r]:0)-n[t-1])*o[t];for(a[0]=e[0],a[r]=e[r],r-1>0&&(a[r-1]=n[r-1]),t=r-2;t>0;--t)a[t]=n[t]-o[t]*a[t+1];return a},Yn=e=>_r(jn(e)),Gn=e=>Hr(jn(e));var Ve=Math.sign,jo=Math.min,B=Math.abs,Yo=e=>{let t=e.length-1,r=[],o=[],n=[];for(let a=0;a<t;a++)r.push((e[a+1]-e[a])*t),o.push(a>0?.5*(e[a+1]-e[a-1])*t:void 0),n.push(a>0?(Ve(r[a-1])+Ve(r[a]))*jo(B(r[a-1]),B(r[a]),.5*B(o[a])):void 0);return[r,o,n]},Go=(e,t,r)=>{let o=e.length-1,n=o*o;return a=>{let f;a>=1?f=o-1:f=Math.max(0,Math.floor(a*o));let i=a-f/o,l=i*i,s=l*i;return(t[f]+t[f+1]-2*r[f])*n*s+(3*r[f]-2*t[f]-t[f+1])*o*l+t[f]*i+e[f]}},Bn=e=>{if(e.length<3)return p(e);let t=e.length-1,[r,,o]=Yo(e);return o[0]=r[0],o[t]=r[t-1],Go(e,o,r)},Zn=e=>{if(e.length<3)return p(e);let t=e.length-1,[r,o,n]=Yo(e);return o[0]=(e[1]*2-e[0]*1.5-e[2]*.5)*t,o[t]=(e[t]*1.5-e[t-1]*2+e[t-2]*.5)*t,n[0]=o[0]*r[0]<=0?0:B(o[0])>2*B(r[0])?2*r[0]:o[0],n[t]=o[t]*r[t-1]<=0?0:B(o[t])>2*B(r[t-1])?2*r[t-1]:o[t],Go(e,n,r)},Fn=e=>{let t=e.length-1,[r,o,n]=Yo(e);o[0]=.5*(e[1]-e[t])*t,o[t]=.5*(e[0]-e[t-1])*t;let a=(e[0]-e[t])*t,f=a;return n[0]=(Ve(a)+Ve(r[0]))*jo(B(a),B(r[0]),.5*B(o[0])),n[t]=(Ve(r[t-1])+Ve(f))*jo(B(r[t-1]),B(f),.5*B(o[t])),Go(e,n,r)};var ni=(e=1)=>e===1?t=>t:t=>Math.pow(t,e),Xr=ni;var ai=(e=2,t=1)=>{let r=Xr(t);if(e<2)return e<1?[]:[r(.5)];let o=[];for(let n=0;n<e;n++)o.push(r(n/(e-1)));return o},Wn=ai;var Un=v("rgb"),Kn=e=>{let t={mode:e.mode,r:Math.max(0,Math.min(e.r!==void 0?e.r:0,1)),g:Math.max(0,Math.min(e.g!==void 0?e.g:0,1)),b:Math.max(0,Math.min(e.b!==void 0?e.b:0,1))};return e.alpha!==void 0&&(t.alpha=e.alpha),t},Qn=e=>Kn(Un(e)),Vn=e=>e!==void 0&&(e.r===void 0||e.r>=0&&e.r<=1)&&(e.g===void 0||e.g>=0&&e.g<=1)&&(e.b===void 0||e.b>=0&&e.b<=1);function Sr(e){return Vn(Un(e))}function Ct(e="rgb"){let{gamut:t}=R(e);if(!t)return o=>!0;let r=v(typeof t=="string"?t:e);return o=>Vn(r(o))}function e0(e){return e=P(e),e===void 0||Sr(e)?e:v(e.mode)(Qn(e))}function Pr(e="rgb"){let{gamut:t}=R(e);if(!t)return a=>P(a);let r=typeof t=="string"?t:e,o=v(r),n=Ct(r);return a=>{let f=P(a);if(!f)return;let i=o(f);if(n(i))return f;let l=Kn(i);return f.mode===l.mode?l:v(f.mode)(l)}}function t0(e,t="lch",r="rgb"){e=P(e);let o=r==="rgb"?Sr:Ct(r),n=r==="rgb"?Qn:Pr(r);if(e===void 0||o(e))return e;let a=v(e.mode);e=v(t)(e);let f={...e,c:0};if(!o(f))return a(n(f));let i=0,l=e.c!==void 0?e.c:0,s=R(t).ranges.c,u=(s[1]-s[0])/Math.pow(2,13),m=f.c;for(;l-i>u;)f.c=i+(l-i)*.5,o(f)?(m=f.c,i=f.c):l=f.c;return a(o(f)?f:{...f,c:m})}function r0(e="rgb",t="oklch",r=fe("oklch"),o=.02){let n=v(e),a=R(e);if(!a.gamut)return u=>n(u);let f=Ct(e),i=Pr(e),l=v(t),{ranges:s}=R(t);return u=>{if(u=P(u),u===void 0)return;let m={...l(u)};if(m.l===void 0&&(m.l=0),m.c===void 0&&(m.c=0),m.l>=s.l[1]){let g={...a.white,mode:e};return u.alpha!==void 0&&(g.alpha=u.alpha),g}if(m.l<=s.l[0]){let g={...a.black,mode:e};return u.alpha!==void 0&&(g.alpha=u.alpha),g}if(f(m))return n(m);let h=0,c=m.c,b=(s.c[1]-s.c[0])/4e3,M=i(m);for(;c-h>b;)m.c=(h+c)*.5,M=i(m),f(m)||r&&o>0&&r(m,M)<=o?h=m.c:c=m.c;return n(f(m)?m:M)}}var fi=(e,t=fe(),r=o=>o)=>{let o=e.map((n,a)=>({color:r(n),i:a}));return(n,a=1,f=1/0)=>(isFinite(a)&&(a=Math.max(1,Math.min(a,o.length-1))),o.forEach(i=>{i.d=t(n,i.color)}),o.sort((i,l)=>i.d-l.d).slice(0,a).filter(i=>i.d<f).map(i=>e[i.i]))},o0=fi;var Bo=e=>Math.max(e,0),Zo=e=>Math.max(Math.min(e,1),0),ii=(e,t,r)=>e===void 0||t===void 0?void 0:e+r*(t-e),li=e=>{let t=1-Zo(e);return[.393+.607*t,.769-.769*t,.189-.189*t,0,.349-.349*t,.686+.314*t,.168-.168*t,0,.272-.272*t,.534-.534*t,.131+.869*t,0,0,0,0,1]},pi=e=>{let t=Bo(e);return[.213+.787*t,.715-.715*t,.072-.072*t,0,.213-.213*t,.715+.285*t,.072-.072*t,0,.213-.213*t,.715-.715*t,.072+.928*t,0,0,0,0,1]},di=e=>{let t=1-Zo(e);return[.2126+.7874*t,.7152-.7152*t,.0722-.0722*t,0,.2126-.2126*t,.7152+.2848*t,.0722-.0722*t,0,.2126-.2126*t,.7152-.7152*t,.0722+.9278*t,0,0,0,0,1]},ui=e=>{let t=Math.PI*e/180,r=Math.cos(t),o=Math.sin(t);return[.213+r*.787-o*.213,.715-r*.715-o*.715,.072-r*.072+o*.928,0,.213-r*.213+o*.143,.715+r*.285+o*.14,.072-r*.072-o*.283,0,.213-r*.213-o*.787,.715-r*.715+o*.715,.072+r*.928+o*.072,0,0,0,0,1]},Cr=(e,t,r=!1)=>{let o=v(t),n=R(t).channels;return a=>{let f=o(a);if(!f)return;let i={mode:t},l,s=n.length;for(let m=0;m<e.length;m++)l=n[Math.floor(m/s)],f[l]!==void 0&&(i[l]=(i[l]||0)+e[m]*(f[n[m%s]]||0));if(!r)return i;let u=P(a);return u&&i.mode!==u.mode?v(u.mode)(i):i}},n0=(e=1,t="rgb")=>{let r=Bo(e);return ce(Pt(r),t,!0)},a0=(e=1,t="rgb")=>{let r=Bo(e);return ce(Pt(r,(1-r)/2),t,!0)},f0=(e=1,t="rgb")=>Cr(li(e),t,!0),i0=(e=1,t="rgb")=>Cr(pi(e),t,!0),l0=(e=1,t="rgb")=>Cr(di(e),t,!0),p0=(e=1,t="rgb")=>{let r=Zo(e);return ce((o,n)=>n==="alpha"?o:ii(r,1-r,o),t,!0)},d0=(e=0,t="rgb")=>Cr(ui(e),t,!0);var mi=v("rgb"),si=[[1,0,-0,0,1,0,-0,-0,1],[.856167,.182038,-.038205,.029342,.955115,.015544,-.00288,-.001563,1.004443],[.734766,.334872,-.069637,.05184,.919198,.028963,-.004928,-.004209,1.009137],[.630323,.465641,-.095964,.069181,.890046,.040773,-.006308,-.007724,1.014032],[.539009,.579343,-.118352,.082546,.866121,.051332,-.007136,-.011959,1.019095],[.458064,.679578,-.137642,.092785,.846313,.060902,-.007494,-.016807,1.024301],[.38545,.769005,-.154455,.100526,.829802,.069673,-.007442,-.02219,1.029632],[.319627,.849633,-.169261,.106241,.815969,.07779,-.007025,-.028051,1.035076],[.259411,.923008,-.18242,.110296,.80434,.085364,-.006276,-.034346,1.040622],[.203876,.990338,-.194214,.112975,.794542,.092483,-.005222,-.041043,1.046265],[.152286,1.052583,-.204868,.114503,.786281,.099216,-.003882,-.048116,1.051998]],ci=[[1,0,-0,0,1,0,-0,-0,1],[.866435,.177704,-.044139,.049567,.939063,.01137,-.003453,.007233,.99622],[.760729,.319078,-.079807,.090568,.889315,.020117,-.006027,.013325,.992702],[.675425,.43385,-.109275,.125303,.847755,.026942,-.00795,.018572,.989378],[.605511,.52856,-.134071,.155318,.812366,.032316,-.009376,.023176,.9862],[.547494,.607765,-.155259,.181692,.781742,.036566,-.01041,.027275,.983136],[.498864,.674741,-.173604,.205199,.754872,.039929,-.011131,.030969,.980162],[.457771,.731899,-.18967,.226409,.731012,.042579,-.011595,.034333,.977261],[.422823,.781057,-.203881,.245752,.709602,.044646,-.011843,.037423,.974421],[.392952,.82361,-.216562,.263559,.69021,.046232,-.01191,.040281,.97163],[.367322,.860646,-.227968,.280085,.672501,.047413,-.01182,.04294,.968881]],hi=[[1,0,-0,0,1,0,-0,-0,1],[.92667,.092514,-.019184,.021191,.964503,.014306,.008437,.054813,.93675],[.89572,.13333,-.02905,.029997,.9454,.024603,.013027,.104707,.882266],[.905871,.127791,-.033662,.026856,.941251,.031893,.01341,.148296,.838294],[.948035,.08949,-.037526,.014364,.946792,.038844,.010853,.193991,.795156],[1.017277,.027029,-.044306,-.006113,.958479,.047634,.006379,.248708,.744913],[1.104996,-.046633,-.058363,-.032137,.971635,.060503,.001336,.317922,.680742],[1.193214,-.109812,-.083402,-.058496,.97941,.079086,-.002346,.403492,.598854],[1.257728,-.139648,-.118081,-.078003,.975409,.102594,-.003316,.501214,.502102],[1.278864,-.125333,-.153531,-.084748,.957674,.127074,-989e-6,.601151,.399838],[1.255528,-.076749,-.178779,-.078411,.930809,.147602,.004733,.691367,.3039]],Fo=(e,t)=>{let r=Math.max(0,Math.min(1,t)),o=Math.round(r/.1),n=Math.round(r%.1),a=e[o];if(n>0&&o<e.length-1){let f=e[o+1];a=a.map((i,l)=>V(a[l],f[l],n))}return f=>{let i=P(f);if(i===void 0)return;let{r:l,g:s,b:u}=mi(i),m={mode:"rgb",r:a[0]*l+a[1]*s+a[2]*u,g:a[3]*l+a[4]*s+a[5]*u,b:a[6]*l+a[7]*s+a[8]*u};return i.alpha!==void 0&&(m.alpha=i.alpha),v(i.mode)(m)}},u0=(e=1)=>Fo(si,e),m0=(e=1)=>Fo(ci,e),s0=(e=1)=>Fo(hi,e);var c0=e=>e*e*(3-2*e),h0=e=>.5-Math.sin(Math.asin(1-2*e)/3);var bi=e=>e*e*e*(e*(e*6-15)+10),b0=bi;var xi=e=>(1-Math.cos(e*Math.PI))/2,x0=xi;function $r(e){let t=v("lrgb")(e);return .2126*t.r+.7152*t.g+.0722*t.b}function g0(e,t){let r=$r(e),o=$r(t);return(Math.max(r,o)+.05)/(Math.min(r,o)+.05)}var gi=T(Br),Mi=T(Wr),vi=T(Vr),yi=T(eo),Ti=T(to),zi=T(mt),ki=T(st),Li=T(ro),Ri=T(ao),wi=T(uo),_i=T(mo),Hi=T(Le),Xi=T(ho),Si=T(Re),Pi=T(bo),Ci=T(xo),$i=T(go),Ni=T(Mo),Ii=T(yo),Oi=T(To),Ai=T(zo),qi=T(ko),Di=T(Lo),Ji=T(_o),Ei=T(So),ji=T(G),Yi=T($o),Gi=T(No),Bi=T(Io),Zi=T(Oo);return L0(Fi);})();
